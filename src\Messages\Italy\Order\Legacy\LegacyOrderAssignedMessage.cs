﻿using commercetools.Sdk.Api.Models.Products;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderAssignedMessage : BaseMessage<LegacyOrderAssignedPayload>, IMessageKey, IDistributedTracing
            {

                public string GetMessageKey()
                    => Guid.NewGuid().ToString(); //Payload?.OrderIdentifier + "-" + Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
                public LegacyOrderAssignedPayload ConvertPayload(BaseOrderPayload payload, string ctOrderId)
                {

                    return new LegacyOrderAssignedPayload
                    {
                        EventDate = payload.EventDate,
                        EventID = payload.EventID,
                        FloristIdentifier = payload.FloristId,
                        OrderIdentifier = ctOrderId,
                        ToBeAcceptedBefore = null,
                        DeliveryAmount = payload.Delivery.TripCost.HasValue ? Convert.ToDecimal(payload.Delivery.TripCost.Value) : 0,
                        Products = payload.Products?.Select(product => (LegacyOrderAssignedProduct)(LegacyProduct)product).ToList() ?? new()


                    };
                    
                }

            }
        }
    }

    public class LegacyOrderAssignedPayload : LegacyPayload, IEquatable<LegacyOrderAssignedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string OrderIdentifier { get; set; }
        public DateTime? ToBeAcceptedBefore { get; set; }
        public decimal? DeliveryAmount { get; set; } = null;
        public List<LegacyOrderAssignedProduct> Products { get; set; } = new();
        public bool Equals(LegacyOrderAssignedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                OrderIdentifier == parameter.OrderIdentifier &&
                DeliveryAmount == parameter.DeliveryAmount &&
                ToBeAcceptedBefore == parameter.ToBeAcceptedBefore &&
                Products == parameter.Products
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderAssignedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            OrderIdentifier,
            DeliveryAmount,
            ToBeAcceptedBefore,
            Products
        }.GetHashCode();
    }

    public class LegacyOrderAssignedProduct
    {
        public string ProductKey { get; set; }
        public string VariantKey { get; set; }
        public decimal? ExecutorAmount { get; set; } = null;

        public static implicit operator LegacyOrderAssignedProduct(LegacyProduct legacyProduct)
        {
            return new LegacyOrderAssignedProduct
            {
                ProductKey = legacyProduct.ProductKey,
                VariantKey = legacyProduct.VariantKey,
                ExecutorAmount = legacyProduct.ExecutingFloristAmount
            };
        }
    }
}
