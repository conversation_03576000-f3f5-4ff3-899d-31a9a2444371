﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Framework
{
    public static class ApplicationBuilderHelper
    {
        public static void WarmUp<T>(this IApplicationBuilder app, Func<T, Task> warmUpAction = null)
        {
            var repo = app.ApplicationServices.GetService<T>();
            if (repo != null && warmUpAction != null)
            {
                var task = warmUpAction.Invoke(repo);
                task.Wait();
            }
        }

        public static void WarmUp<T>(this IApplicationBuilder app, Action<T> warmUpAction = null)
        {
            var repo = app.ApplicationServices.GetService<T>();
            if (repo != null && warmUpAction != null)
            {
                warmUpAction.Invoke(repo);
            }
        }
    }
}
