﻿namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderNotesUpdated
    {
        public string OrderIdentifier { get; set; }
        public string Notes { get; set; }

        public static implicit operator GlobalOrderNotesUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderNotesUpdatedMessage v)
        {
            return new GlobalOrderNotesUpdated
            {
                Notes = v?.Payload?.Notes,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
