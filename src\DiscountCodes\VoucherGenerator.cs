﻿using System;
using System.Linq;
using System.Text;

namespace IT.SharedLibraries.CT.DiscountCodes
{
    public static class VoucherGenerator
    {
        public static string GenerateVoucherCode()
        {
            StringBuilder voucherCodeBuilder = new();
            string flatGuid = Guid.NewGuid().ToString().ToUpperInvariant().Replace("-", "");
            Random random = new();
            char[] chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".ToArray();

            for (int i = 0; i < 8; i++)
            {
                voucherCodeBuilder.Append(chars[random.Next(0, chars.Length)]);
            }
            voucherCodeBuilder.Append('-');
            voucherCodeBuilder.Append(flatGuid.AsSpan(0, 5));

            return voucherCodeBuilder.ToString();
        }
    }
}
