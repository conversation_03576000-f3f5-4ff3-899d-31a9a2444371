﻿using ITF.Lib.Common.Notifications.Messages;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{

    public class FloristFreshPortalPayload : IPayload
    {
        public string FloristId { get; set; }
        public string Group { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string PostalCode { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string Vat_number { get; set; }
        public string Comment { get; set; }
        public DateTime AgreementStopDate { get; set; }
        public string Currency_code { get; set; }
        public List<FloristContact> FloristContacts { get; set; } = new List<FloristContact>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

   
}
