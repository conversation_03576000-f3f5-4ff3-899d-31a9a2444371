﻿using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;
public class DeliveryAddressEmail
{
  [SwaggerSchema("Recipient's First Name")]
  public string FirstName { get; set; }

  [SwaggerSchema("Recipient's Last Name")]
  public string LastName { get; set; }

  [SwaggerSchema("Recipient's Civility")]
  public string Salutation { get; set; }

  [SwaggerSchema("Type of Delivery Place (e.g. Home, Work,...)")]
  public string DeliveryPlace { get; set; }

  [SwaggerSchema("Recipient's Address")]
  public string Street { get; set; }

  [SwaggerSchema("Recipient's Address")]
  public string AdditionalAddress { get; set; }

  [SwaggerSchema("Recipient's Address")]
  public string ZipCode { get; set; }

  [SwaggerSchema("Recipient's Address")]
  public string City { get; set; }

  [SwaggerSchema("Recipient's Address")]
  public string Region { get; set; }

  [SwaggerSchema("Recipient's Address in 2-letters format (e.g. FR, ES, PT, IT, SE, DK)")]
  public string Country { get; set; }

  [SwaggerSchema("Recipient's Phone number in international format: FR = +33612345678, IT = +393481234567,...")]
  public string Phone { get; set; }
}
