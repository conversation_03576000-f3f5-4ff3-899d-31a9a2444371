﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Florist;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristAccessoriesAddedMessage : BaseMessage<LegacyFloristAccessoriesAddedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => string.Join(',', Payload?.Accessories.Select( c => c.ProductCode));

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyFloristAccessoriesAddedPayload : LegacyPayload, IEquatable<LegacyFloristAccessoriesAddedPayload>
    {
        public List<Accessory> Accessories { get; set; }

        public bool Equals(LegacyFloristAccessoriesAddedPayload parameter)
        {
            return (Accessories == parameter.Accessories);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristAccessoriesAddedPayload);
        }

        public override int GetHashCode() => new
        {
            Accessories
        }.GetHashCode();
    }
}

