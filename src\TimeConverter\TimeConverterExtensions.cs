﻿using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.TimeConverter
{
    public static class TimeConverterExtensions
    {
        public static IServiceCollection UseTimeConverter(this IServiceCollection services)
        {
            services.AddScoped<ITimeConverter, TimeConverter>();
            return services;
        }

        public static IServiceCollection UseTimeConverterAsSingleton(this IServiceCollection services)
        {
            services.AddSingleton<ITimeConverter, TimeConverter>();
            return services;
        }
    }
}
