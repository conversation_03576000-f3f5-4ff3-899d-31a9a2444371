﻿using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.Postgres.Repository;
using ITF.SharedLibraries.Postgres.Repository.Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Text;
using ITF.SharedLibraries.Postgres.Repository.EntityFramework;

namespace ITF.SharedLibraries.Postgres.Extensions
{
    public static class PostgresExtensions
    {
        public static IServiceCollection AddPostgresWithDapper(this IServiceCollection services, IConfiguration config, string varEnv = "Postgres")
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);
            services.AddSingleton(typeof(IGenericDapperRepository), typeof(GenericDapperRepository));

            return services;
        }

        public static IServiceCollection AddPostgresWithEntityFramework<TContext>(this IServiceCollection services, IConfiguration config, string varEnv = "Postgres", bool automaticMigrations = true) where TContext : DbContext
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddDbContext<TContext>(options =>
                options.UseNpgsql(configuration.postgres, opt => opt.EnableRetryOnFailure(5))
                );

            services.AddScoped(typeof(IEntityFrameworkRepository<,>), typeof(EntityFrameworkRepository<,,>));
            
            if(automaticMigrations)
                services.AddHostedService<MigratorHostedService<TContext>>();

            return services;
        }
    }
}
