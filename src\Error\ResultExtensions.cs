﻿//using System;
//using System.Threading.Tasks;
//using System.Collections.Generic;
//using System.Linq;

//namespace ITF.Lib.Common.Functionnal;

//public static class ResultExtensions
//{
//    public static Result<T> Ensure<T>(
//        this Result<T> result,
//        Func<T, bool> predicate,
//        Error error)
//    {
//        if (result.IsFailure)
//        {
//            return result;
//        }

//        return predicate(result.Value) ?
//            result :
//            Result.Failure<T>(error);
//    }

//    public static Result<TOut> Map<TIn, TOut>(
//        this Result<TIn> result,
//        Func<TIn, TOut> mappingFunc)
//    {
//        return result.IsSuccess ?
//            Result.Success(mappingFunc(result.Value)) :
//            Result.Failure<TOut>(result.Errors);
//    }

//    public static Result<TOut> Map<TIn, TOut>(
//        this Result<TIn> result,
//        Func<TIn, Result<TOut>> mappingFunc)
//    {
//        if (result.IsSuccess)
//        {
//            var resMap = mappingFunc(result.Value);

//            if (resMap.IsSuccess)
//                return Result.Success(resMap.Value);
//            return Result.Failure<TOut>(resMap.Errors);
//        }
//        else
//            return Result.Failure<TOut>(result.Errors);
            
//    }

//    //public static Result<TOut> Map<TIn, TOut>(
//    //    this Result<TIn> result,
//    //    Func<Result<TIn>, Result<TOut>> mappingFunc)
//    //{
//    //    if (result.IsSuccess)
//    //    {
//    //        var resMap = mappingFunc(result.Value);

//    //        if (resMap.IsSuccess)
//    //            return Result.Success(resMap.Value);
//    //        return Result.Failure<TOut>(resMap.Errors);
//    //    }
//    //    else
//    //        return Result.Failure<TOut>(result.Errors);

//    //}

//    public static Result<TOut> Bind<TIn,TOut>(
//        this Result<TIn> result,
//        Func<TIn, Result<TOut>> func)
//    {
//        if (result.IsFailure)
//        {
//            return Result.Failure<TOut>(result.Errors);
//        }

//        return func(result.Value);
//    }

//    public static async Task<Result> Bind<TIn>(
//        this Result<TIn> result,
//        Func<TIn, Task<Result>> func)
//    {
//        if (result.IsFailure)
//        {
//            return Result.Failure(result.Errors);
//        }

//        return await func(result.Value);
//    }

//    public static async Task<Result<TOut>> Bind<TIn, TOut>(
//        this Result<TIn> result,
//        Func<TIn, Task<Result<TOut>>> func)
//    {
//        if (result.IsFailure)
//        {
//            return Result.Failure<TOut>(result.Errors);
//        }

//        return await func(result.Value);
//    }

//    public static Result<TIn> Tap<TIn>(this Result<TIn> result, Action<TIn> action)
//    {
//        if (result.IsSuccess)
//        {
//            action(result.Value);
//        }

//        return result;
//    }

//    public static async Task<Result<TIn>> Tap<TIn>(this Result<TIn> result, Func<Task> func)
//    {
//        if (result.IsSuccess)
//        {
//            await func();
//        }

//        return result;
//    }

//    public static Result<TIn> TapErrorOnPredicate<TIn>(this Result<TIn> result , Func<TIn, bool> predicate, Action<TIn> errorAction)
//    {

//        if (result.IsFailure)
//            return result;

//        if (!predicate(result.Value))
//            errorAction(result.Value);

//        return result;
//    }

//    public static Result<TIn> Ensure<TIn>(this Result<TIn> result, Func<TIn, bool> predicate, Action<TIn> errorAction)
//    {

//        if (result.IsFailure)
//            return result;

//        if (!predicate(result.Value))
//            errorAction(result.Value);

//        return result;
//    }

//    public static async Task<Result<TIn>> Tap<TIn>(
//        this Task<Result<TIn>> resultTask,
//        Func<TIn, Task> func)
//    {
//        Result<TIn> result = await resultTask;

//        if (result.IsSuccess)
//        {
//            await func(result.Value);
//        }

//        return result;
//    }
//}
