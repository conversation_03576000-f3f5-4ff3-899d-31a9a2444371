﻿using EventStore.Client;
using ITF.SharedLibraries.CustomBackgroundService;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VaultSharp.V1.AuthMethods.AWS;

namespace ITF.SharedLibraries.Kafka.Subscriber
{
  public class KafkaMultipleSubscribers<TKey, TValue> : CriticalBackgroundService
  {
    private readonly IKafkaSubscriber<TKey, TValue> _kafkaSubscriber;

    public KafkaMultipleSubscribers(
        ILogger<KafkaMultipleSubscribers<TKey, TValue>> logger,
        IKafkaSubscriber<TKey, TValue> kafkaSubscriber,
        IHostApplicationLifetime applicationLifetime) : base(applicationLifetime, logger)
    {
      _kafkaSubscriber = kafkaSubscriber;
      _kafkaSubscriber.InitKafka();
    }

    protected async override Task InfiniteProcessAsync(CancellationToken stoppingToken)
    {
        var cancelToken = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);

        _logger.LogInformation("{service} is starting", nameof(KafkaMultipleSubscribers<TKey, TValue>));
        cancelToken.Token.Register(() =>
            _logger.LogInformation("{service} background task is stopping due to a cancellationToken canceled state", nameof(KafkaMultipleSubscribers<TKey, TValue>)));

        var kafkaTasks = new List<Task>();

        try
        {
            Parallel.ForEach(_kafkaSubscriber.GetSubscribers().ToList(), sub =>
            {
                kafkaTasks.Add(_kafkaSubscriber.ConsumerCheckAsync(sub, cancelToken.Token));
            });

            // Wait for any task to complete
            var completedTask = await Task.WhenAny(kafkaTasks);

            // Check if the completed task is faulted
            if (completedTask.IsFaulted)
            {
                var exception = completedTask.Exception?.Flatten().InnerException;
                _logger.LogError(exception, "{service} encountered an error in one of the Kafka tasks", nameof(KafkaMultipleSubscribers<TKey, TValue>));
                throw new AggregateException("One or more Kafka tasks encountered an error", exception);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{service} encountered an unexpected error", nameof(KafkaMultipleSubscribers<TKey, TValue>));
            throw;
        }
        finally
        {
            cancelToken.Cancel();
            // Wait for all tasks to complete or timeout
            await Task.WhenAny(Task.WhenAll(kafkaTasks), Task.Delay(5000));
        }
    }
  }
}

