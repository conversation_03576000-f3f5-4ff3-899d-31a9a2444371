﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Shopopop.Payloads
{
    public class DeliveryEndedPayload : IPayload
    {
        public string OrderId { get; set; }
        public string Status { get; set; }
        public string CourrierStatus { get; set; }
        public string CourrierReason { get; set; }

        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
    }
}
