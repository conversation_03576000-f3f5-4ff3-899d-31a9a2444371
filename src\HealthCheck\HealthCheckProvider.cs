﻿using System.Threading;

namespace ITF.SharedLibraries.HealthCheck
{
    public class HealthCheckProvider
    {
        private long _threadSafeBoolBackValue = 0;

        public HealthCheckProvider()
        {
            Interlocked.Exchange(ref _threadSafeBoolBackValue, 1);
        }

        public void SetUnHealthy()
            => Interlocked.Exchange(ref _threadSafeBoolBackValue, 0);

        public bool IsHealthy()
            => Interlocked.Read(ref _threadSafeBoolBackValue) == 1;
    }
}
