﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristBlockedTransmissionMessage : BaseMessage<LegacyFloristBlockedTransmissionPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

             
            }
        }
    }

    public class LegacyFloristBlockedTransmissionPayload : LegacyPayload, IEquatable<LegacyFloristBlockedTransmissionPayload>
    {
        public string FloristIdentifier { get; set; }
        public bool BlockedTransmission { get; set; }

        public bool Equals(LegacyFloristBlockedTransmissionPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                BlockedTransmission == parameter.BlockedTransmission
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristBlockedTransmissionPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            BlockedTransmission
        }.GetHashCode();

        //public static implicit operator LegacyFloristBlockedTransmissionPayload(Messages.V1.LegacyFloristBlockedTransmissionMessage message)
        //{
        //    var entity = new LegacyFloristBlockedTransmissionPayload
        //    {
        //        FloristIdentifier = message.Payload.FloristIdentifier,
        //        BlockedTransmission = message.Payload.BlockedTransmission,
        //    };
        //    return entity;
        //}
    }
}
