﻿using System;
using System.Runtime.Serialization;

namespace IT.SharedLibraries.CT.Exceptions
{
    public class CtCustomException : Exception
    {
        public CtCustomException()
        {
        }

        public CtCustomException(string message) : base(message)
        {
        }

        public CtCustomException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected CtCustomException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
