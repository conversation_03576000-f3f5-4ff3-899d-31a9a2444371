﻿using ITF.SharedModels.Group.Enums;
using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveryCostUpdated
    {
        public string OrderIdentifier { get; set; }
        public decimal DeliveryCost { get; set; }

        public static implicit operator GlobalOrderDeliveryCostUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryCostUpdatedMessage v)
        {
            return new GlobalOrderDeliveryCostUpdated
            {
                DeliveryCost = v?.Payload?.DeliveryCost ?? 0,
                OrderIdentifier = v?.Payload.OrderIdentifier,

            };
        }
    }
}
