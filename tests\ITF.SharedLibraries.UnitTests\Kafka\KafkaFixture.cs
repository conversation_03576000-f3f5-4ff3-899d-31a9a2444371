﻿using System;
using System.IO;
using System.Threading.Tasks;
using Ductus.FluentDocker.Model.Common;
using Ductus.FluentDocker.Builders;
using Ductus.FluentDocker.Services;
using Microsoft.Extensions.Configuration;
using ITF.SharedLibraries.Kafka;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.Kafka.Publisher;
using Microsoft.Extensions.Logging;
using ITF.SharedLibraries.Kafka.Subscriber;
using ITF.SharedLibraries.UnitTests.Kafka.Handler;
using System.Collections.Concurrent;
using Confluent.Kafka;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using commercetools.Sdk.Api.Serialization;
using commercetools.Sdk.Api;

namespace ITF.SharedLibraries.UnitTests.Kafka
{
    public sealed class KafkaFixture : IDisposable
    {
        private readonly ICompositeService _svc;

        public IKafkaPublisher _kafkaPublisher;
        public IKafkaSubscriber<string, string> _kafkaSubscriber;
        public ConcurrentBag<object> _concurrentBag = new();
        public SerializerService SerializerService { get; private set; }
        
        public KafkaFixture()
        {
            var file = Path.Combine(Directory.GetCurrentDirectory(),
                    (TemplateString)"Resources/Kafka/docker-compose.yml");

            // @formatter:off
            _svc = new Builder()
                              .UseContainer()
                              .UseCompose()
                              .FromFile(file)
                              .RemoveOrphans()
                              .Timeout(TimeSpan.FromMinutes(5))
                              .KeepRunning()
                              .Build().Start();
            // @formatter:on


            var config = new ConfigurationBuilder()
               .AddJsonFile("Resources/Kafka/appsettings.json")
               .Build();

            var configuration = config.Get<Configuration>("Kafka");


            var services = new ServiceCollection();
            services.UseCommercetoolsApiSerialization();
            var serviceProvider = services.BuildServiceProvider();
            SerializerService = serviceProvider.GetService<SerializerService>();

            // Clean topics
            configuration.TopicsToCreateConfigurations.ForEach(t => RemoveTopic(t.TopicName).Wait());

            // Kafka publisher
            _kafkaPublisher = new KafkaPublisher(new LoggerFactory(), configuration, SerializerService);

            // Kafka subscriber
            IMessageHandler messageHandler = new MessageHandler(_concurrentBag);
            _kafkaSubscriber = new KafkaSubscriber<string, string>(configuration, new[] { messageHandler }, new NullLogger<KafkaSubscriber<string, string>>(), serviceProvider, SerializerService);
        }

        private static async Task RemoveTopic(string topic)
        {
            try
            {
                using var adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = "localhost:9092" }).Build();
                await adminClient.DeleteTopicsAsync(new[] { topic });
            }
            catch { }
        }

        public void Dispose()
        {
            _svc?.Stop();
            _svc?.Dispose();
        }
    }
}
