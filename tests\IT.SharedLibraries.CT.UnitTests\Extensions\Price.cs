﻿using commercetools.Sdk.Api.Models.Common;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace IT.SharedLibraries.CT.UnitTests.Extensions
{
    public class Price
    {
        public static IEnumerable<object[]> TestGetPrice_ReturnsExpectedDecimalData =>
        [
            [new TypedMoney { CentAmount = 1234, FractionDigits = 2 }, 1, null, 12.34],
            [new TypedMoney { CentAmount = 100, FractionDigits = 2 }, 1, new List<IPriceTier> { }, 1.00],
            [new TypedMoney { CentAmount = 1999, FractionDigits = 3 }, 1, new List<IPriceTier> { }, 1.999],
            [new TypedMoney { CentAmount = 0, FractionDigits = 2 }, 1, new List<IPriceTier> { }, 0.00],
            [new TypedMoney { CentAmount = 1, FractionDigits = 0 }, 1, new List<IPriceTier> { }, 1],

            [new TypedMoney { CentAmount = 5995, FractionDigits = 2 }, 6, new List<IPriceTier> { new PriceTier { MinimumQuantity = 6 , Value = new TypedMoney { CentAmount = 9991666666666667, FractionDigits = 15 } } }, 59.95],
            [new TypedMoney { CentAmount = 5995, FractionDigits = 2 }, 7, new List<IPriceTier> { new PriceTier { MinimumQuantity = 6, Value = new TypedMoney { CentAmount = 9991666666666667, FractionDigits = 15 } } , new PriceTier { MinimumQuantity = 7, Value = new TypedMoney { CentAmount = 9707142857142856, FractionDigits = 15 } } }, 67.95],
            [new TypedMoney { CentAmount = 5995, FractionDigits = 2 }, 8, new List<IPriceTier> { new PriceTier { MinimumQuantity = 6, Value = new TypedMoney { CentAmount = 9991666666666667, FractionDigits = 15 } } , new PriceTier { MinimumQuantity = 7, Value = new TypedMoney { CentAmount = 9707142857142856, FractionDigits = 15 } } }, 59.95]
        ];

        [Theory]
        [MemberData(nameof(TestGetPrice_ReturnsExpectedDecimalData))]
        public void GetPrice_ReturnsExpectedDecimal(ITypedMoney productPrice , int qty ,IList<IPriceTier> priceTiers , decimal expectedPrice)
        {
            // Arrange
            var priceMock = new Mock<IPrice>();
            priceMock.Setup(p => p.Value).Returns(productPrice);
            priceMock.Setup(p => p.Tiers).Returns(priceTiers);

            // Act
            var result = priceMock.Object.GetPrice(qty);

            // Assert
            Assert.Equal(expectedPrice, result);
        }

        [Theory]
        [InlineData(-1, true)]   // Expiré hier
        [InlineData(0, true)]   // Expire aujourd'hui return true if we have to consider hour:minutes value
        [InlineData(1, false)]   // Expire demain
        [InlineData(null, false)] // ValidUntil est null
        public void IsExpired_ReturnsExpectedResult(int? daysOffset, bool expectedResult)
        {
            // Arrange
            DateTime? validUntil = daysOffset.HasValue
                ? DateTime.Now.Date.AddDays(daysOffset.Value)
                : null;

            var priceMock = new Mock<IPrice>();
            priceMock.Setup(p => p.ValidUntil).Returns(validUntil);

            // Act
            var result = priceMock.Object.IsExpired();

            // Assert
            Assert.Equal(expectedResult, result);
        }
    }
}
