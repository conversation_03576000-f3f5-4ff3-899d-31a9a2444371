﻿using GoogleApi;
using GoogleApi.Entities.Common.Enums;
using GoogleApi.Entities.Maps.Common;
using GoogleApi.Entities.Maps.DistanceMatrix.Request;
using GoogleApi.Entities.Maps.DistanceMatrix.Response;
using GoogleApi.Entities.Maps.Geocoding.Address.Request;
using GoogleApi.Entities.Maps.Geocoding.Common;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.HealthCheck;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static ITF.SharedLibraries.Geocoding.Exceptions;

namespace ITF.SharedLibraries.Geocoding
{
    public class GoogleMapsGeocodingService : IGeocodingService
    {
        private readonly IOptionsMonitor<GoogleMapsSettings> _gmSettings;
        private readonly ILogger _logger;
        private readonly HealthCheckProvider _hc;

        public GoogleMapsGeocodingService(
            IOptionsMonitor<GoogleMapsSettings> googleMapsSettings,
            ILogger<GoogleMapsGeocodingService> logger,
            HealthCheckProvider healthCheckProvider
            )
        {
            _gmSettings = googleMapsSettings;
            _logger = logger;
            _hc = healthCheckProvider;

            // check the required sensitive data are present in the GoogleMapsSettings else setUnhealthy
            if(_hc != null &&
                (string.IsNullOrWhiteSpace(_gmSettings?.CurrentValue?.CryptKey) || string.IsNullOrWhiteSpace(_gmSettings?.CurrentValue?.ClientId)))
                SetUnHealthy();
        }

        private void SetUnHealthy()
        {
            if (_hc != null)
            {
                try
                {
                    _hc.SetUnHealthy();
                    _logger.LogError("We set the App as Unhealthy Beceause we can't load the required settings 'CryptKey' and/or 'ClientId' from GoogleMaps service");
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "We can't set the App as Unhealthy (missing 'CryptKey' and/or 'ClientId' from GoogleMaps service) because of an exception when trying to call the SetUnHealthy Method");
                }
            }
        }

        public async Task<Coordinates> GeocodeAsync(string street, string zipCode, string city, string countryCode)
        {
            var request = new AddressGeocodeRequest
            {
                Key = _gmSettings.CurrentValue.CryptKey,
                ClientId = _gmSettings.CurrentValue.ClientId,
                Address = string.IsNullOrWhiteSpace(street) == false ? 
                $"{street},{zipCode} {city}, {GetCountry(zipCode, countryCode)}" :
                $"{zipCode} {city}, {GetCountry(zipCode, countryCode)}"
            };

            // Call Google Maps
            var result = await GoogleMaps.Geocode.AddressGeocode.QueryAsync(request);
            
            if (result.Status != Status.Ok)
                throw new GeocodingProviderTransientException($"GoogleMaps fails to geocode {request.Address} with result {result.Status}");

            if (!result.Results.Any())
                throw new GeocodingProviderNoMatchingResultException($"GoogleMaps fails to geocode {request.Address} (no results at all)");

            return GetMatchingsCoordinates(result.Results, zipCode, city, countryCode);
        }

        private Coordinates GetMatchingsCoordinates(IEnumerable<Result> results, string zipCode, string city, string countryCode)
        {
            var matchingResults = new List<Result>();

            // Get all corresponding countries
            results
                .ToList()
                .ForEach(r =>
                {
                    _logger.LogInformation("Current analysed address result {address} from GoogleMaps", r?.FormattedAddress);

                    if (OverallMatch(r, zipCode, city, countryCode))
                        matchingResults.Add(r);
                    else
                        _logger.LogWarning("Current analysed address result {address} from GoogleMaps does not meet the analysing requirements for {zipCode} {city} {countryCode}", r?.FormattedAddress, zipCode, city, countryCode);
                });

            // No match
            if (matchingResults.Count == 0)
                throw new GeocodingProviderNoMatchingResultException("The GoogleMaps results don't match with the current requirements (no results at all)");

            // On all previous kept results : second check => same zip code
            var googleMapBestChoiceResult = matchingResults.FirstOrDefault(m => m.FormattedAddress.Contains(zipCode));

            if (googleMapBestChoiceResult is null)
                googleMapBestChoiceResult = matchingResults.FirstOrDefault(m => m.FormattedAddress.Contains(city));

            if (googleMapBestChoiceResult is null)
                throw new GeocodingProviderNoMatchingResultException($"The GoogleMaps results don't match with the current requirements ({string.Join(" / ", matchingResults.Select(m => m.FormattedAddress))})");

            // Pick the coordinates from Google maps results
            return new Coordinates { Latitude = googleMapBestChoiceResult.Geometry.Location.Latitude, Longitude = googleMapBestChoiceResult.Geometry.Location.Longitude };
        }

        private static bool OverallMatch(Result result, string zipCode, string city, string countryCode)
        {
            var isSameCity = false;
            var isSameZipCode = false;
            var isSameDepartment = false;
            var isSameCountry = false;

            // Get expected variables from Google result
            #region country
            var googlecountry = result?.FormattedAddress?.Split(new char[] { ',' })?.Last()?.Replace(" ", "")?.ToLower();
            if (!string.IsNullOrEmpty(googlecountry))
                googlecountry = Regex.Replace(googlecountry, @"[\d-]", string.Empty);
            #endregion

            #region zipCode
            var googleZipCode = result?
                    .AddressComponents
                    .FirstOrDefault(ac => ac.Types.ToList().Contains(AddressComponentType.Postal_Code))?
                    .LongName;
            #endregion

            #region address
            var googleAddress = result?
                .FormattedAddress?
                .Replace("-", " ")?
                .ToLower();
            #endregion

            #region city
            var googleCity =
                result?
                .AddressComponents?
                .FirstOrDefault(ac => ac.Types.ToList().Contains(AddressComponentType.Locality))?
                .LongName?
                .ToLower()
                .RemoveDiacritics();
            #endregion

            #region Checks
            // ZipCode
            if (!string.IsNullOrEmpty(googleZipCode) && googleZipCode.Equals(zipCode))
                isSameZipCode = true;

            // Country
            if (!string.IsNullOrEmpty(googlecountry) &&
                GetCountries(zipCode, countryCode).ConvertAll(d => d.ToLower()).Contains(googlecountry))
                isSameCountry = true;

            // City & Country
            if (IsFrenchDromCom(zipCode, countryCode))
            {
                // Case for all Drom Com (no zipcode returned)
                if (!string.IsNullOrEmpty(googleAddress) && googleAddress.RemoveDiacritics().Contains(city.Replace("-", " ").ToLower().RemoveDiacritics()))
                    isSameCity = true;

                // Same zipcode but France instead of Réunion => it matches
                if (!string.IsNullOrEmpty(googleZipCode) &&
                    !string.IsNullOrEmpty(googlecountry) &&
                    googleZipCode.Length > 2 && zipCode.Length > 2 &&
                    googleZipCode.Substring(0, 3).Equals(zipCode.Substring(0, 3)) &&
                    countryCode.Equals("FR") &&
                    googlecountry.Equals("france"))
                    isSameCountry = true;
            }

            // City
            var deliveryCity = city.ToLower().RemoveDiacritics();
            if (!string.IsNullOrEmpty(googleCity) && !string.IsNullOrEmpty(deliveryCity))
            {
                if (!isSameCity)
                    isSameCity = googleCity.Contains(deliveryCity) || deliveryCity.Contains(googleCity);
            }

            // Department
            isSameDepartment = !string.IsNullOrEmpty(googleZipCode) &&
                    googleZipCode.Substring(0, 2).Equals(zipCode.Substring(0, 2), StringComparison.InvariantCultureIgnoreCase);

            #endregion

            #region assert
            if (!isSameCountry)
                return false;

            if (isSameZipCode ||
                (IsFrenchDromCom(zipCode, countryCode) && isSameCity) ||
                (isSameDepartment && isSameCity))
                return true;

            return false;
            #endregion
        }

        private static List<string> GetCountries(string zipCode, string countryCode)
        {
            var result = new List<string>();

            if (countryCode != "FR" && countryCode != "LU")
                return result;

            if (countryCode == "LU")
            {
                result.Add("Luxembourg");
                return result;
            }

            if (zipCode.Length < 3)
                return result;

            switch (zipCode.Substring(0, 3))
            {
                case "980":
                    result.Add("Monaco");
                    break;
                case "971":
                    result.Add("Guadeloupe");
                    break;
                case "972":
                    result.Add("Martinique");
                    break;
                case "973":
                    result.Add("Guyane");
                    break;
                case "974":
                    result.AddRange(new[] { "La Réunion", "La Reunion", "Réunion", "Reunion" });
                    break;
                case "975":
                    result.AddRange(new[] { "St-Pierre-et-Miquelon", "St Pierre et Miquelon", "Saint-Pierre-et-Miquelon", "Saint Pierre et Miquelon", "St. Pierre & Miquelon" });
                    break;
                case "976":
                    result.Add("Mayotte");
                    break;
                case "984":
                    result.AddRange(new[] { "Terres-Australes et Antarctiques", "Terres Australes et Antarctiques" });
                    break;
                case "986":
                    result.AddRange(new[] { "Wallis-et-Futuna", "Wallis et Futuna" });
                    break;
                case "987":
                    result.AddRange(new[] { "Polynésie Française", "Polynesie Française", "Polynésie Francaise", "Polynesie Francaise" });
                    break;
                case "988":
                    result.AddRange(new[] { "Nouvelle-Calédonie", "Nouvelle Calédonie", "Nouvelle-Caledonie", "Nouvelle Caledonie" });
                    break;
                default:
                    result.AddRange(new[] { "France", "FR" });
                    break;
            }

            return result;
        }

        private static string GetCountry(string zipCode, string countryCode)
            => GetCountries(zipCode, countryCode).FirstOrDefault();

        private static bool IsFrenchDromCom(string zipCode, string countryCode)
           => (countryCode == "FR" && zipCode.Length > 2 && new[] { "971", "972", "973", "974", "975", "976", "984", "986", "987", "988" }.Contains(zipCode.Substring(0, 3)));

        public async Task<Distance> ComputeRealDistanceAsync(Coordinates from, Coordinates to)
        {
            var request = new DistanceMatrixRequest
            {
                Key = _gmSettings.CurrentValue.CryptKey,
                ClientId = _gmSettings.CurrentValue.ClientId,
                Origins = new List<LocationEx> { new LocationEx(new CoordinateEx(from.Latitude, from.Longitude)) },
                Destinations = new List<LocationEx> { new LocationEx(new CoordinateEx(to.Latitude, to.Longitude)) },
            };

            // Call Google Maps
            DistanceMatrixResponse result = await GoogleMaps.DistanceMatrix.QueryAsync(request);

            if (result.Status != Status.Ok)
                throw new GeocodingProviderTransientException($"GoogleMaps fails to compute the distance between {from} and {to} with result {result.Status}");

            if (!result.Rows.Any())
                throw new GeocodingProviderNoMatchingResultException($"GoogleMaps fails to compute the distance between {from} and {to} (no results at all)");

            var row = result.Rows.FirstOrDefault();

            if (!row.Elements.Any())
                throw new GeocodingProviderNoMatchingResultException($"GoogleMaps fails to compute the distance between {from} and {to} (no results at all)");

            var element = row.Elements.FirstOrDefault();
            return new Distance
            {
                DurationInSeconds = element.Duration.Value,
                RealDistanceInMeters = element.Distance.Value
            };
        }
    }
}
