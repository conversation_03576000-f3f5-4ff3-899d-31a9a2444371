using ITF.SharedLibraries.Hash;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace ITF.SharedLibraries.UnitTests
{
    public class PartitionTest
    {
        [Theory]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        [InlineData(9)]
        [InlineData(10)]
        [InlineData(11)]
        [InlineData(12)]
        [InlineData(13)]
        [InlineData(14)]
        [InlineData(15)]
        [InlineData(16)]
        [InlineData(17)]
        [InlineData(18)]
        [InlineData(19)]
        [InlineData(20)]
        public void GetPartition_KeepsInRange(int partitionNumber)
        {
            // Arrange
            // Act

            // Real orderIds
            var partitions = new List<long>
            {
                MurmurHash2.GetPartition("**********", partitionNumber),
                MurmurHash2.GetPartition("**********", partitionNumber),
                MurmurHash2.GetPartition("**********", partitionNumber),
                MurmurHash2.GetPartition("IT46556013", partitionNumber),
                MurmurHash2.GetPartition("IF24540417", partitionNumber),
                MurmurHash2.GetPartition("IT46555995", partitionNumber),
                MurmurHash2.GetPartition("IF30214081", partitionNumber),
                MurmurHash2.GetPartition("CCF0621401320", partitionNumber),
                MurmurHash2.GetPartition("IT99532941", partitionNumber),
                MurmurHash2.GetPartition("IF20852208", partitionNumber),
                MurmurHash2.GetPartition("CCF0651407891", partitionNumber),
                MurmurHash2.GetPartition("IT46556161", partitionNumber),
                MurmurHash2.GetPartition("IT46556107", partitionNumber),
                MurmurHash2.GetPartition("IT46556123", partitionNumber),
                MurmurHash2.GetPartition("IT46556014", partitionNumber),
                MurmurHash2.GetPartition("IT46555837", partitionNumber),
                MurmurHash2.GetPartition("IF26080706", partitionNumber),
            };

            // Random strings
            for (int i = 0; i < 10000; i++)
            {
                partitions.Add(MurmurHash2.GetPartition(RandomString(8), partitionNumber));
            }

            // Assert
            Assert.True(partitions.Min() == 0);
            Assert.True(partitions.Max() == (partitionNumber - 1));
            //Assert.True(partitions.Min() >= 0 && partitions.Min() <= (partitionNumber - 1));
            //Assert.True(partitions.Max() >= 0 && partitions.Max() <= (partitionNumber - 1));
        }

        [Theory]
        [InlineData(1)]
        [InlineData(2)]
        [InlineData(3)]
        [InlineData(4)]
        [InlineData(5)]
        [InlineData(6)]
        [InlineData(7)]
        [InlineData(8)]
        [InlineData(9)]
        [InlineData(10)]
        [InlineData(11)]
        [InlineData(12)]
        [InlineData(13)]
        [InlineData(14)]
        [InlineData(15)]
        [InlineData(16)]
        [InlineData(17)]
        [InlineData(18)]
        [InlineData(19)]
        [InlineData(20)]
        public void GetPartition_KeepsConsistent(int partitionNumber)
        {
            // Arrange
            var partitions = new List<Repartition>();
            for (int i = 0; i < partitionNumber; i++)
            {
                var str = RandomString(8);
                partitions.Add(new Repartition
                {
                    SuppliedString = str,
                    PartitionNumber = MurmurHash2.GetPartition(str, partitionNumber)
                });
            }

            // Act
            var repartitionList = new List<RepartitionList>();
            foreach (var p in partitions)
            {
                var currentRepartition = new RepartitionList { SuppliedString = p.SuppliedString };
                for (int i = 0; i < 10000; i++)
                {
                    currentRepartition
                        .PartitionNumbers
                        .Add(MurmurHash2.GetPartition(currentRepartition.SuppliedString, partitionNumber));
                }
                repartitionList.Add(currentRepartition);
            }

            // Assert
            foreach (var p in partitions.Select((value, i) => (value, i)))
            {
                var correspingRepartition = repartitionList.ElementAt(p.i);
                Assert.All(correspingRepartition.PartitionNumbers, c => Assert.Equal(p.value.PartitionNumber, c));
            }
        }

        private static Random random = new();
        public static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
              .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        class Repartition
        {
            public long PartitionNumber { get; set; }
            public string SuppliedString { get; set; }
        }

        class RepartitionList
        {
            public List<long> PartitionNumbers { get; set; } = new();
            public string SuppliedString { get; set; }
        }
    }
}
