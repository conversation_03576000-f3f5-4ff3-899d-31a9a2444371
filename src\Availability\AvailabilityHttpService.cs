﻿using ITF.SharedLibraries.Availability.dto;
using ITF.SharedLibraries.HttpClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ITF.SharedLibraries.Availability
{
    public interface IAvailabilityHttpService : IHttpClient
    {
        Task<HttpResponseMessage> CheckAvailability(AvailabilityInputDto availabilityInputDto);
    }

    public class AvailabilityHttpService(System.Net.Http.HttpClient httpClient, IConfiguration config, ILogger<AvailabilityHttpService> logger) 
        : ITF.SharedLibraries.HttpClient.HttpClient(httpClient, config, "AvailabilityEndpoint", logger), IAvailabilityHttpService
    {
        public async Task<HttpResponseMessage> CheckAvailability(AvailabilityInputDto availabilityInputDto)
        {
            ///availabilityfr/api/v1/Availability/CheckAvailability
            return await PostAsync(availabilityInputDto, "/CheckAvailability");
        }
    }
}
