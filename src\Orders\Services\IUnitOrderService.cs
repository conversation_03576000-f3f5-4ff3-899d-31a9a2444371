﻿using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ShippingMethods;
using ITF.SharedModels.DataModels.Order;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Orders.Services
{
    public interface IUnitOrderService
    {
        IShippingMethod GetNationalShippingMethod(GlobalOrderModel order, IShippingMethodPagedQueryResponse availableShippingMethods);
        string GetNationalShippingMethodKey(List<IProduct> products, List<IProduct> accessories, bool isMourning);
        string GetState(GlobalOrderShipping shipping);
    }
}
