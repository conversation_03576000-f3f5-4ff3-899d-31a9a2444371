﻿namespace ITF.SharedLibraries.HttpClient
{
    public class Configuration
    {
        public int HttpTimeoutInSeconds { get; set; }
        public int PolicyTimeoutInSeconds { get; set; }
        public int HandlerLifetime { get; set; }
        public int DefaultConnectionLimit { get; set; }
        public string Url { get; set; }
        public string Endpoint { get; set; }
        public bool DisableCookieAffinity { get; set; }
        public bool DisableExpect100ToContinue { get; set; }
        public bool DisableNagleAlgorithm { get; set; }
        public int WorkerThreads { get; set; }
        public int CompletionPortThreads { get; set; }
        public Authentication.Authentication Authentication { get; set; }
        public string AllowedOrigins { get; set; }
        public string AllowedMethods { get; set; }
    }
}
