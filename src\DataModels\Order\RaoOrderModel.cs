﻿using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.DataModels.Order;

public class RaoOrderModel
{
    public List<OrderHistory>? OrderHistories { get; set; }
    public double? AffectationDistance { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? EstimatedDeliveryDistance { get; set; }
    public double? EstimatedDeliveryTime { get; set; }
    public string? GeocodingSource { get; set; }
    public string? AdditionalAddress { get; set; }
    public string? ManagerStatus { get; set; }
    public string? Url { get; set; }
    public string? ProcessingStatus { get; set; }
    public string? DeliveryPhone2 { get; set; }
    public string? ExternalReference { get; set; }
    public Billing? Billing { get; set; }
    public double? AxSendCounter { get; set; }
    public string? From { get; set; }
    public string? RootOrder { get; set; }
    public List<StatusHistory>? StatusHistory { get; set; }
    public DateTime? LFMDate { get; set; }
    public string? FloristProcessingStatus { get; set; }
    public string? OrderSubType { get; set; }
    public string? OrderMatchingId { get; set; }
    public string? OrderId { get; set; }
    public DateTime? OrderDate { get; set; }
    public string? OrderType { get; set; }
    public string? Status { get; set; }
    public string? StatusComment { get; set; }
    public string? CustomerTitle { get; set; }
    public string? CustomerFirstName { get; set; }
    public string? CustomerLastName { get; set; }
    public string? CustomerEmail { get; set; }
    public string? CustomerPhone { get; set; }
    public string? CustomerCompanyName { get; set; }
    public string? CustomerType { get; set; }
    public string? CustomerId { get; set; }
    public string? BillingStreet { get; set; }
    public string? BillingZipCode { get; set; }
    public string? BillingCity { get; set; }
    public string? Event { get; set; }
    public string? RecipientTitle { get; set; }
    public string? RecipientFirstName { get; set; }
    public string? RecipientLastName { get; set; }
    public string? DeliveryStreet { get; set; }
    public string? DeliveryZipCode { get; set; }
    public string? DeliveryCity { get; set; }
    public string? DeliveryCountry { get; set; }
    public string? DeliveryBuilding { get; set; }
    public string? DeliveryFloor { get; set; }
    public string? DeliveryDigicode { get; set; }
    public string? DeliveryDoor { get; set; }
    public string? DeliveryLocationType { get; set; }
    public string? DeliveryLocation { get; set; }
    public string? DeliveryContactFirstName { get; set; }
    public string? DeliveryContactLastName { get; set; }
    public string? DeliveryPhone { get; set; }
    public string? DeliveryInstruction { get; set; }
    public DateTime? DeliveryDate { get; set; }
    public string? DeliveryWindow { get; set; }
    public string? DeliveryTime { get; set; }
    public string? TransmitterFloristId { get; set; }
    public string? ExecutingFloristId { get; set; }
    public double? DeliveryCost { get; set; }
    public double? PaidAmount { get; set; }
    public double? TotalAmount { get; set; }
    public double? SaleExecutionAmount { get; set; }
    public string? CodeAP { get; set; }
    public string? PartnerCode { get; set; }
    public string? EmployeeId { get; set; }
    public string? Source { get; set; }
    public string? SalesOrigin { get; set; }
    public string? PaymentProvider { get; set; }
    public string? PaymentMode { get; set; }
    public string? PaymentId { get; set; }
    public string? PaymentContractNumber { get; set; }
    public string? PaymentStatus { get; set; }
    public string? ParentReference { get; set; }
    public string? Message { get; set; }
    public string? Signature { get; set; }
    public string? CodeLF { get; set; }
    public string? PaymentSchedule { get; set; }
    public List<OrderDetail>? OrderDetails { get; set; }
    public string? InternationalOrderId { get; set; }
    public string? ExternalOrderId { get; set; }
    public double? AdditionnalDeliveryCost { get; set; }
    public double? DeliveryCostCalculationType { get; set; }

}
public static class RaoOrderModelExtensions
{
    public static BaseOrderPayload ToBaseOrderPayload(this RaoOrderModel raoOrder)
    {
        return new BaseOrderPayload
        {
            APCode = raoOrder.CodeAP,
            Billing = new BillingInformations
            {
                City = raoOrder.BillingCity,
                Street = raoOrder.BillingStreet,
                ZipCode = raoOrder.BillingZipCode,
            },
            Customer = new CustomerInformations
            {
                CompanyName = raoOrder.CustomerCompanyName,
                Email = raoOrder.CustomerEmail,
                EmployeeId = raoOrder.EmployeeId,
                FirstName = raoOrder.CustomerFirstName,
                LastName = raoOrder.CustomerLastName,
                Id = raoOrder.CustomerId,
                PartnerCode = raoOrder.PartnerCode,
                Phone = raoOrder.CustomerPhone,
                Title = raoOrder.CustomerTitle,
                Type = raoOrder.CustomerType
            },
            Delivery = new DeliveryInformations
            {
                AdditionalAddress = raoOrder.AdditionalAddress,
                AdditionnalTripCost = raoOrder.AdditionnalDeliveryCost,
                ContactFirstName = raoOrder.DeliveryContactFirstName,
                ContactLastName = raoOrder.DeliveryContactLastName,
                Date = raoOrder.DeliveryDate.HasValue ? raoOrder.DeliveryDate.Value : DateTime.MinValue,
                Instructions = raoOrder.DeliveryInstruction,
                LocationType = raoOrder.DeliveryLocationType,
                Place = raoOrder.DeliveryLocation,
                PreviousDate = DateTime.MinValue,
                Time = raoOrder.DeliveryTime,
                TripCost = raoOrder.DeliveryCost,
                TripCostComputationType = raoOrder.DeliveryCostCalculationType.HasValue ? Convert.ToInt32(raoOrder.DeliveryCostCalculationType.Value) : 0,
                Window = raoOrder.DeliveryWindow ?? string.Empty
            },
            DynamicsSyncCounter = 0,
            ExecutingFloristSpecified = false,
            ExternalOrderId = raoOrder.ExternalOrderId,
            ExternalReference = raoOrder.ExternalReference,
            FloristId = raoOrder.ExecutingFloristId,
            GlobalPrivex = 0,
            InternationalOrderId = raoOrder.InternationalOrderId,
            Message = raoOrder.Message,
            OrderDate = raoOrder.OrderDate.HasValue ? raoOrder.OrderDate.Value : DateTime.MinValue,
            OrderEvent = string.Empty,
            OrderId = raoOrder.OrderId ?? string.Empty,
            OrderType = raoOrder.OrderType,
            PaidAmount = raoOrder.PaidAmount,
            ParentReference = raoOrder.ParentReference,
            Payment = new PaymentInformations
            {
                Id = raoOrder.PaymentId,
                ContractNumber = raoOrder.PaymentContractNumber,
                Mode = raoOrder.PaymentMode,
                Provider = raoOrder.PaymentProvider,
                Schedule = raoOrder.PaymentSchedule,
                Status = raoOrder.PaymentStatus
            },
            Products = new(),
            Recipient = new RecipientInformations
            {
                FirstName = raoOrder.RecipientFirstName,
                LastName = raoOrder.RecipientLastName,
                Greetings = raoOrder.RecipientTitle,
                City = raoOrder.DeliveryCity,
                CountryCode = raoOrder.DeliveryCountry,
                Latitude = raoOrder.Latitude.HasValue ? raoOrder.Latitude.Value : 0,
                Longitude = raoOrder.Longitude.HasValue ? raoOrder.Longitude.Value : 0,
                MainPhone = raoOrder.DeliveryPhone,
                SecondPhone = raoOrder.DeliveryPhone2,
                Street = raoOrder.DeliveryStreet,
                ZipCode = raoOrder.DeliveryZipCode,
            },
            SalesOrigin = raoOrder.SalesOrigin,
            Signature = raoOrder.Signature,
            Source = raoOrder.Source,
            Status = new StatusInformations
            {
                Status = raoOrder.Status,
                ManagerStatus = raoOrder.ManagerStatus,
                NotificationProcessingStatus = raoOrder.ProcessingStatus,
                ReadStatus = raoOrder.ProcessingStatus
            },
            TotalAmount = raoOrder.TotalAmount,
            TransmitterFloristId = raoOrder.TransmitterFloristId,
        };
    }

}

public class OrderHistory
{
    public string? OrderHistoryId { get; set; }
    public string? OrderId { get; set; }
    public string? Message { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? FloristId { get; set; }
    public double? VolumePerformance { get; set; }
    public double? AmountPerformance { get; set; }
    public double? GlobalPerformance { get; set; }
    public int? ExecutionCounter { get; set; }
    public double? ExecutionAmount { get; set; }
    public string? AffectationStatus { get; set; }
    public DateTime? LastModified { get; set; }
    public string? OrderStatus { get; set; }
    public string? CreatedBy { get; set; }
}

public class Billing
{
    public string? Entity { get; set; }
    public string? OrderId { get; set; }
    public string? FloristId { get; set; }
    public string? FileName { get; set; }
    public string? Url { get; set; }
    public DateTime? CreatedAt { get; set; }
}

public class StatusHistory
{
    public string? FloristId { get; set; }
    public string? Source { get; set; }
    public string? Reason { get; set; }
    public string? Agent { get; set; }
    public string? PreviousStatus { get; set; }
    public string? NewStatus { get; set; }
    public DateTime? CreatedAt { get; set; }
}

public class OrderDetail
{
    public string? OrderId { get; set; }
    public int? OrderDetailId { get; set; }
    public string? ProductId { get; set; }
    public string? Label { get; set; }
    public int? Quantity { get; set; }
    public double? Price { get; set; }
    public double? SaleExecutionPrice { get; set; }
    public double? MarketingSupplement { get; set; }
    public string? Size { get; set; }
    public string? IntercatCode { get; set; }
    public string? BundleId { get; set; }
    public string? Localization { get; set; }
    public string? Description { get; set; }
    public string? DiscountCode { get; set; }
    public string? RibbonText { get; set; }
    public double? FloristConsideration { get; set; }
    public string? Type { get; set; }
    public string? OrderDetailIdParent { get; set; }
}
public class FileReferenceRaoModel
{
    public string? Entity { get; set; }

    public string? OrderId { get; set; }

    public string? FloristId { get; set; }

    public string? FileName { get; set; }

    public string? Url { get; set; }

    public DateTime? CreatedAt { get; set; }
}
