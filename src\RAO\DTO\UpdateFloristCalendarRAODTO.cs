﻿namespace ITF.SharedLibraries.RAO.DTO
{
    public class UpdateFloristCalendarRAODTO : Calendar
    {

    }
    public class Calendar
    {
        public string? CalendarId { get; set; } = string.Empty;
        public string? FloristId { get; set; } = string.Empty;
        public string? DayOfWeek { get; set; } = string.Empty;
        public string? ClosedWindow { get; set; } = string.Empty;
        public string? StartTime { get; set; } = string.Empty;
        public string? EndTime { get; set; } = string.Empty;
        public bool? GuaranteedDelivery { get; set; } = false;
        public int? MaxDistance { get; set; } = 0;
        public DateTime? ModifiedDate { get; set; } = DateTime.UtcNow;
        public string? ModifiedBy { get; set; } = string.Empty;
        public string? LunchBreakStartTime { get; set; } = string.Empty;
        public string? LunchBreakEndTime { get; set; } = string.Empty;
        public List<CalendarHistory>? CalendarHistories { get; set; } = new();
    }

    public class CalendarHistory
    {
        public string? CalendarHistoryId { get; set; } = string.Empty;
        public string? CalendarId { get; set; } = string.Empty;
        public string? FloristId { get; set; } = string.Empty;
        public string? DayOfWeek { get; set; } = string.Empty;
        public string? ClosedWindow { get; set; } = string.Empty;
        public string? StartTime { get; set; } = string.Empty;
        public string? EndTime { get; set; } = string.Empty;
        public bool? GuaranteedDelivery { get; set; } = false;
        public int? MaxDistance { get; set; } = 0;
        public DateTime? ModifiedDate { get; set; } = DateTime.UtcNow;
        public string? ModifiedBy { get; set; } = string.Empty;
    }
}
