﻿using commercetools.Sdk.Api.Models.Orders;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Orders
{
    public interface IOrderQueryService
    {
        Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrder(string orderNumber);
        Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrderById(string ctOrderId);
        Task<OrderPagedQueryResponse> GetTransmittedOrders(string floristId, int limit, int offset);
        Task<OrderPagedQueryResponse> GetOrdersHistory(string floristId, int limit, int offset);
        Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrderWithoutExpand(string orderNumber);
        Task<OrderPagedQueryResponse> GetToExecuteOrders(string floristId, bool sortByDeliveryDate, int limit);
        Task<OrderPagedQueryResponse> GetToExecuteOrdersTest(string floristId, OrderQueryService.SortOrdersBy sortOrdersBy, int limit);
    }
}
