﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class InvoicePayload : IPayload
    {
        public string FloristId { get; set; }
        public string OrderId { get; set; }
        public int Version { get; set; }
        public string Url { get; set; }
        public string AssignmentState { get; set; } // OrderAssignmentType => ASSIGNED / UPDATED / REJECTED / CANCELED
        public DateTime DeliveryDate { get; set; }
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
