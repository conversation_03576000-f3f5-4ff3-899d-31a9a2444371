﻿using System.Collections.Generic;
using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;
public class CartEmail
{
  public string CartId { get; set; }
  public string FloralProductType { get; set; }

  [SwaggerSchema("Is a mourning order")]
  public bool IsMourning { get; set; }
  public string TrackingPartnerSiteHighLevel { get; set; }
  public string TrackingPartnerSiteLowLevel { get; set; }

  [SwaggerSchema("Cart's Url")]
  public string Url { get; set; }
  public CustomerEmail Customer { get; set; } = new();
  public List<ProductEmail> ProductItems { get; set; } = new ();
  public List<VoucherEmail> Vouchers { get; set; } = new ();
}
