﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class GfsHolidaysPayload : IPayload
    {
        public string CountryCode { get; set; }
        public List<GfsHoliday> Holidays { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class GfsHoliday
    {
        public int? Id { get; set; }
        public string CountryCode { get; set; }
        public string Name { get; set; }
        public DateTime? BeginDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsTemplate { get; set; }
        public DateTime LastUpdate { get; set; }
    }
}
