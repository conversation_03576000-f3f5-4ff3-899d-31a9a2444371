﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.Synchronization.Payloads;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CatalogHeaderMessage : BaseMessage<CatalogHeaderPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.ProductNumber;
            }
        }
    }
}
