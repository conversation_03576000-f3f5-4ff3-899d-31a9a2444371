﻿using commercetools.Sdk.Api.Models.CartDiscounts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.DiscountCodes;

namespace IT.SharedLibraries.CT.DiscountCodes
{
    public interface IDiscountCodeService
    {
        void Initialize();

        Task<ICartDiscount> GetCartDiscountByKey(string cartDiscountKey);
        Task<ICartDiscount> GetCartDiscountById(string cartDiscountId);
        Task<IDiscountCode> CreateDiscountCodeFromDraft(IDiscountCodeDraft discountCodeDraft);
        Task<IDiscountCode> CreateDiscountCode(string prefix, int daysDelay, int useLimit, LocalizedString discountLocalizedName, string templateCartDiscountKey);
        Task<bool> SaveVoucherInCart(string cartId, string voucherCode, long cartVersion);
    }
}
