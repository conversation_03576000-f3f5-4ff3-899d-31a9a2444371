﻿global using ITF.Lib.Common.Error;
global using static ITF.Lib.Common.Error.ErrorUtils;
global using static ITF.Lib.Common.Error.ExceptionsUtils;

using System;
using System.Collections.Immutable;


namespace ITF.Lib.Common.Error;

//public class Error : IEquatable<Error>
//{
//    public static readonly Error None = new(string.Empty, string.Empty);
//    public static readonly Error NullValue = new("Error.NullValue", "The specified result value is null.");

//    public Error(string code, string message , Exception e)
//    {
//        Code = code;
//        Message = message;
//        E = e;
//    }

//    public Error(string code, string message)
//    {
//        Code = code;
//        Message = message;
//        E = default;
//    }

//    public string Code { get; }

//    public Exception E { get; }

//    public string Message { get; }

//    public static implicit operator string(Error error) => error.Code;

//    public static bool operator ==(Error a, Error b)
//    {
//        if (a is null && b is null)
//        {
//            return true;
//        }

//        if (a is null || b is null)
//        {
//            return false;
//        }

//        return a.Equals(b);
//    }

//    public static bool operator !=(Error a, Error b) => !(a == b);

//    public virtual bool Equals(Error other)
//    {
//        if (other is null)
//        {
//            return false;
//        }

//        return Code == other.Code && Message == other.Message;
//    }

//    public override bool Equals(object obj) => obj is Error error && Equals(error);

//    public override int GetHashCode() => HashCode.Combine(Code, Message);

//    public override string ToString() => Code + " : " + Message;
//}


public record NoError : BusinessError;
public record NullError : BusinessError;
public record ExceptionError : BusinessError;
public record CustomStringError(string Code) : BusinessError;

public abstract record InfrastructureError(ErrorType ErrorType = ErrorType.Business) : IError;
public abstract record BusinessError(ErrorType ErrorType = ErrorType.Business) : IError;
public abstract record LibraryError(ErrorType ErrorType = ErrorType.FromLibrary) : IError;
public abstract record ConfigurationError(ErrorType ErrorType = ErrorType.FromLibrary) : IError;

public enum ErrorType { Business, FromLibrary, Infrastructure, Configuration }

public interface IError
{
    public ErrorType ErrorType { get; init; }
}

public class Error : IEquatable<Error>
{
    public static readonly Error None = new(new NoError(), string.Empty);
    public static readonly Error NullValue = new(new NullError(), "The specified result value is null.");

    public IError Code { get; init; }
    public Exception? E { get; } = null;
    public string Message { get; } = string.Empty;
    public Error? InnerError { get; } = null;

    public Error(Exception e, string message = "", Error? innerError = null)
    {
        Code = new ExceptionError();
        Message = message;
        E = e;
        InnerError = innerError is null ? null : innerError;
    }

    public Error(IError code, string message = "", Error? innerError = null, Exception? e = null)
    {
        Code = code is null ? None.Code : code;
        Message = code is null ? None.Message : message;
        E = code is null ? null : e;
        InnerError = (code is null || innerError is null) ? null : innerError;
    }

    public Error(string code, string message = "", Error? innerError = null, Exception? e = null)
    {
        Code = string.IsNullOrWhiteSpace(code) ? None.Code : new CustomStringError(code);
        Message = string.IsNullOrWhiteSpace(code) ? None.Message : message;
        E = string.IsNullOrWhiteSpace(code) ? null : e;
        InnerError = (string.IsNullOrWhiteSpace(code) || innerError is null) ? null : innerError;
    }

    public Error(Error? innerError, string message, Exception? e = null)
    {
        Code = innerError is null ? None.Code : innerError.Code;
        Message = innerError is null ? None.Message : message;
        E = innerError is null ? null : e;
        InnerError = innerError is null ? null : innerError;
    }

    public static implicit operator string(Error? error) => error?.ToString() ?? "";
    public static implicit operator Error(string? message) => !string.IsNullOrWhiteSpace(message) ? new(new CustomStringError(message)) : None;

    public static bool operator ==(Error? a, Error? b)
    {
        if (a is null && b is null)
        {
            return true;
        }

        if (a is null || b is null)
        {
            return false;
        }

        return a.Equals(b);
    }
    public static bool operator !=(Error? a, Error? b) => !(a == b);

    // Warning here Equals take into Account Same Error Code Object and also same error Message !
    public virtual bool Equals(Error? other)
    {
        if (other is null)
        {
            return false;
        }

        return Code == other.Code && Message == other.Message;
    }

    public override bool Equals(object? obj) => obj is Error error && Equals(error);

    public override int GetHashCode() => HashCode.Combine(Code.ToString(), Message);

    public override string ToString() => E is null ?
        $"{Code} {((string.IsNullOrWhiteSpace(Message)) ? string.Empty : $" , CustomMessage = {Message}")} {(InnerError is null ? string.Empty : $" , InnerError = {InnerError} ")}" :
        $"{Code} , Exception = {E.GetAllExceptionMessages()} {(string.IsNullOrWhiteSpace(Message) ? string.Empty : $" , CustomMessage = {Message}")} {(InnerError is null ? string.Empty : $" , InnerError = {InnerError} ")}";

}

public static partial class ExceptionsUtils
{

    public static string GetAllExceptionMessages(this Exception? ex)
    {
        if (ex?.InnerException == null)
        {
            return ex?.Message ?? "";
        }
        return ex.Message + " " + GetAllExceptionMessages(ex.InnerException);
    }
}

public static partial class ErrorUtils
{

    //public static readonly IError ExceptionErrorValue = new ExceptionError();

    public static ImmutableList<Error> GetAllErrorsFromInnerError(this Error? e)
    {
        var errors = ImmutableList.Create(e ?? Error.None);

        if (e?.InnerError == null)
        {
            return errors;
        }
        return errors.AddRange(GetAllErrorsFromInnerError(e.InnerError));
    }
}
