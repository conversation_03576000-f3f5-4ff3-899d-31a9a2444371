﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Categories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Products
{
    public class CategoryService : ICategoryService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CategoryService> _logger;
        private readonly string _projectKey;

        public CategoryService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CategoryService> logger)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }


        /// <summary>
        /// Get a CommerceTools product category by it's key. Used to get accessories for exemple.
        /// </summary>
        /// <param name="key"></param>
        /// <returns>A CommerceTools product category of type ICategory</returns>
        public async Task<ICategory> GetByKey(string key)
        {
            ICategory category = null;
            try
            {
                category = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Categories()
                    .WithKey(key)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving category with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving category with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving category with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return category;
        }

        public async Task<IList<ICategory>> GetAll()
        {
            IList<ICategory> list = null;
            try
            {
                var result = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Categories()
                    .Get()
                    .ExecuteAsync();
                list = result.Results;
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading Categories, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading Categories because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return list;
        }

        public async Task<ICategory?> Create(ICategoryDraft categoryDraft)
        {
            ICategory product = null;
            try
            {
                product = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Categories()
                    .Post(categoryDraft)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {categoryDraft.Key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with key {categoryDraft.Key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with ikey {categoryDraft.Key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<ICategory> Update(ICategory existingCategory, List<ICategoryUpdateAction> updateActions)
        {

            ICategory updatedProduct = null;
            if (updateActions.Count > 0)
            {

                CategoryUpdate update = new CategoryUpdate()
                {
                    Version = existingCategory.Version,
                    Actions = updateActions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Categories()
                    .WithId(existingCategory.Id)
                    .Post(update)
                    .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "");
                    _logger.LogError(ex, $"Error while updating product {existingCategory.Key} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;


            throw new NotImplementedException();
        }
    }
}
