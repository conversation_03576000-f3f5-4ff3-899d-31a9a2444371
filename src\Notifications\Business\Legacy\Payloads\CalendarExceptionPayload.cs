﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class CalendarExceptionPayload : IPayload
    {
        public string Id { get; set; }
        public string FloristId { get; set; }
        public DateTime Date { get; set; }
        public string Reason { get; set; }
        public string Window { get; set; }
        public string State { get; set; }
        public DateTime? WorkHourOpening { get; set; }
        public DateTime? WorkHourClosing { get; set; }
        public string ActionType { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
