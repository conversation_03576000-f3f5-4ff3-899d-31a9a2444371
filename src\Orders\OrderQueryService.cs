﻿using Azure.Core;
using commercetools.Base.Client;
using commercetools.Sdk.Api.Client.RequestBuilders.InStore;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using ITF.SharedModels.Group.Enums;
using Microsoft.Extensions.Configuration;

namespace IT.SharedLibraries.CT.Orders
{
    public class OrderQueryService : IOrderQueryService
    {
        private readonly IConfiguration _configuration;
        private readonly string _projectKey;
        //private const string SORT_BY_DELIVERY_DATE = "shippingAddress.custom.fields.date desc";
        private const string SORT_BY_DELIVERY_DATE = "createdAt desc";
        private readonly string _countryStoreKey;
        private readonly ByProjectKeyInStoreKeyByStoreKeyOrdersRequestBuilder ordersRequestBuilder;

        public enum SortOrdersBy
        {
            DeliveryDate,
            InternalOrderId,
            Default
        }

        public OrderQueryService(
            IClient commerceToolsClient,
            IConfiguration configuration)
        {
            _configuration = configuration;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
            _countryStoreKey = _configuration.GetSection("CommerceToolCustomSettings:LocalCountryStoreKey").Value;
            ordersRequestBuilder = commerceToolsClient.WithApi().WithProjectKey(_projectKey).InStoreKeyWithStoreKeyValue(_countryStoreKey).Orders();
        }
        
        public async Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrder(string orderNumber)
        {
            return (commercetools.Sdk.Api.Models.Orders.Order) await ordersRequestBuilder
                .WithOrderNumber(orderNumber)
                .Get()
                .WithExpand("lineItems[*].productType.obj.name")
                .WithExpand("lineItems[*].variant.attributes[*].value[*]")
                //.WithExpand("shippingInfo.taxCategory")
                .ExecuteAsync();
        }

        public async Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrderById(string ctOrderId)
        {
            return (commercetools.Sdk.Api.Models.Orders.Order)await ordersRequestBuilder
                .WithId(ctOrderId)
                .Get()
                .WithExpand("lineItems[*].productType.obj.name")
                .WithExpand("lineItems[*].variant.attributes[*].value[*]")
                //.WithExpand("shippingInfo.taxCategory")
                .ExecuteAsync();
        }

        public async Task<commercetools.Sdk.Api.Models.Orders.Order> GetOrderWithoutExpand(string orderNumber)
        {
            return (commercetools.Sdk.Api.Models.Orders.Order) await ordersRequestBuilder
                .WithOrderNumber(orderNumber)
                .Get()
                .ExecuteAsync();
        }

        public async Task<OrderPagedQueryResponse> GetToExecuteOrders(string floristId, bool sortByDeliveryDate, int limit)
        {
            var deliveryDateLimit = DateTime.UtcNow.AddMonths(-4).ToString("yyyy-MM-dd");
            var where = $"custom(fields({CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID}=\"{floristId}\")) and " +
                $"shippingAddress(custom(fields(date >= \"{deliveryDateLimit}\"))) and " +
                $"custom(fields({CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS} in " +
                $"(\"{StatusEnum.ASSIGNED}\" , \"{StatusEnum.ACCEPTED}\", \"{StatusEnum.IN_PROGRESS}\", \"{StatusEnum.IN_DELIVERY}\",\"{StatusEnum.ABSENT}\")))";

            var request = ordersRequestBuilder
                    .Get()
                    .WithWhere(where)
                    .WithWithTotal(false)
                    .WithLimit(limit)
                    .WithExpand("lineItems[*].productType.obj.name")
                    .WithExpand("lineItems[*].variant.attributes[*].value[*]");

            if (sortByDeliveryDate)
            {
                request.WithSort(SORT_BY_DELIVERY_DATE);
            }

            return (OrderPagedQueryResponse)await request.ExecuteAsync();
        }

        public async Task<OrderPagedQueryResponse> GetToExecuteOrdersTest(string floristId, SortOrdersBy sortOrdersBy, int limit)
        {
            var where = $"custom(fields({CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID}=\"{floristId}\")) and " +
                $"custom(fields({CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS} in " +
                $"(\"{StatusEnum.ASSIGNED}\" , \"{StatusEnum.ACCEPTED}\", \"{StatusEnum.IN_PROGRESS}\", \"{StatusEnum.IN_DELIVERY}\",\"{StatusEnum.ABSENT}\")))";

            var request = ordersRequestBuilder
                    .Get()
                    .WithWhere(where)
                    .WithWithTotal(false)
                    .WithLimit(limit)
                    .WithExpand("lineItems[*].productType.obj.name")
                    .WithExpand("lineItems[*].variant.attributes[*].value[*]");

            if (sortOrdersBy == SortOrdersBy.DeliveryDate)
            {
                request.WithSort(SORT_BY_DELIVERY_DATE);
            }

            var result = (OrderPagedQueryResponse)await request.ExecuteAsync();
            List<IOrder> orders = (List<IOrder>)result.Results;
            orders.Sort((a, b) =>
            {
                if((OrderExtensionMethods.GetInternalOrderId(a) == null) && (OrderExtensionMethods.GetInternalOrderId(b) != null)) return -1;
                if((OrderExtensionMethods.GetInternalOrderId(a) != null) && (OrderExtensionMethods.GetInternalOrderId(b) == null)) return 1;
                if((OrderExtensionMethods.GetInternalOrderId(a) == null) && (OrderExtensionMethods.GetInternalOrderId(b) == null)) return 0;

                var arrA = OrderExtensionMethods.GetInternalOrderId(a).Split('-');
                var arrB = OrderExtensionMethods.GetInternalOrderId(b).Split('-');
                var keyA1 = Int32.Parse(arrA[0]);
                var keyA2 = Int32.Parse(arrA[1]);
                var keyB1 = Int32.Parse(arrB[0]);
                var keyB2 = Int32.Parse(arrB[1]);

                // Compare the 2 keys
                if (keyA1 > keyB1) return 1;
                if (keyA1 < keyB1) return -1;
                if (keyA2 > keyB2) return 1;
                if (keyA2 < keyB2) return -1;
                return 0;
            });
            result.Results = orders;
            return result;
        }

        public async Task<OrderPagedQueryResponse> GetTransmittedOrders(string floristId, int limit, int offset)
        {
            var where = $"custom(fields({CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID} = \"{floristId}\"))";

            return (OrderPagedQueryResponse) await ordersRequestBuilder
                .Get()
                .WithWhere(where)
                .WithSort(SORT_BY_DELIVERY_DATE)
                .WithWithTotal(false)
                .WithLimit(limit)
                .WithOffset(offset)
                .ExecuteAsync();
        }

        public async Task<OrderPagedQueryResponse> GetOrdersHistory(string floristId, int limit, int offset)
        {
            var where = $"custom(fields({CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID}=\"{floristId}\")) " +
                $"and custom(fields({CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS} in (\"{StatusEnum.DELIVERED}\", \"{StatusEnum.CANCELLED}\")))";

            return (OrderPagedQueryResponse) await ordersRequestBuilder
                .Get()
                .WithWhere(where)
                .WithSort(SORT_BY_DELIVERY_DATE)
                .WithWithTotal(false)
                .WithLimit(limit)
                .WithOffset(offset)
                .WithExpand("lineItems[*].productType.obj.name")
                .WithExpand("lineItems[*].variant.attributes[*].value[*]")
                .ExecuteAsync();
        }
    }
}
