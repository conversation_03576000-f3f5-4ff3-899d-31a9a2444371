﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.BusinessUnit;
public class BusinessUnitCreatedPayload : IPayload
{
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
    public BusinessUnitCreatedMessage BusinessUnitCreatedMessage { get; set; }
}
