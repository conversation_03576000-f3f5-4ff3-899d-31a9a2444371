﻿using ITF.SharedLibraries.HashicorpVault.ConfigurationProvider;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.HashicorpVault.Middleware
{
    public class HashicorpVaultMiddleware
    {
        readonly RequestDelegate _next;

        public HashicorpVaultMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            ConfigurationChangeObserver.Instance.OnChanged(new ConfigurationChangeEventArgs());
            context.Response.StatusCode = 200;
            await context.Response.WriteAsync("The Hashicorp Vault synchronization event has been sent.");
        }
    }
}
