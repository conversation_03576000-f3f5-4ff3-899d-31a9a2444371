﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS
{
    public class BackchargePayload : GFSPayloadBase, IPayload
    {
        public int BackchargeId { get; set; }
        public int Amount { get; set; }
        public int BackchargeType { get; set; }
        public int BackchargeState { get; set; }
        public string Reason { get; set; }
        public string ClearingPeriod { get; set; }
        public int FloristgateNumber { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
