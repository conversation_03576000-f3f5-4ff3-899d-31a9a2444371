﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public class CancelMiddleware
    {
        private readonly IServiceProvider _serviceProvider;

        public CancelMiddleware(RequestDelegate next, IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task Invoke(HttpContext context)
        {
            if (context is null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            var token = new System.Threading.CancellationToken();
            var backgroundService = _serviceProvider.GetRequiredService<AzureServiceBusSubscriber>();
            await backgroundService?.StopAsync(token);

            context.Response.StatusCode = 200;
            await context.Response.WriteAsync("Cancellation token has been sent to background task");
        }
    }
}
