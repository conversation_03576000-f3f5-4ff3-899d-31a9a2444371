﻿using commercetools.Sdk.Api.Models.BusinessUnits;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.BusinessUnit.Resource;

public class BusinessUnitResourceUpdatedPayload : IPayload
{
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
    public IBusinessUnit BusinessUnit { get; set; }
    public ResourceUpdatedDeliveryPayload ResourceUpdated { get; set; }
}
