﻿using Elastic.Apm.NetCoreAll;
using Elastic.Apm.SerilogEnricher;
using Elastic.CommonSchema.Serilog;
using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using Serilog.Sinks.Elasticsearch;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ITF.SharedLibraries.Logging;
using Microsoft.Extensions.Logging;
using CSharpFunctionalExtensions;
using Elastic.Apm;
using Elastic.CommonSchema;
using Agent = Elastic.Apm.Agent;
using Host = Microsoft.Extensions.Hosting.Host;

namespace ITF.SharedLibraries.HostBuilder.Extensions;

public static class CustomHostBuilder
{
    public static IHostBuilder CreateCustomHostBuilder(string[] args)
        => Host.CreateDefaultBuilder(args)
            .UseDefaultServiceProvider((context, options) => { options.ValidateScopes = true; })
            .ConfigureLogging(loggingBuilder => loggingBuilder.ClearProviders()) // get rid of default logs
            .ConfigureAppConfiguration((hostingContext, config) =>
            {
                if (Directory.Exists("settings/"))
                {
                    var files = Directory.GetFiles("settings/", "*.json");
                    files.ToList().ForEach(f =>
                    {
                        var fileinfo = new FileInfo(f);
                        if (fileinfo.Exists && fileinfo.Length > 0)
                            config.AddJsonFile(f, optional: true, reloadOnChange: true);
                    });
                }
            })
            .UseSerilog(new Action<HostBuilderContext, LoggerConfiguration>((h, l) =>
            {
                var configuration = h.Configuration.Get<Configuration>("ElasticSearchLog");
                var config = new EcsTextFormatterConfiguration
                {
                    MapCustom = (ecsDoc, logEvent) =>
                    {
                        ecsDoc.Service ??= new Service(); // Ensure Service is initialized
                        ecsDoc.Service.Name = Agent.Config.ServiceName; // Assign the service name
                        return ecsDoc;
                    }
                };

                l.ReadFrom.Configuration(h.Configuration)
                .Enrich.WithElasticApmCorrelationInfo()
                .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(configuration.ElasticSearchLog))
                {
                    AutoRegisterTemplate = true,
                    IndexFormat = "mslogs-{0:yyyy.MM.dd}",
                    DetectElasticsearchVersion = true,
                    RegisterTemplateFailure = RegisterTemplateRecovery.IndexAnyway,
                    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                    FailureCallback = (l,e) => Console.WriteLine($"Unable to submit event {l?.RenderMessage()} to ElasticSearch. Exception : " + e?.ToString()),
                    EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                                EmitEventFailureHandling.WriteToFailureSink |
                                EmitEventFailureHandling.RaiseCallback,
                    BufferCleanPayload = (failingEvent, statuscode, exception) =>
                    {
                        dynamic e = JObject.Parse(failingEvent);
                        return JsonConvert.SerializeObject(new Dictionary<string, object>()
                        {
                            { "action", "DeniedByElasticSearch"},
                            { "@timestamp",e["@timestamp"]},
                            { "level","Error"},
                            { "message","Error: "+e.message},
                            { "messageTemplate",e.messageTemplate},
                            { "failingStatusCode", statuscode},
                            { "failingException", exception}
                        });
                    },
                    CustomFormatter = new EcsTextFormatter(config)
                });
            }), writeToProviders: true) // fix https://github.com/elastic/apm-agent-dotnet/issues/1901
        .UseAllElasticApm();

    public static IHostBuilder ConfigureHostBuilder(this IHostBuilder host) =>
        host.UseDefaultServiceProvider((_, opts) => opts.ValidateScopes = true)
        .ConfigureAppConfiguration((_, conf) =>
        {
            Result.Try(() =>
            {
                if (Directory.Exists("settings/"))
                    Directory.GetFiles("settings/", "*.json").ToList().ForEach(f =>
                    {
                        var fInfo = new FileInfo(f);
                        var _ = fInfo.Exists && fInfo.Length > 0 ?
                            conf.AddJsonFile(f, optional: true, reloadOnChange: true) : null;
                    });
            }).TapError(e => Console.WriteLine($"Error when trying to add *.json config files into the IConfiguration : {e} "));
        })
        .ConfigureLogging(logging => logging.ClearProviders())
        .UseSerilog((h, l) =>
        {
            Result.Try(() =>
            {
                var config = new EcsTextFormatterConfiguration
                {
                    MapCustom = (ecsDoc, logEvent) =>
                    {
                        ecsDoc.Service ??= new Service(); // Ensure Service is initialized
                        ecsDoc.Service.Name = Agent.Config.ServiceName; // Assign the service name
                        return ecsDoc;
                    }
                };

                l.ReadFrom.Configuration(h.Configuration)
                .Enrich.WithElasticApmCorrelationInfo()
                .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(h.Configuration.Get<Configuration>("ElasticSearchLog").ElasticSearchLog))
                {
                    AutoRegisterTemplate = true,
                    IndexFormat = "mslogs-{0:yyyy.MM.dd}",
                    DetectElasticsearchVersion = true,
                    RegisterTemplateFailure = RegisterTemplateRecovery.IndexAnyway,
                    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                    FailureCallback = (l, e) => Console.WriteLine($"Unable to submit event {l?.RenderMessage()} to ElasticSearch. Exception : " + e?.ToString()),
                    EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                                EmitEventFailureHandling.WriteToFailureSink |
                                EmitEventFailureHandling.RaiseCallback,
                    BufferCleanPayload = (failingEvent, statuscode, exception) =>
                    {
                        dynamic e = JObject.Parse(failingEvent);
                        return JsonConvert.SerializeObject(new Dictionary<string, object>()
                        {
                                        { "action", "DeniedByElasticSearch"},
                                        { "@timestamp",e["@timestamp"]},
                                        { "level","Error"},
                                        { "message","Error: "+e.message},
                                        { "messageTemplate",e.messageTemplate},
                                        { "failingStatusCode", statuscode!},
                                        { "failingException", exception}
                        });
                    },
                    CustomFormatter = new EcsTextFormatter(config)
                });
            }).TapError(e => Console.WriteLine($"Error when trying to UseSerilog config extension method : {e}"));

        }, writeToProviders: true) // fix https://github.com/elastic/apm-agent-dotnet/issues/1901
        .UseAllElasticApm();
}

