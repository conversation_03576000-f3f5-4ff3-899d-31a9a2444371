﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Product
{
    public class ProductPriceDiscountsSetPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public ProductPriceDiscountsSetMessage ProductPriceDiscountsSetMessage { get; set; }
    }
}
