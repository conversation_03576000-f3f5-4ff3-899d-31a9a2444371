﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace ITF.SharedLibraries.HttpClient
{
    public interface IHttpClient
    {
        Authentication.Authentication _authentication { get; }
        static string _token { get; }
        System.Net.Http.HttpClient _client { get; }
        static DateTime _tokenExpiration { get; }
        Uri _uri { get; }
        string _endpoint { get; }
        Task<HttpResponseMessage> GetAsync(Dictionary<string, string> headers,string? endpoint = null, string? queryParameters = null , int maxRetries = 3);
        Task<HttpResponseMessage> GetAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3);
        Task<T?> GetAsync<T>(string? endpoint = null, string? queryParameters = null, SerializerType serializerType = SerializerType.TextJson);
        Task<string?> GetAsyncString(string? endpoint = null, string? queryParameters = null);
        Task<HttpResponseMessage> PostAsync(object objectToPost, string? endpoint = null, string? queryParameters = null, SerializerType serializerType = SerializerType.TextJson, string contentType = "application/json", JsonSerializerOptions? options = null, int maxRetries = 3);
        Task<HttpResponseMessage> PatchAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3);
        Task<HttpResponseMessage> PatchAsync(object objectToPatch, string? endpoint = null, string? queryParameters = null, SerializerType serializerType = SerializerType.TextJson , string contentType = "application/json", JsonSerializerOptions? options = null, int maxRetries = 3);
        Task<HttpResponseMessage> DeleteAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3);
        Task<HttpResponseMessage> PutAsync(string? endpoint = null, string? queryParameters = null, int maxRetries = 3);
        Task<HttpResponseMessage> PutAsync(object objectToPut, string? endpoint = null, string? queryParameters = null, SerializerType serializerType = SerializerType.TextJson, string contentType = "application/json", JsonSerializerOptions? options = null, int maxRetries = 3);
        Task<HttpResponseMessage> PostSoapAsync(string soapAction, string xml, string endpoint = null, string queryParameters = null);
        Task<T> ReadInformationsAsync<T>(HttpResponseMessage response, SerializerType serializerType = SerializerType.TextJson, JsonSerializerSettings settings = null) where T : class;
        Task<T> ReadODATAInformationsAsync<T>(HttpResponseMessage response, string tokenValue, SerializerType serializerType = SerializerType.TextJson) where T : class;
        void SetHeader(string key, string value);
        Task SetHeaderAsync(string key, string value);
    }
}