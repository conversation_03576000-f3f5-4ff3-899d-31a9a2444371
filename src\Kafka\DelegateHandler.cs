﻿using ITF.SharedLibraries.Kafka.Subscriber;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.Kafka
{
    public class DelegateHandler
    {
        public delegate void KafkaDelegateHandler(IServiceCollection services);

        public static void KafkaHandlerSupplier<TService, TImplementation>(IServiceCollection services)
            where TService : class, IMessageHandler
            where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }
    }
}
