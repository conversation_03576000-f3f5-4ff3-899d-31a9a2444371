﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.DomainDrivenDesign.Interfaces;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.MongoDB.Repository
{
    public abstract class MongoAggregateStoreRepository<T, TId> : MongoRepository<T>, IMongoAggregateStoreRepository<T, TId>
        where T : AggregateRoot<TId>
        where TId : IValueId
    {
        public MongoAggregateStoreRepository(IMongoClient mongoClient, string databaseName ,string collection) : base(mongoClient, databaseName , collection)
        {
        }

        public async Task<bool> Exists(TId aggregateId)
        {
            return await AnyAsync(aggregateId.AsString());
        }

        public async Task<T> Load(TId aggregateId)
        {
            var res = await FindByIdAsync(aggregateId.AsString());

            if (res is null)
                throw new InvalidOperationException($"MongoDb : Aggregate with id {aggregateId.AsString()} cannot be found");

            return res;
        }

        public async Task Remove(TId aggregateId)
        {
            await DeleteByIdAsync(aggregateId.AsString());
        }

        public async Task Save(T aggregate)
        {
            await InsertOneAsync(aggregate);
        }
    }
}
