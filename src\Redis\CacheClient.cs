﻿using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;
using Elastic.Apm.StackExchange.Redis;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace ITF.SharedLibraries.Redis
{
    public class CacheClient : ICacheClient
    {
        private readonly Lazy<IConnectionMultiplexer> _connection;
        private readonly Configuration _configuration;
        private readonly ILogger<CacheClient> _logger;

        public CacheClient(Configuration configuration, ILogger<CacheClient> logger)
        {
            _connection = new Lazy<IConnectionMultiplexer>(() =>
            {
                ConnectionMultiplexer redis = ConnectionMultiplexer.Connect(configuration.ConnectionString);
                return redis;
            });

            Connection.UseElasticApm();
            _configuration = configuration;
            _logger = logger;
        }

        public IConnectionMultiplexer Connection => _connection.Value;
        public IDatabase Database => Connection.GetDatabase();
        public IServer Server => Connection.GetServer(_configuration.ConnectionString);

        public async Task<int> RevokeAsync(string key, string pattern = null)
        {
            int nbDeleted = 0;

            try
            {
                if (Server != null)
                {
                    foreach (var storedKey in Server.Keys(pattern: pattern is null ? key : $"{key}{pattern}"))
                    {
                        if (await Database.KeyDeleteAsync(storedKey))
                            nbDeleted++;
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Can't delete {key} with pattern {pattern} from Redis", key, pattern);
            }

            return nbDeleted;
        }

        public T Get<T>(RedisKey key, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson)
        {
            try
            {
                RedisValue rv = Database.StringGet(key, flags);
                if (!rv.HasValue)
                    return default;
                return rv.ToString().Deserialize<T>(serializerType);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Can't get {key} from Redis", key);
                return default;
            }
        }

        public async Task<T> GetAsync<T>(RedisKey key, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson)
        {
            try
            {
                RedisValue rv = await Database.StringGetAsync(key, flags);
                if (!rv.HasValue)
                    return default;
                return rv.ToString().Deserialize<T>(serializerType);
            }
            catch(Exception e)
            {
                _logger.LogError(e, "Can't get {key} from Redis", key);
                return default;
            }
        }

        public bool Set(RedisKey key, object value, TimeSpan? expiry = null, When when = When.Always, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson)
        {
            try
            {
                if (value == null) return false;
                return Database.StringSet(key, value.Serialize(serializerType), expiry, when, flags);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Can't set {key} to Redis", key);
                return false;
            }
        }

        public async Task<bool> SetAsync(RedisKey key, object value, TimeSpan? expiry = null, When when = When.Always, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson)
        {
            try
            {
                if (value == null) return false;
                return await Database.StringSetAsync(key, value.Serialize(serializerType), expiry, when, flags);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Can't set {key} to Redis", key);
                return false;
            }
        }
    }
}
