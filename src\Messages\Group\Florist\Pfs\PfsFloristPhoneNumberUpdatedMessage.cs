﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;
using System;

namespace ITF.SharedModels.Messages.Group.Florist.Pfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristPhoneNumberUpdatedMessage : BaseMessage<PfsFloristPhoneNumberUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier + "_" + Payload?.PhoneNumber;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristPhoneNumberUpdatedMessage() { }


                public PfsFloristPhoneNumberUpdatedMessage(string floristIdentifier, string phone) 
                { 
                    this.Payload = new PfsFloristPhoneNumberUpdatedPayload { FloristIdentifier = floristIdentifier, PhoneNumber = phone };
                }
            }

            
        }
    }

    public class PfsFloristPhoneNumberUpdatedPayload : LegacyPayload, IEquatable<PfsFloristPhoneNumberUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string PhoneNumber { get; set; }

        public bool Equals(PfsFloristPhoneNumberUpdatedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                PhoneNumber == parameter.PhoneNumber
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristPhoneNumberUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            PhoneNumber
        }.GetHashCode();
    }
}
