﻿namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderItemExecutorAmountUpdated
    {
        public string OrderIdentifier { get; set; }
        public string ProductKey { get; set; }
        public string VariantKey { get; set; }
        public decimal ExecutorAmount { get; set; }

        public static implicit operator GlobalOrderItemExecutorAmountUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemExecutorAmountUpdatedMessage v)
        {
            return new GlobalOrderItemExecutorAmountUpdated
            {
                ExecutorAmount = v?.Payload?.ExecutorAmount ?? 0,
                OrderIdentifier = v?.Payload.OrderIdentifier,
                ProductKey = v?.Payload.ProductKey,
                VariantKey = v?.Payload.VariantKey,

            };
        }
    }
}
