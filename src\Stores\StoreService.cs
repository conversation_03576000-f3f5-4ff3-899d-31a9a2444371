﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Stores;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Stores
{
    public class StoreService : IStoreService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<StoreService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public StoreService(IClient commerceToolsClient, IConfiguration configuration, ILogger<StoreService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }
        public async Task<IStore> GetByKey(string storeKey)
        {
            IStore channel = null;
            try
            {
                var resultSet = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Stores()
                    .Get()
                    .WithWhere($"key = \"{storeKey}\"")
                    .ExecuteAsync();

                if (resultSet != null)
                {
                    channel = resultSet.Results[0];
                }
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading store with key={storeKey}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading store with key={storeKey} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return channel;
        }
    }
}
