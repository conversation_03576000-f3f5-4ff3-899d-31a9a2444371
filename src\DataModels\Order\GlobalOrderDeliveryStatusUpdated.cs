﻿using ITF.SharedModels.Group.Enums;
using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveryStatusUpdated
    {
        public string OrderIdentifier { get; set; }
        public DeliveryStatusEnum DeliveryStatus { get; set; }

        public static implicit operator GlobalOrderDeliveryStatusUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryStatusUpdatedMessage v)
        {
            return new GlobalOrderDeliveryStatusUpdated
            {
                DeliveryStatus = v?.Payload?.DeliveryStatus ?? DeliveryStatusEnum.SCHEDULED,
                OrderIdentifier = v?.Payload.OrderIdentifier,

            };
        }
    }
}
