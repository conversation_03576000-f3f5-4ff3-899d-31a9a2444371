﻿using ITF.Lib.Common.DomainDrivenDesign;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.DataModels.Order
{
    public class OrderClaim : BaseClass<string>
    {
        public DateTime DeliveryDate { get; set; }
        public string OrderId { get; set; } = string.Empty;
        public string FloristId { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string ProductSize { get; set; } = string.Empty;
        public string ProductStyle { get; set; } = string.Empty;
        public string ReclamationCode { get; set; } = string.Empty;
        public string ReclamationReason { get; set; } = string.Empty;

        public override void SetId()
        {
            
        }
    }
}
