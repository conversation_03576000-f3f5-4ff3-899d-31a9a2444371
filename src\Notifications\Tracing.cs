﻿using ITF.Lib.Common.Notifications.Messages;

namespace ITF.Lib.Common.Notifications
{
    public static class Tracing
    {
        public static string? GetTracingData(object? data)
        {
            if (data is null)
                return null;

            try
            {
                var converted = data as IDistributedTracing;
                return converted?.DistributedTracingData;
            }
            catch { }

            return null;
        }
    }
}
