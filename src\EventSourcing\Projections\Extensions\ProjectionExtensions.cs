﻿using ITF.SharedLibraries.EventSourcing.EventStore;
using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using static ITF.SharedLibraries.EventSourcing.Projections.DelegateHandler;
using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using ITF.SharedLibraries.EventSourcing.Projections.Services;
using EventStore.Client;

namespace ITF.SharedLibraries.EventSourcing.Projections.Extensions
{
    public static class ProjectionExtensions
    {
        public static IServiceCollection AddEventStoreProjectionsManager(this IServiceCollection services, IConfiguration config, ProjectionHandler checkPointHandler, string varEnv = "EventStore", params ProjectionHandler[] projectionDelegateHandlers)
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);

            var settings = EventStoreClientSettings.Create(configuration.ConnectionString);
            var client = new EventStoreClient(settings);

            services.AddSingleton(client);

            checkPointHandler.Invoke(services);
            projectionDelegateHandlers.ToList().ForEach(h =>
            {
                // Resolve the promise
                h.Invoke(services);
            });
            services.AddSingleton<ISubscriptionManager, EventStoreProjectionManager>();
            // Allows to access the background service as singleton and stop it
            services.AddSingleton<EventSubscriberService>();
            return services;
        }

        public static IServiceCollection AddEventSourcingSubscriberBackgroundWorker(this IServiceCollection services)
            => services.AddHostedService(provider => provider.GetService<EventSubscriberService>());
    }
}
