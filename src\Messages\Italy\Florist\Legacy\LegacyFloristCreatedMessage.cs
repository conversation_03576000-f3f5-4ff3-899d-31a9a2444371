﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using ITF.SharedModels.Notifications.Business.Synchronization.Payloads;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using static ITF.SharedModels.Messages.Italy.Florist.Legacy.Messages.V1;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristCreatedMessage : BaseMessage<LegacyFloristCreatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyFloristCreatedMessage((FloristHeaderMessage message, FloristContactsMessage contacts)src)
                {
                    var payload = src.message.Payload;
                    var legacyFloristCreatedMessage = new LegacyFloristCreatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyFloristCreatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            UnitCountryCode = payload.Location.CountryCode,
                            FloristIdentifier = payload.FloristId,
                            PersonalCode = payload.FloristId,
                            Calendar = new(),
                            SpecialDays = new(),
                            FloristActivity = new LegacyFloristActivity
                            {
                                AgreementOn = payload.InitialisationExecutionDate,
                                BlockedInExecution = payload.Group == "AVB",
                                BlockedInTransmission = payload.IsSuspended,
                                Deleted = payload.AgreementStopDate >= DateTime.Now,
                                LastReinstateOn = payload.SuspensionEndDate,
                                LastSuspensionOn = payload.SuspensionStartDate,
                                LastSuspensionReason = string.Empty
                            },
                            Documents = new(),
                            ShopLocation = new LegacyShopLocation
                            {
                                CountryCode = payload.Location.CountryCode,
                                City = payload.Location.City,
                                Gps = new LegacyCoordinates { Latitude = payload.Location.Latitude, Longitude = payload.Location.Longitude },
                                Name = payload.Name,
                                Province = string.Empty,
                                Street = payload.Location.Street,
                                ZipCode = payload.Location.ZipCode
                            },
                            Accessories = new(),
                            Contacts = src.contacts?.Payload?.FloristContacts?.Select(contact => (LegacyContact)contact).ToList() ?? new(),
                            Attributes = new(),
                            Credentials = new(),
                            SubCodes = new(),
                            Password = string.Empty

                        }
                    };


                    return legacyFloristCreatedMessage;
                }
            }
        }
    }

    public class LegacyFloristCreatedPayload : LegacyPayload, IEquatable<LegacyFloristCreatedPayload>
    {
        public string UnitCountryCode { get; set; }
        public string FloristIdentifier { get; set; }
        public string PersonalCode { get; set; }
        public LegacyFloristActivity FloristActivity { get; set; }
        public List<LegacyContact> Contacts { get; set; } = new();
        public List<LegacyAccessory> Accessories { get; set; } = new();
        public List<LegacyDocument> Documents { get; set; } = new();
        public LegacyShopLocation ShopLocation { get; set; } = new();
        public List<LegacyDailyCalendar> Calendar { get; set; } = new();
        public List<LegacySpecialDay> SpecialDays { get; set; } = new();
        public List<LegacyFloristCredential> Credentials { get; set; } = new();
        /// <summary>
        /// List of country specific fields
        /// </summary>
        public Dictionary<string, string> Attributes { get; set; } = new();
        public string Password { get; set; } 
        public List<string> SubCodes { get; set; } = new();

        public bool Equals(LegacyFloristCreatedPayload parameter)
        {
            return (UnitCountryCode == parameter.UnitCountryCode &&
                FloristIdentifier == parameter.FloristIdentifier &&
                FloristActivity.Equals(parameter.FloristActivity) &&
                Contacts.Equals(parameter.Contacts) &&
                ShopLocation.Equals(parameter.ShopLocation) &&
                Accessories.Equals(parameter.Accessories) &&
                SpecialDays.Equals(parameter.SpecialDays) &&
                Calendar.Equals(parameter.Calendar) &&
                Documents.Equals(parameter.Documents) &&
                Password.Equals(parameter.Password) &&
                SubCodes.Equals(parameter.SubCodes)
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristCreatedPayload);
        }

        public override int GetHashCode() => new
        {
            UnitCountryCode,
            FloristIdentifier,
            FloristActivity,
            Contacts,
            ShopLocation,
            Calendar,
            Accessories,
            SpecialDays,
            Documents,
            Password,
            SubCodes
        }.GetHashCode();
    }

    public class LegacyCoordinates
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public bool Equals(LegacyCoordinates parameter)
        {
            return (Latitude == parameter.Latitude &&
                Longitude == parameter.Longitude
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyCoordinates);
        }
        public override int GetHashCode() => new
        {
            Latitude,
            Longitude
        }.GetHashCode();
    }

    public class LegacyShopLocation
    {
        public string Name { get; set; }
        public string Street { get; set; }
        public string ZipCode { get; set; }
        public string City { get; set; }
        public string Province { get; set; }
        public string CountryCode { get; set; }
        public LegacyCoordinates Gps { get; set; } = new();
        public bool Equals(LegacyShopLocation parameter)
        {
            return (Name == parameter.Name &&
                Street == parameter.Street &&
                ZipCode == parameter.ZipCode &&
                City == parameter.City &&
                Province == parameter.Province &&
                CountryCode == parameter.CountryCode &&
                Gps == parameter.Gps
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyShopLocation);
        }
        public override int GetHashCode() => new
        {
            Name,
            Street,
            ZipCode,
            City,
            Province,
            CountryCode,
            Gps
        }.GetHashCode();
    }

    public class LegacyContact
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public ContactTypeEnum Type { get; set; }
        public string Entry { get; set; }

        public static implicit operator LegacyContact(FloristContact contact)
        {
            return new LegacyContact
            {
                Type = contact.Type switch
                {
                    "Phone" => ContactTypeEnum.Phone,
                    "URL" => ContactTypeEnum.Unknown,
                    "Email" => ContactTypeEnum.SecondMail,
                    _ => ContactTypeEnum.Unknown
                },
                Entry = contact.Value
            };
        }
        public bool Equals(LegacyContact parameter)
        {
            return (Type == parameter.Type &&
                Entry == parameter.Entry
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyContact);
        }
        public override int GetHashCode() => new
        {
            Type,
            Entry
        }.GetHashCode();
    }

    public class LegacyAccessory
    {
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public bool InStock { get; set; }

       
        public bool Equals(LegacyAccessory parameter)
        {
            if (parameter == null) return false;

            return (ProductCode == parameter.ProductCode &&
                ProductName == parameter.ProductName &&
                InStock == parameter.InStock
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyAccessory);
        }
        public override int GetHashCode() => new
        {
            ProductCode,
            ProductName,
            InStock
        }.GetHashCode();
    }

    public class LegacyDocument
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public DocTypeEnum DocType { get; set; }
        public string OrderReference { get; set; }
        public string FileName { get; set; }
        public string FileExtension { get; set; }
        public string Url { get; set; } // url to call to get file bytes
        public int Year { get; set; }
        public int Month { get; set; }
        public bool Equals(LegacyDocument parameter)
        {
            if (parameter == null) return false;

            return (DocType == parameter.DocType &&
                OrderReference == parameter.OrderReference &&
                FileName == parameter.FileName &&
                FileExtension == parameter.FileExtension &&
                Url == parameter.Url &&
                Year == parameter.Year &&
                Month == parameter.Month
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyDocument);
        }
        public override int GetHashCode() => new
        {
            DocType,
            OrderReference,
            FileName,
            FileExtension,
            Url,
            Year,
            Month
        }.GetHashCode();
    }

    public class LegacyDailyCalendar
    {
        public DayOfWeek DayOfWeek { get; set; }
        public TimeSpan? OpenHour { get; set; }
        public TimeSpan? CloseHour { get; set; }
        public bool IsBreakTimePresent { get; set; }
        public TimeSpan? BreakTimeStart { get; set; }
        public TimeSpan? BreakTimeEnd { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public DailyCalendarTypeEnum Type { get; set; }
        public bool Equals(LegacyDailyCalendar parameter)
        {
            if (parameter == null) return false;

            return (DayOfWeek == parameter.DayOfWeek &&
                OpenHour == parameter.OpenHour &&
                CloseHour == parameter.CloseHour &&
                IsBreakTimePresent == parameter.IsBreakTimePresent &&
                BreakTimeStart == parameter.BreakTimeStart &&
                BreakTimeEnd == parameter.BreakTimeEnd &&
                Type == parameter.Type
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyDailyCalendar);
        }
        public override int GetHashCode() => new
        {
            DayOfWeek,
            OpenHour,
            CloseHour,
            IsBreakTimePresent,
            BreakTimeStart,
            BreakTimeEnd,
            Type
        }.GetHashCode();
    }

    public class LegacySpecialDay
    {
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public SpecialDayTypeEnum Type { get; set; }
        public bool Equals(LegacySpecialDay parameter)
        {
            if (parameter == null) return false;

            return (Start == parameter.Start &&
                End == parameter.End &&
                Type == parameter.Type
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacySpecialDay);
        }
        public override int GetHashCode() => new
        {
            Start,
            End,
            Type
        }.GetHashCode();
    }

    public class LegacyFloristActivity
    {
        public DateTime AgreementOn { get; set; }
        public string LastSuspensionReason { get; set; }
        public DateTime? LastSuspensionOn { get; set; }
        public DateTime? LastReinstateOn { get; set; }
        public bool BlockedInExecution { get; set; } = false;
        public bool BlockedInTransmission { get; set; } = false;
        public bool Deleted { get; set; } = false;
        public bool Equals(LegacyFloristActivity parameter)
        {
            if (parameter == null) return false;

            return (AgreementOn == parameter.AgreementOn &&
                LastSuspensionReason == parameter.LastSuspensionReason &&
                LastSuspensionOn == parameter.LastSuspensionOn &&
                LastReinstateOn == parameter.LastReinstateOn &&
                BlockedInExecution == parameter.BlockedInExecution &&
                BlockedInTransmission == parameter.BlockedInTransmission &&
                Deleted == parameter.Deleted
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristActivity);
        }
        public override int GetHashCode() => new
        {
            AgreementOn,
            LastSuspensionReason,
            LastSuspensionOn,
            LastReinstateOn,
            BlockedInExecution,
            BlockedInTransmission,
            Deleted
        }.GetHashCode();
    }

    //to remove
    public class LegacyFloristCredential
    {
        public string CredentialType { get { return "password"; } }
        public string Password { get; set; }
    }


}
