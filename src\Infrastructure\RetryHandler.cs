﻿using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure;

public class RetryConfiguration
{
    public int MaxAttempts { get; set; } = 5;
    public int BaseDelayMs { get; set; } = 100;
    public bool UseExponentialBackoff { get; set; } = true;
    public int MaxDelayMs { get; set; } = 3000;
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool UseJitter { get; set; } = true;
}

public class RetryHandler(ILogger logger, RetryConfiguration? config = null)
{
    private readonly RetryConfiguration config = config ?? new RetryConfiguration();
    private readonly Random random = new();

    /// <summary>
    /// Handles retry logic for operations that might encounter ConcurrentModificationException
    /// </summary>
    /// <typeparam name="TResult">The return type of the operation</typeparam>
    /// <param name="operation">The operation to execute</param>
    /// <param name="operationName">Name of the operation for logging</param>
    /// <returns>Result of the operation</returns>
    public async Task<TResult> ExecuteWithRetry<TResult>(
        Func<Task<TResult>> operation,
        string operationName = "operation")
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        Exception? lastException = null;

        for (int attempt = 1; attempt <= config.MaxAttempts; attempt++)
        {
            try
            {
                if (attempt == 1)
                {
                    logger.LogDebug("Attempting {OperationName}", operationName);
                }
                else
                {
                    logger.LogDebug("Attempting {OperationName} (retry {Attempt}/{MaxAttempts})",
                        operationName, attempt - 1, config.MaxAttempts - 1);
                }

                var result = await operation();

                if (attempt == 1)
                {
                    logger.LogDebug("{OperationName} completed successfully in {ElapsedMs}ms",
                        operationName, stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    logger.LogInformation("{OperationName} retry {Attempt} succeeded after {TotalElapsedMs}ms",
                        operationName, attempt - 1, stopwatch.ElapsedMilliseconds);
                }

                return result;
            }
            catch (Exception ex) when (ex is commercetools.Base.Client.Error.ConcurrentModificationException || ex is CtCustomException)
            {
                lastException = ex;

                if (attempt == config.MaxAttempts)
                {
                    logger.LogError(ex, "{OperationName} failed after {MaxAttempts} attempts and {TotalElapsedMs}ms due to concurrent modification",
                        operationName, config.MaxAttempts, stopwatch.ElapsedMilliseconds);
                    throw;
                }

                var delay = CalculateDelay(attempt);
                logger.LogWarning("409 Concurrent modification detected on attempt {Attempt} after {ElapsedMs}ms, waiting {DelayMs}ms before retry {NextAttempt}: {Message}",
                    attempt, stopwatch.ElapsedMilliseconds, delay, attempt, ex.Message);

                await Task.Delay(delay);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "{OperationName} failed on attempt {Attempt} after {ElapsedMs}ms",
                    operationName, attempt, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        // This should never be reached due to the throw in the catch block above
        throw lastException ?? new InvalidOperationException("Retry loop completed without result");
    }

    private int CalculateDelay(int attempt)
    {
        if (!config.UseExponentialBackoff)
        {
            return config.BaseDelayMs;
        }

        // Calculate exponential backoff: baseDelay * (multiplier ^ (attempt - 1))
        var delay = (int)(config.BaseDelayMs * Math.Pow(config.BackoffMultiplier, attempt - 1));

        // Apply maximum delay limit
        delay = Math.Min(delay, config.MaxDelayMs);

        // Add jitter to prevent thundering herd
        if (config.UseJitter)
        {
            var jitter = random.Next(0, (int)(delay * 0.1)); // 10% jitter
            delay += jitter;
        }

        return delay;
    }
}

// Extension methods for easier usage
public static class RetryHandlerExtensions
{
    /// <summary>
    /// Handle 409 conflicts with retry for single parameter methods
    /// </summary>
    public static async Task<IOrder> Handle409Conflict<T>(
        this RetryHandler retryHandler,
        Func<T, Task<IOrder>> method,
        T param,
        string operationName = "order update")
    {
        return await retryHandler.ExecuteWithRetry(
            () => method(param),
            operationName);
    }

    /// <summary>
    /// Handle 409 conflicts with retry for two parameter methods
    /// </summary>
    public static async Task<IOrder> Handle409Conflict<T, T1>(
        this RetryHandler retryHandler,
        Func<T, T1, Task<IOrder>> method,
        T param,
        T1 param2,
        string operationName = "order update")
    {
        return await retryHandler.ExecuteWithRetry(
            () => method(param, param2),
            operationName);
    }

    /// <summary>
    /// Handle 409 conflicts with retry for three parameter methods
    /// </summary>
    public static async Task<IOrder> Handle409Conflict<T, T1, T2>(
        this RetryHandler retryHandler,
        Func<T, T1, T2, Task<IOrder>> method,
        T param,
        T1 param2,
        T2 param3,
        string operationName = "order update")
    {
        return await retryHandler.ExecuteWithRetry(
            () => method(param, param2, param3),
            operationName);
    }

    /// <summary>
    /// Handle 409 conflicts with retry for four parameter methods
    /// </summary>
    public static async Task<IOrder> Handle409Conflict<T, T1, T2, T3>(
        this RetryHandler retryHandler,
        Func<T, T1, T2, T3, Task<IOrder>> method,
        T param,
        T1 param2,
        T2 param3,
        T3 param4,
        string operationName = "order update")
    {
        return await retryHandler.ExecuteWithRetry(
            () => method(param, param2, param3, param4),
            operationName);
    }
    /// <summary>
    /// Generic retry handler for any operation
    /// </summary>
    public static async Task<TResult> Handle409Conflict<TResult>(
        this RetryHandler retryHandler,
        Func<Task<TResult>> operation,
        string operationName = "operation")
    {
        return await retryHandler.ExecuteWithRetry(operation, operationName);
    }
}
