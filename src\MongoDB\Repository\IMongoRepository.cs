﻿using ITF.Lib.Common.DomainDrivenDesign;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.MongoDB.Repository
{
    public interface IMongoRepository<T> where T : BaseClass<string>
    {
        void WarmUp();
        IQueryable<T> AsQueryable();
        Task<IEnumerable<T>> GetAll();
        Task<IEnumerable<T>> FilterByAsync(Expression<Func<T, bool>> filterExpression);
        Task<IEnumerable<T>> FilterByAsync(FilterDefinition<T> filterDefinition);
        Task<T> FilterOneByAsync(Expression<Func<T, bool>> filterExpression);
        Task<T> FilterOneByAsync(FilterDefinition<T> filterDefinition);
        Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            Expression<Func<T, bool>> filterExpression,
            Expression<Func<T, TProjected>> projectionExpression);
        Task<IEnumerable<TProjected>> FilterByWithProjection<TProjected>(
            FilterDefinition<T> filterDefinition,
            Expression<Func<T, TProjected>> projectionExpression);
        Task<T> FindByIdAsync(string id);
        Task InsertOneAsync(T document);
        Task InsertManyAsync(ICollection<T> documents);
        Task ReplaceOneAsync(T document , bool insert = true);
        Task DeleteOneAsync(Expression<Func<T, bool>> filterExpression);
        Task DeleteOneAsync(FilterDefinition<T> filterDefinition);
        Task DeleteByIdAsync(string id);
        Task DeleteManyAsync(Expression<Func<T, bool>> filterExpression);
        Task DeleteManyAsync(FilterDefinition<T> filterDefinition);
        Task<long> CountAsync(Expression<Func<T, bool>> filterExpression);
        Task<long> CountAsync(FilterDefinition<T> filterDefinition);
        Task<bool> AnyAsync(Expression<Func<T, bool>> filterExpression);
        Task<bool> AnyAsync(FilterDefinition<T> filterDefinition);
        Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(FilterDefinition<T> filterDefinition, SortDefinition<T> sortDefinition, int page, int pageSize);
        Task<(int totalPages, IReadOnlyList<T> data)> AggregateByPage(Expression<Func<T, bool>> filterExpression, SortDefinition<T> sortDefinition, int page, int pageSize);
    }
}