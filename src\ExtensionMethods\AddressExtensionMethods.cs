﻿using commercetools.Sdk.Api.Models.Common;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedModels.Group.Enums;
using System.Globalization;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class AddressExtensionMethods
    {
        public static TimeSpan? GetTime(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.TIME))
            {
                string timeString = address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.TIME].ToString();
                TimeSpan result = TimeSpan.Zero;

                TimeSpan.TryParseExact(timeString, "c", CultureInfo.CurrentCulture, out result);
                return result;
            }
            return null;
        }
        public static string GetComments(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.COMMENTS))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.COMMENTS].ToString();
            }
            return null;
        }
        public static string GetContactTitle(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.CONTACT_TITLE))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.CONTACT_TITLE].ToString();
            }
            return null;
        }
        public static string GetContactFirstName(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.CONTACT_FIRSTNAME))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.CONTACT_FIRSTNAME].ToString();
            }
            return null;
        }
        public static string GetCompanyNumber(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.BillingAddress.COMPANY_NUMBER))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.BillingAddress.COMPANY_NUMBER].ToString();
            }
            return null;
        }
        public static AddressTypeEnum GetAddressType(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.BillingAddress.TYPE) &&
                Enum.TryParse(address.Custom.Fields[CtOrderCustomAttributesNames.BillingAddress.TYPE].ToString(), out AddressTypeEnum type))
            {
                return type;
            }
            return AddressTypeEnum.INDIVIDUAL;
        }
        public static string GetContactLastName(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.CONTACT_LASTNAME))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.CONTACT_LASTNAME].ToString();
            }
            return null;
        }
        public static string GetContactPhone(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.CONTACT_PHONE))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.CONTACT_PHONE].ToString();
            }
            return null;
        }
        public static string GetInvoiceRefernce(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.BillingAddress.INVOICE_REFERENCE))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.BillingAddress.INVOICE_REFERENCE].ToString();
            }
            return null;
        }
        public static string GetCostCenter(this IAddress address)
        {
            if (address.Custom != null && address.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.BillingAddress.COST_CENTER))
            {
                return address.Custom.Fields[CtOrderCustomAttributesNames.BillingAddress.COST_CENTER].ToString();
            }
            return null;
        }
    }
}
