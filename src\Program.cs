using ITF.SharedLibraries.HostBuilder.Extensions;
using ITF.SharedLibraries.Kafka.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace IT.Microservices.OrderReactor
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            return CustomHostBuilder.CreateCustomHostBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                }).ConfigureServices(services => services.AddKafkaSubscribersBackgroundWorker<string, string>());
        }
    }
}
