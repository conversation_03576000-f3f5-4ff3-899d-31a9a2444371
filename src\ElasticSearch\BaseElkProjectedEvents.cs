﻿using System;
using System.Collections.Generic;

namespace ITF.SharedLibraries.ElasticSearch
{
    public abstract class BaseElkProjectedEvents<T> : BaseElastic<T> where T : class
    {
        public BaseElkProjectedEvents()
        {
            LastProjectedIds = new List<string>();
        }

        public List<string> LastProjectedIds { get; set; }
        public DateTime LastUpdate { get; set; }

        public bool EventHasAlreadyBeenProjected(string messageId)
        {
            if (LastProjectedIds.Count == 0 || !LastProjectedIds.Contains(messageId?.ToLower()))
                return false;

            return true;
        }

        public void StoreProjectedEvent(string messageId, int retention = 50)
        {
            if (LastProjectedIds.Count < retention)
                LastProjectedIds.Add(messageId.ToLower());
            else
            {
                LastProjectedIds.RemoveAt(0);
                LastProjectedIds.Add(messageId.ToLower());
            }
        }
        public void SetLastUpdate()
        {
            LastUpdate = DateTime.Now;
        }
    }
}
