﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos.Table;

namespace ITF.SharedLibraries.AzureTable
{
    public interface IAzureTableRepository<T> where T : TableEntity, new()
    {
        Task ConfigureAsync(string connexionString, string partitionKey, string table);
        Task ConfigureAsync(string envVariable);
        Task ConfigureAsync(Configuration configuration);
        Task<T> DeleteByRowKeyAsync(T entity);
        Task<List<T>> GetAsync(string query, string partitionKey = null);
        Task<T> GetByRowKeyAsync(string rowKey, string partitionKey = null);
        Task<TableResult> InsertOrUpdateAsync(T entity);
        Task DeleteAllAsync(string partitionKey);
    }
}