﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class CatalogBundledItemPayload : IPayload
    {
        public List<CatalogBundledItem> CatalogBundledItems { get; set; } = new List<CatalogBundledItem>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
    public class CatalogBundledItem
    {
        public string ProductNumber { get; set; }
        public string Unit { get; set; }
        public string ItemReference { get; set; }
        public bool Blocked { get; set; }
        public int Quantity { get; set; }
        public DateTime LastModified { get; set; }
    }
}
