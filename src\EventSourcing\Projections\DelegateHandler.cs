﻿using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.EventSourcing.Projections
{
    public class DelegateHandler
    {
        public delegate void ProjectionHandler(IServiceCollection services);

        public static void ProjectionHandlerSupplier<TService, TImplementation>(IServiceCollection services)
        where TService : class, IProjection
        where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }

        public static void EventStoreCheckpointHandlerSupplier<TService, TImplementation>(IServiceCollection services)
        where TService : class, IEventStoreCheckpointStore
        where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }

        public static void MartenCheckpointHandlerSupplier<TService, TImplementation>(IServiceCollection services)
        where TService : class, IMartenCheckpointStore
        where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }
    }
}
