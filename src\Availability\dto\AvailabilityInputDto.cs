﻿namespace ITF.SharedLibraries.Availability.dto
{
    public class AvailabilityInputDto
    {
        public required string ProductSKU { get; set; }
        public required List<string> ProductSKUs { get; set; }
        public required string PostalCode { get; set; }
        public required string CountryCode { get; set; }
        public required DateTime DeliveryDate { get; set; }

        public string ProductVariantSKU { get; set; }
        public List<string> ProductVariantSKUs { get; set; }

        // Optional fields
        public string City { get; set; }
        public string Street { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string OrderId { get; set; }
        public DeliveryMoment? Moment { get; set; }
        public bool? IsFuneral { get; set; }
        public bool? HasRibbon { get; set; }
        public string Province { get; set; }

        public List<string>? FloristId { get; set; }
        public string? BusinessUnitId { get; set; }
    }

    public enum DeliveryMoment
    {
        Morning,
        Afternoon,
        Wholeday,
        Evening
    }
}
