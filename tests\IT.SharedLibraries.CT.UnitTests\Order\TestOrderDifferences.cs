﻿using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Orders.Comparers;
using IT.SharedLibraries.CT.ShippingMethods;
using IT.SharedLibraries.CT.Zones;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace IT.SharedLibraries.CT.UnitTests.Order
{
    public class TestOrderDifferences
    {


        [Theory]
        [InlineData("11:30", "10:30", true)]
        [InlineData("11:30", "11:31", true)]
        [InlineData("11:30", "11:31:000", true)]
        [InlineData("12:00", "12:00", false)]
        [InlineData("12:00", "12:00:000", false)]
        [InlineData("12:00.000", "12:00", false)]
        [InlineData("9:45", "09:45", false)]
        [InlineData("9:45", "09:45:000", false)]
        [InlineData("9:45", "09:46:000", true)]
        [InlineData("9:45", "09:46", true)]
        [InlineData("14:30", "14:30:00.000", false)]
        [InlineData("", "", false)]
        [InlineData(null,"", false)]
        [InlineData("I'm a wrong dateTime format", "12:00", false)]
        [InlineData("09:45", "I'm a wrong dateTime format", true)]
        public void IsDeliveryTimeReceivedDifferentThanDeliveryTimeToCheck_AddsNewDeliveryTimeReceived(string deliveryTimeReceived, string deliveryTimeToCheck, bool shouldAddDifference)
        {
            List<KeyValuePair<OrderDifference, object>> differences = new();
            var orderService = new OrderService(null, null, new NullLogger<OrderService>(), null, null, null, null, null, null, null, null, null, null, null);

            orderService.HandleDeliveryTimeDifference(differences, deliveryTimeReceived, deliveryTimeToCheck);

            if (shouldAddDifference)
            {
                var _ = TimeSpan.TryParse(deliveryTimeReceived, out var timeReceived);

                Assert.Equal(differences.FirstOrDefault(), new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryTime, timeReceived.ToString(@"hh\:mm")));

            }
            else
                Assert.Empty(differences);

        }

        [Theory]
        [InlineData("delivered ", " DELIVERED", false)]
        [InlineData("pending", "delivered", true)]
        [InlineData(null, "delivered", true)]
        [InlineData(null, null, false)]
        public void IsOrderFieldTrimIgnoreCaseIsDifferent(string? incomingField, string? currentField, bool shouldBeDifferent)
        {
            var comparer = new FieldComparer();

            if (shouldBeDifferent)
                Assert.False(comparer.Equals(incomingField, currentField));
            else
                Assert.True(comparer.Equals(incomingField, currentField));

        }




    }

  
}
