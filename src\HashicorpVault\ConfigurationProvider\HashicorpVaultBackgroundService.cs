﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.HashicorpVault.ConfigurationProvider
{
    public class HashicorpVaultBackgroundService : BackgroundService
    {
        private readonly Configuration _configuration;
        private readonly ILogger _logger;

        public HashicorpVaultBackgroundService(ILoggerFactory loggerFactory, Configuration configuration)
        {
            _configuration = configuration;
            _logger = loggerFactory.CreateLogger<HashicorpVaultBackgroundService>();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while(!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Triggers the synchronization with Hashicorp Vault
                    ConfigurationChangeObserver.Instance.OnChanged(new ConfigurationChangeEventArgs());

                    await Task.Delay(_configuration.PollingFrequency > 0 ? _configuration.PollingFrequency : 60 * 1000, stoppingToken);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "An error occurred while polling Hashicorp Vault");
                }
            }
        }
    }
}
