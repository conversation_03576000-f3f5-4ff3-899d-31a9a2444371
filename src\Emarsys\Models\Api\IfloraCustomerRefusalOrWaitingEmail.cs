﻿using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Emarsys.Models.Api;
public class IfloraCustomerRefusalOrWaitingEmail : TriggerAnExternalEventBaseEmail
{
    public IfloraCustomerRefusalOrWaitingEmail(IfloraCustomerEmail ifloraCustomerEmail)
    {
        external_id = ifloraCustomerEmail.Email;
        data = ifloraCustomerEmail;
    }
}
