﻿using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Product.Resource
{
    public class ProductResourceUpdatedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public IProductProjection Product { get; set; }
        public ResourceUpdatedDeliveryPayload ResourceUpdated { get; set; }

    }
}
