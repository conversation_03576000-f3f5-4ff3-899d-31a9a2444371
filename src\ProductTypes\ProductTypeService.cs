﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.ProductTypes;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.ProductTypes
{
    public class ProductTypeService : IProductTypeService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ProductTypeService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public ProductTypeService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ProductTypeService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }

        public async Task<IProductType> GetByKey(string productTypeKey)
        {
            IProductType productType = null;
            try
            {
                productType = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductTypes()
                    .WithKey(productTypeKey)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading producttpye with key={productTypeKey}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading producttpye with key={productTypeKey} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return productType;
        }

        public async Task<IList<IProductType>> GetAll()
        {
            IList<IProductType> productTypes = null;
            try
            {
                var result = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductTypes()
                    .Get()
                    .ExecuteAsync();
                productTypes = result.Results;
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading ProductTypes, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading ProductTypes because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return productTypes;
        }
    }
}
