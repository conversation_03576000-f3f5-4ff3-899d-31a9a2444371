using Newtonsoft.Json;
using ITF.SharedLibraries.Emarsys.Models.Entity;

namespace ITF.SharedLibraries.Emarsys.Models.Api
{
    public class EmarsysRdsUpsertRequest
    {
        [JsonProperty("records")]
        public List<EmarsysRdsRecord> Records { get; set; } = new();
    }

    public class EmarsysRdsResponse
    {
        [JsonProperty("replyCode")]
        public int ReplyCode { get; set; }

        [JsonProperty("replyText")]
        public string ReplyText { get; set; } = string.Empty;

        [JsonProperty("data")]
        public object? Data { get; set; }

        public bool IsSuccess => ReplyCode == 0;

        public string GetErrorMessage()
        {
            return IsSuccess ? string.Empty : $"Error {ReplyCode}: {ReplyText}";
        }
    }
}