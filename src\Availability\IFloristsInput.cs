﻿using System;
using System.Collections.Generic;

namespace ITF.Lib.Common.Availability
{
    public interface IFloristsInput
    {
        public string CountryCode { get; set; }
        public string Language {  get; set; }
        public List<string> Skus { get; set; }
        public string City {  get; set; }
        public string Province { get; set; }
        public string ZipCode { get; set; }
        public string Address { get; set; }
        public string OnlineUser { get; set; }
        public DateTime DeliveryDate { get; set; }
        public int Moment { get; set; } // ITF.SharedModels.Group.Enums.MomentEnum
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }
}
