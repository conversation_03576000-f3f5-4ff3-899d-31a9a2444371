﻿using Elasticsearch.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.ElasticSearch
{
    public class Configuration
    {
        public Configuration()
        {
            Nodes = new List<Node>();
        }

        public List<Node> Nodes { get; set; }

        public class Node
        {
            public string NodeUrl { get; set; }
        }
    }
}
