﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristBlockedExecutionMessage : BaseMessage<LegacyFloristBlockedExecutionPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

               
            }
        }
    }

    public class LegacyFloristBlockedExecutionPayload : LegacyPayload, IEquatable<LegacyFloristBlockedExecutionPayload>
    {
        public string FloristIdentifier { get; set; }
        public bool BlockedExecution { get; set; }

        public bool Equals(LegacyFloristBlockedExecutionPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                BlockedExecution == parameter.BlockedExecution
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristBlockedExecutionPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            BlockedExecution
        }.GetHashCode();

    
    }
}
