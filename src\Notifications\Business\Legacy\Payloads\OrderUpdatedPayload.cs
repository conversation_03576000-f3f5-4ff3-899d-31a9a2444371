﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class OrderUpdatedPayload : BaseOrderPayload
    {
        //public OrderUpdatedPayload(BaseOrderPayload baseOrderPayload) : base(baseOrderPayload)
        //{

        //}
        public OrderUpdatedPayload()
        {

        }
        public OrderUpdatedPayload(BaseOrderPayload basePayload)
        {
            // Simple properties
            FloristId = basePayload.FloristId;
            OrderId = basePayload.OrderId ?? "";
            Version = basePayload.Version;
            OrderDate = basePayload.OrderDate;
            OrderEvent = basePayload.OrderEvent;
            Message = basePayload.Message;
            Signature = basePayload.Signature;
            GlobalPrivex = basePayload.GlobalPrivex;
            APCode = basePayload.APCode;
            TransmitterFloristId = basePayload.TransmitterFloristId;
            OrderType = basePayload.OrderType;
            SalesOrigin = basePayload.SalesOrigin;
            ParentReference = basePayload.ParentReference;
            InternationalOrderId = basePayload.InternationalOrderId;
            ExternalOrderId = basePayload.ExternalOrderId;
            Source = basePayload.Source;
            ExternalReference = basePayload.ExternalReference;
            TotalAmount = basePayload.TotalAmount;
            PaidAmount = basePayload.PaidAmount;
            ExecutingFloristSpecified = basePayload.ExecutingFloristSpecified;
            DynamicsSyncCounter = basePayload.DynamicsSyncCounter;
            EventAt = basePayload.EventAt;
            EventID = basePayload.EventID;
            EventDate = basePayload.EventDate;

            // Complex objects - need to create new instances to avoid reference sharing
            Status = basePayload.Status != null ? new StatusInformations
            {
                Status = basePayload.Status.Status,
                ManagerStatus = basePayload.Status.ManagerStatus,
                NotificationProcessingStatus = basePayload.Status.NotificationProcessingStatus,
                ReadStatus = basePayload.Status.ReadStatus
            } : null;

            Recipient = basePayload.Recipient != null ? new RecipientInformations
            {
                Greetings = basePayload.Recipient.Greetings,
                LastName = basePayload.Recipient.LastName,
                FirstName = basePayload.Recipient.FirstName,
                Street = basePayload.Recipient.Street,
                ZipCode = basePayload.Recipient.ZipCode,
                City = basePayload.Recipient.City,
                CountryCode = basePayload.Recipient.CountryCode,
                MainPhone = basePayload.Recipient.MainPhone,
                SecondPhone = basePayload.Recipient.SecondPhone,
                Latitude = basePayload.Recipient.Latitude,
                Longitude = basePayload.Recipient.Longitude
            } : null;

            Delivery = basePayload.Delivery != null ? new DeliveryInformations
            {
                Instructions = basePayload.Delivery.Instructions,
                Date = basePayload.Delivery.Date,
                PreviousDate = basePayload.Delivery.PreviousDate,
                Window = basePayload.Delivery.Window,
                Time = basePayload.Delivery.Time,
                LocationType = basePayload.Delivery.LocationType,
                Place = basePayload.Delivery.Place,
                AdditionalAddress = basePayload.Delivery.AdditionalAddress,
                TripCost = basePayload.Delivery.TripCost,
                AdditionnalTripCost = basePayload.Delivery.AdditionnalTripCost,
                ContactLastName = basePayload.Delivery.ContactLastName,
                ContactFirstName = basePayload.Delivery.ContactFirstName,
                TripCostComputationType = basePayload.Delivery.TripCostComputationType
            } : null;

            Customer = basePayload.Customer != null ? new CustomerInformations
            {
                Type = basePayload.Customer.Type,
                Id = basePayload.Customer.Id,
                Title = basePayload.Customer.Title ?? "MRS",
                Email = basePayload.Customer.Email,
                Phone = basePayload.Customer.Phone,
                FirstName = basePayload.Customer.FirstName,
                LastName = basePayload.Customer.LastName,
                CompanyName = basePayload.Customer.CompanyName,
                EmployeeId = basePayload.Customer.EmployeeId,
                PartnerCode = basePayload.Customer.PartnerCode
            } : null;

            Payment = basePayload.Payment != null ? new PaymentInformations
            {
                Mode = basePayload.Payment.Mode,
                Provider = basePayload.Payment.Provider,
                Id = basePayload.Payment.Id,
                Status = basePayload.Payment.Status,
                ContractNumber = basePayload.Payment.ContractNumber,
                Schedule = basePayload.Payment.Schedule
            } : null;

            Billing = basePayload.Billing != null ? new BillingInformations
            {
                City = basePayload.Billing.City,
                Street = basePayload.Billing.Street,
                ZipCode = basePayload.Billing.ZipCode
            } : null;

            // Deep copy the products list
            Products = basePayload.Products?.Select(p => new ProductInformations
            {
                ProductId = p.ProductId,
                Size = p.Size,
                Style = p.Style,
                Quantity = p.Quantity,
                Privex = p.Privex,
                RibbonText = p.RibbonText,
                Description = p.Description,
                DiscountCode = p.DiscountCode,
                Margin = p.Margin,
                LineNumber = p.LineNumber,
                BundleId = p.BundleId,
                IntercatCode = p.IntercatCode,
                Price = p.Price,
                Label = p.Label,
                FloristFee = p.FloristFee,
                Type = p.Type
            }).ToList();
        }

    }
}
