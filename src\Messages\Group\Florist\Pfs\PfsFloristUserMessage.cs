﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy;
using System;

namespace ITF.SharedModels.Messages.Group.Florist.Pfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristUserUpdatedMessage : BaseMessage<PfsFloristUserUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier + "_" + Payload?.Username;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristUserUpdatedMessage() { }


                public PfsFloristUserUpdatedMessage(string floristIdentifier, string username, string defaultShopId, UserRoleEnum role)
                {
                    this.Payload = new PfsFloristUserUpdatedPayload { 
                        FloristIdentifier = floristIdentifier,
                        Username = username,
                        Role = role,
                    };
                }
            }
        }
    }

    public class PfsFloristUserUpdatedPayload : LegacyPayload, IEquatable<PfsFloristUserUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string Username { get; set; }
        public string DefaultShopId { get; set; }
        public UserRoleEnum Role { get; set; }
        public string InitialPassword { get; set; }

        public bool Equals(PfsFloristUserUpdatedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier
                && Username == parameter.Username
                && DefaultShopId == parameter.DefaultShopId
                && Role == parameter.Role
                && InitialPassword == parameter.InitialPassword);
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristUserUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Username,
            DefaultShopId,
            Role,
            InitialPassword
        }.GetHashCode();
    }

    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristUserDeletedMessage : BaseMessage<PfsFloristUserDeletedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier + "_" + Payload?.DeletedBy;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristUserDeletedMessage() { }


                public PfsFloristUserDeletedMessage(string floristIdentifier, string username, string deletedBy)
                {
                    this.Payload = new PfsFloristUserDeletedPayload { FloristIdentifier = floristIdentifier, Username = username, DeletedBy = deletedBy };
                }
            }
        }
    }

    public class PfsFloristUserDeletedPayload : LegacyPayload, IEquatable<PfsFloristUserDeletedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string Username { get; set; }
        public string DeletedBy { get; set; }

        public bool Equals(PfsFloristUserDeletedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier
                && DeletedBy == parameter.DeletedBy);
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristUserDeletedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            DeletedBy
        }.GetHashCode();
    }
}
