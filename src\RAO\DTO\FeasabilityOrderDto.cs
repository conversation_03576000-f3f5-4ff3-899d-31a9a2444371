﻿namespace ITF.SharedLibraries.RAO.DTO
{
    public class FeasabilityOrderDto
    {
        public DateTime DeliveryDate { get; set; } = DateTime.UtcNow;
        public string DeliveryZipCode { get; set; }
        public decimal TotalAmount { get; set; }
        public string DeliveryCity { get; set; }
        public string DeliveryStreet { get; set; }
        public string DeliveryWindow { get; set; }
        public string OrderId { get; set; }
        public string DeliveryCountry { get; set; } = "FR"; // Default to France, can be overridden
        public List<string> ProductIds { get; set; } = [];
    }

    public class FeasabilityOrderResponseDto
    {
        public bool IsFeasible { get; set; }
        public string FeasibleReason { get; set; } = string.Empty;
        public List<FloristDto> Florists { get; set; } = [];
        public string Reason { get; set; } = string.Empty;
    }

    public class FloristDto
    {
        public string FloristId { get; set; } = string.Empty;
        public string CodeAP { get; set; } = string.Empty;
    }
}
