﻿using System.Collections.Generic;

namespace ITF.Lib.Common.Availability
{
    public interface IValidateRegionOutput
    {
        public bool IsValid { get; set; }
        public string? ZoneGeo { get; set; }
        public string? AlternativeCategoryUrl { get; set; }
        public AlternativeProduct AlternativeProduct { get; set; }
        public List<AlternativeAccessory> AlternativeAccessories { get; set; }
    }

    public class AlternativeProduct
    {
        public string? Reference { get; set; }
        public string? Label { get; set; }
        public Price? Price { get; set; }
        public Price? DiscountedPrice { get; set; }
    }

    public class AlternativeAccessory
    {
        public string? Reference { get; set; }
        public long Quantity { get; set; }
    }

    public class Price
    {
        public string? Currency { get; set; }
        public long? MinQuantity { get; set; }
        public long? MaxQuantity { get; set; }
        public long? Value { get; set; }
        public string? Type { get; set; }
    }
}
