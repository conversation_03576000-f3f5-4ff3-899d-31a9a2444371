using System.Text.Json.Serialization;

namespace ITF.SharedLibraries.Emarsys.Models.Entity
{
    public class EmarsysRdsRecord
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("email")]
        public string Email { get; set; } = string.Empty;

        [JsonPropertyName("response_date")]
        public string ResponseDate { get; set; } = string.Empty;

        [JsonPropertyName("name_relative")]
        public string NameRelative { get; set; } = string.Empty;

        [JsonPropertyName("birthdate_relative")]
        public string BirthdateRelative { get; set; } = string.Empty;

        [JsonPropertyName("month_bd")]
        public int MonthBd { get; set; }

        [JsonPropertyName("day_bd")]
        public int DayBd { get; set; }

        [JsonPropertyName("business_unit")]
        public string BusinessUnit { get; set; } = string.Empty;

        [JsonPropertyName("form_type")]
        public string FormType { get; set; } = string.Empty;

        [JsonPropertyName("origin")]
        public string Origin { get; set; } = string.Empty;
    }
}