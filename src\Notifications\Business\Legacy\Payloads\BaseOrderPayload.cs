﻿using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Orders;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class BaseOrderPayload : IPayload
    {
        public string? FloristId { get; set; } // Executing florist
        public string OrderId { get; set; }
        public int Version { get; set; }
        public DateTime OrderDate { get; set; }
        public string? OrderEvent { get; set; } // Order.Event
        public string? Message { get; set; }
        public string? Signature { get; set; }
        public double GlobalPrivex { get; set; }
        public string? APCode { get; set; }
        public string? TransmitterFloristId { get; set; }
        public string? OrderType { get; set; }
        public string? SalesOrigin { get; set; }
        public string? ParentReference { get; set; }
        public string? InternationalOrderId { get; set; }
        public string? ExternalOrderId { get; set; }
        public string? Source { get; set; }
        public string? ExternalReference { get; set; }
        public double? TotalAmount { get; set; }
        public double? PaidAmount { get; set; }
        public bool ExecutingFloristSpecified { get; set; }
        public int DynamicsSyncCounter { get; set; } // AxSendCounter
        public DateTime? EventAt { get; set; }
        public StatusInformations? Status { get; set; }
        public RecipientInformations? Recipient { get; set; }
        public DeliveryInformations? Delivery { get; set; }
        public CustomerInformations? Customer { get; set; }
        public PaymentInformations? Payment { get; set; }
        public BillingInformations? Billing { get; set; }
        public List<ProductInformations>? Products { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }

        

       

       
        //public BaseOrderPayload(BaseOrderPayload basePayload)
        //{
        //    // Simple properties
        //    FloristId = basePayload.FloristId;
        //    OrderId = basePayload.OrderId ?? "";
        //    Version = basePayload.Version;
        //    OrderDate = basePayload.OrderDate;
        //    OrderEvent = basePayload.OrderEvent;
        //    Message = basePayload.Message;
        //    Signature = basePayload.Signature;
        //    GlobalPrivex = basePayload.GlobalPrivex;
        //    APCode = basePayload.APCode;
        //    TransmitterFloristId = basePayload.TransmitterFloristId;
        //    OrderType = basePayload.OrderType;
        //    SalesOrigin = basePayload.SalesOrigin;
        //    ParentReference = basePayload.ParentReference;
        //    InternationalOrderId = basePayload.InternationalOrderId;
        //    ExternalOrderId = basePayload.ExternalOrderId;
        //    Source = basePayload.Source;
        //    ExternalReference = basePayload.ExternalReference;
        //    TotalAmount = basePayload.TotalAmount;
        //    PaidAmount = basePayload.PaidAmount;
        //    ExecutingFloristSpecified = basePayload.ExecutingFloristSpecified;
        //    DynamicsSyncCounter = basePayload.DynamicsSyncCounter;
        //    EventAt = basePayload.EventAt;
        //    EventID = basePayload.EventID;
        //    EventDate = basePayload.EventDate;

        //    // Complex objects - need to create new instances to avoid reference sharing
        //    Status = basePayload.Status != null ? new StatusInformations
        //    {
        //        Status = basePayload.Status.Status,
        //        ManagerStatus = basePayload.Status.ManagerStatus,
        //        NotificationProcessingStatus = basePayload.Status.NotificationProcessingStatus,
        //        ReadStatus = basePayload.Status.ReadStatus
        //    } : null;

        //    Recipient = basePayload.Recipient != null ? new RecipientInformations
        //    {
        //        Greetings = basePayload.Recipient.Greetings,
        //        LastName = basePayload.Recipient.LastName,
        //        FirstName = basePayload.Recipient.FirstName,
        //        Street = basePayload.Recipient.Street,
        //        ZipCode = basePayload.Recipient.ZipCode,
        //        City = basePayload.Recipient.City,
        //        CountryCode = basePayload.Recipient.CountryCode,
        //        MainPhone = basePayload.Recipient.MainPhone,
        //        SecondPhone = basePayload.Recipient.SecondPhone,
        //        Latitude = basePayload.Recipient.Latitude,
        //        Longitude = basePayload.Recipient.Longitude
        //    } : null;

        //    Delivery = basePayload.Delivery != null ? new DeliveryInformations
        //    {
        //        Instructions = basePayload.Delivery.Instructions,
        //        Date = basePayload.Delivery.Date,
        //        PreviousDate = basePayload.Delivery.PreviousDate,
        //        Window = basePayload.Delivery.Window,
        //        Time = basePayload.Delivery.Time,
        //        LocationType = basePayload.Delivery.LocationType,
        //        Place = basePayload.Delivery.Place,
        //        AdditionalAddress = basePayload.Delivery.AdditionalAddress,
        //        TripCost = basePayload.Delivery.TripCost,
        //        AdditionnalTripCost = basePayload.Delivery.AdditionnalTripCost,
        //        ContactLastName = basePayload.Delivery.ContactLastName,
        //        ContactFirstName = basePayload.Delivery.ContactFirstName,
        //        TripCostComputationType = basePayload.Delivery.TripCostComputationType
        //    } : null;

        //    Customer = basePayload.Customer != null ? new CustomerInformations
        //    {
        //        Type = basePayload.Customer.Type,
        //        Id = basePayload.Customer.Id,
        //        Title = basePayload.Customer.Title,
        //        Email = basePayload.Customer.Email,
        //        Phone = basePayload.Customer.Phone,
        //        FirstName = basePayload.Customer.FirstName,
        //        LastName = basePayload.Customer.LastName,
        //        CompanyName = basePayload.Customer.CompanyName,
        //        EmployeeId = basePayload.Customer.EmployeeId,
        //        PartnerCode = basePayload.Customer.PartnerCode
        //    } : null;

        //    Payment = basePayload.Payment != null ? new PaymentInformations
        //    {
        //        Mode = basePayload.Payment.Mode,
        //        Provider = basePayload.Payment.Provider,
        //        Id = basePayload.Payment.Id,
        //        Status = basePayload.Payment.Status,
        //        ContractNumber = basePayload.Payment.ContractNumber,
        //        Schedule = basePayload.Payment.Schedule
        //    } : null;

        //    Billing = basePayload.Billing != null ? new BillingInformations
        //    {
        //        City = basePayload.Billing.City,
        //        Street = basePayload.Billing.Street,
        //        ZipCode = basePayload.Billing.ZipCode
        //    } : null;

        //    // Deep copy the products list
        //    Products = basePayload.Products?.Select(p => new ProductInformations
        //    {
        //        ProductId = p.ProductId,
        //        Size = p.Size,
        //        Style = p.Style,
        //        Quantity = p.Quantity,
        //        Privex = p.Privex,
        //        RibbonText = p.RibbonText,
        //        Description = p.Description,
        //        DiscountCode = p.DiscountCode,
        //        Margin = p.Margin,
        //        LineNumber = p.LineNumber,
        //        BundleId = p.BundleId,
        //        IntercatCode = p.IntercatCode,
        //        Price = p.Price,
        //        Label = p.Label,
        //        FloristFee = p.FloristFee,
        //        Type = p.Type
        //    }).ToList();
        //}





    }
    public static class BaseOrderPayloadExtension
    {
        public static SalesOriginEnum GetSalesOrigin(this BaseOrderPayload payload)
        {
            if (payload.SalesOrigin != null)
            {
                Func<string, bool> stringEquals = (str) => payload.SalesOrigin.Equals(str, StringComparison.OrdinalIgnoreCase);
                if (stringEquals("CallCenter") || stringEquals("INTERNET") || stringEquals("Réseau") || stringEquals("Web")) return SalesOriginEnum.DESKTOP;
                else if (stringEquals("WebMobile")) return SalesOriginEnum.MOBILE;
                else if (stringEquals("app_ios")) return SalesOriginEnum.APP_IOS;
                else if (stringEquals("app_android")) return SalesOriginEnum.APP_ANDROID;
            }
            return SalesOriginEnum.UNKNOWN;
        }
    }
    public static class ProductInformationsListExtension
    {
        public static List<ProductInformations> GetEligibleProducts(this List<ProductInformations> products)
        {
            var productsResult = new List<ProductInformations>();
            foreach (var product in products ?? new())
            {
                if (product != null && !product.ProductId.StartsWith("FDL", StringComparison.OrdinalIgnoreCase))
                {
                    if (product?.ProductId == "IT")
                    {
                        product.ProductId = "ITFPLUS";
                        product.IntercatCode = "ITFPLUS-1";//to satisfy the implicit operator mapping bewteen LegacyProduct(ProductInformations)
                    }
                    productsResult.Add(product ?? new());
                }

            }

            return productsResult;
        }
        public static List<ProductInformations> GroupByBundleProducts(this List<ProductInformations> products)
        {
            var productsResult = new List<ProductInformations>();
            var bundleIds = products.Where(p => p.IsBundlePart()).Select(p => p.BundleId).Distinct().ToList();
            foreach (var bundleId in bundleIds ?? new())
            {
                productsResult.Add(products.MapLineItemtoBundleProduct(bundleId));
            }
            productsResult.AddRange(products.FindAll(p => !p.IsBundlePart()));
            return productsResult;
        }

        public static ProductInformations MapLineItemtoBundleProduct(this List<ProductInformations> products, string bundleId)
        {
            var bundleProducts = products.FindAll(p => p.BundleId == bundleId);
            var bundleInfo = bundleId.Split('-') ?? Array.Empty<string>();
            string size = string.Empty;
            string style = string.Empty;

            if (bundleInfo.Length == 3)
            {
                size = bundleInfo[1];
                style = bundleInfo[2];
            }
            return new ProductInformations
            {
                ProductId = bundleId,
                BundleId = bundleId,
                Size = size,
                Style = style,
                Quantity = 1,
                Margin = Math.Round(bundleProducts.Sum(p => !p.IsDiscountLineItem() ? (p.Margin ?? 0) : 0), 2),
                Price = Math.Round(bundleProducts.Sum(p => !p.IsDiscountLineItem() ? p.Price : -p.Price), 2),
                Privex = Math.Round(bundleProducts.Sum(p => !p.IsDiscountLineItem() ? p.Privex : -p.Privex), 2)

            };
        }

       


    }
}
