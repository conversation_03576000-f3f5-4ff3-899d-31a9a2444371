﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderAccepted
    {
        public string FloristIdentier { get; set; }
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderAccepted(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedMessage v)
        {
            return new GlobalOrderAccepted
            {
                FloristIdentier = v?.Payload?.FloristIdentifier,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
