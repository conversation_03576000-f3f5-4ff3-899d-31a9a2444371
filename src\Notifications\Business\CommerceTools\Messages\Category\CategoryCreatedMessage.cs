﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Category;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Category
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CategoryCreatedMessage : BaseMessage<CategoryCreatedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.CategoryCreatedMessage?.Category?.Id;
            }
        }
    }
}
