﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Availability;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Messages.Availability
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class AvailabilityPerformedMessage : BaseMessage<AvailabilityPerformedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.OrderId;
            }
        }
    }
}
