﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Azure.Cosmos.Table;
using System.Threading.Tasks;
using ITF.SharedLibraries.EnvironmentVariable;
using System.Linq;

namespace ITF.SharedLibraries.AzureTable
{
    public class AzureTableRepository<T> : IAzureTableRepository<T> where T : TableEntity, new()
    {
        public CloudStorageAccount _storageAccount { get; private set; }
        public CloudTableClient _cloudTableClient { get; private set; }
        public CloudTable _cloudTable { get; private set; }
        public string _partitionKey { get; private set; }

        public async Task ConfigureAsync(string connexionString, string partitionKey, string table)
        {
            _storageAccount = CloudStorageAccount.Parse(connexionString);
            _cloudTableClient = _storageAccount.CreateCloudTableClient();
            _partitionKey = partitionKey;
            _cloudTable = _cloudTableClient.GetTableReference(table);
            await _cloudTable.CreateIfNotExistsAsync();
        }
        public async Task ConfigureAsync(string envVariable)
        {
            var configuration = EnvironmentVariable.EnvironmentVariable.Get<Configuration>(envVariable);
            await ConfigureAsync(configuration);
        }

        public async Task ConfigureAsync(Configuration configuration)
        {
            _storageAccount = CloudStorageAccount.Parse(configuration.Connexion);
            _cloudTableClient = _storageAccount.CreateCloudTableClient();
            _partitionKey = configuration.partitionKey;
            _cloudTable = _cloudTableClient.GetTableReference(configuration.table);
            await _cloudTable.CreateIfNotExistsAsync();
        }

        
        public async Task<TableResult> InsertOrUpdateAsync(T entity)
        {
            SetPartitionKeyIfMissing(entity);
            var operation = TableOperation.InsertOrReplace(entity);
            return await _cloudTable.ExecuteAsync(operation);
        }

        public async Task<T> GetByRowKeyAsync(string rowKey, string partitionKey = null)
        {
            var operation = TableOperation.Retrieve<T>(partitionKey ?? _partitionKey, rowKey);
            var result = await _cloudTable.ExecuteAsync(operation);
            return (T)result.Result;
        }

        public async Task<List<T>> GetAsync(string query, string partitionKey = null)
        {
            var filter = TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.Equal, (partitionKey ?? _partitionKey));
            filter += $" and ({query})";

            var fullQuery = new TableQuery<T>().Where(filter);
            var res = await _cloudTable.ExecuteQuerySegmentedAsync(fullQuery, null);
            return res.ToList();
        }

        public async Task<T> DeleteByRowKeyAsync(T entity)
        {
            SetPartitionKeyIfMissing(entity);
            var operation = TableOperation.Delete(entity);
            var result = await _cloudTable.ExecuteAsync(operation);
            return (T)result.Result;
        }

        public async Task DeleteAllAsync(string partitionKey)
        {
            var projectionQuery = new TableQuery()
              .Where(TableQuery.GenerateFilterCondition("PartitionKey",
                QueryComparisons.Equal, partitionKey))
              .Select(new[] { "RowKey" });

            var entities = _cloudTable.ExecuteQuery(projectionQuery).ToList();
            var offset = 0;
            while (offset < entities.Count)
            {
                var batch = new TableBatchOperation();
                var rows = entities.Skip(offset).Take(100).ToList();
                foreach (var row in rows)
                {
                    batch.Delete(row);
                }

                await _cloudTable.ExecuteBatchAsync(batch);
                offset += rows.Count;
            }
        }

        private void SetPartitionKeyIfMissing(T entity)
        {
            if (string.IsNullOrEmpty(entity.PartitionKey))
                entity.PartitionKey = _partitionKey;
        }
    }
}
