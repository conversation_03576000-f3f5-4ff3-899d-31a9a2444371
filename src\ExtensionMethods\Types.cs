﻿using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.ExtensionMethods
{
    public class Types
    {
        public static IEnumerable<Type> GetAllTypesOf<T>()
        {
            var platform = Environment.OSVersion.Platform.ToString();
            var runtimeAssemblyNames = DependencyContext.Default.GetRuntimeAssemblyNames(platform);

            return runtimeAssemblyNames
                .Select(Assembly.Load)
                .SelectMany(a => a.ExportedTypes)
                .Where(t => typeof(T).IsAssignableFrom(t));
        }

        public static IEnumerable<Type> GetAllTypesImplementingOpenGenericType(Type openGenericType)
        {
            var res = AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(x => x.GetTypes())
                .Where(x =>
                    !x.IsAbstract
                && !x.IsInterface
                && x.GetInterfaces().Any(i =>
                       i.IsGenericType
                   && i.GetGenericTypeDefinition() == openGenericType
                    )
                );

            return res;
        }

    }
}
