﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS
{
    public class FloralChequePayload : GFSPayloadBase, IPayload
    {
        public string ChequeNumber { get; set; }
        public int TransactionNumber { get; set; }
        public string IssuingMemberCode { get; set; }
        public string ExecutingMemberCode { get; set; }
        public DateTime ChequeDate { get; set; }
        public int Amount { get; set; }
        public string ClearingPeriod { get; set; }
        public int FloristgateNumber { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
