﻿using commercetools.Sdk.Api.Models.Carts;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Cart
{
    public class CartAbandonedFromLongPeriodPayload : IPayload
    {
        public int ThresholdRuleInMinutes { get; set; }
        public int MaxThresholdRuleInMinutes { get; set; }
        public int AbandonedSinceInMinutes { get; set; }
        public ICart Cart { get; set; }
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
    }
}
