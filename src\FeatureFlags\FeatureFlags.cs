﻿using Microsoft.FeatureManagement;
using System.Threading.Tasks;
using Unleash;
using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.FeatureFlags
{
    public class FeatureFlags : IFeatureFlags
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IFeatureManager _featureManager;
        private readonly ILogger<FeatureFlags> _logger;

        public FeatureFlags(
            IServiceScopeFactory scopeFactory,
            IFeatureManager featureManager,
            ILogger<FeatureFlags> logger
            )
        {
            _scopeFactory = scopeFactory;
            _featureManager = featureManager;
            _logger = logger;
        }
        public async Task<bool> IsEnabled(string feature, bool? fallBackBehavior = null)
        {
            var prodiver = "";
            try
            {
                using var scope = _scopeFactory.CreateScope();

                var featureFlagConfiguration = scope.ServiceProvider.GetRequiredService<Configuration>();
                prodiver = featureFlagConfiguration.Provider?.ToLower();
                switch (prodiver)
                {
                    case "unleash":
                        var unleash = scope.ServiceProvider.GetRequiredService<IUnleash>();
                        if (fallBackBehavior.HasValue)
                            return await Task.FromResult(unleash.IsEnabled(feature, fallBackBehavior.Value));
                        else
                            return await Task.FromResult(unleash.IsEnabled(feature));

                    case "featuremanager":
                    case "feature_manager":
                        return await _featureManager.IsEnabledAsync(feature);

                    default:
                        return await _featureManager.IsEnabledAsync(feature);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to check feature toggle activation for {feature} with provider {prodiver}", feature, prodiver);
                if (fallBackBehavior.HasValue)
                {
                    _logger.LogWarning("Use provided fallback value {toggle}", fallBackBehavior.Value);
                    return await Task.FromResult(fallBackBehavior.Value);
                }
                else
                {
                    var toggle = await _featureManager.IsEnabledAsync(feature);
                    _logger.LogWarning("Use feature management fallback value {toggle}", toggle);
                    return toggle;
                }
            }
        }
    }
}
