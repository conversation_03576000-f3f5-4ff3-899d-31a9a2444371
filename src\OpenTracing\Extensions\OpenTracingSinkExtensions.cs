﻿using ITF.SharedLibraries.OpenTracing.Logger;
using OpenTracing;
using OpenTracing.Propagation;
using OpenTracing.Tag;
using OpenTracing.Util;
using Serilog;
using Serilog.Configuration;
using System;
using System.Collections.Generic;

namespace ITF.SharedLibraries.OpenTracing.Extensions
{
    public static class OpenTracingSinkExtensions
	{
		public static LoggerConfiguration OpenTracing(this LoggerSinkConfiguration loggerConfiguration, IFormatProvider formatProvider = null)
		{
			return loggerConfiguration.Sink(new OpenTracingSink(GlobalTracer.Instance, formatProvider));
		}

        public static IScope PublisherSpan(this ITracer tracer, string operationName, out IDictionary<string, string> tracingKeys, IDictionary<string, string> addtionalTags = null)
        {
            IScope scope = null;
            tracingKeys = new Dictionary<string, string>();
            try
            {
                scope = tracer.BuildSpan(operationName).StartActive(finishSpanOnDispose: true);
                var span = scope.Span.SetTag(Tags.SpanKind, Tags.SpanKindProducer);

                if(addtionalTags != null)
                {
                    foreach (var tag in addtionalTags)
                    {
                        span = scope.Span.SetTag(tag.Key, tag.Value);
                    }
                }

                var carrier = new Dictionary<string, string>();
                tracer.Inject(span.Context, BuiltinFormats.TextMap, new TextMapInjectAdapter(carrier));

                // Set the output tracing keys to be transported through the bus
                tracingKeys = carrier;
            }
            catch (Exception) { }

            return scope;
        }

        public static IScope ConsumerSpan(this ITracer tracer, string operationName, IDictionary<string, string> tracingKeys)
        {
            ISpanBuilder spanBuilder;
            try
            {
                ISpanContext parentSpanCtx = tracer.Extract(BuiltinFormats.TextMap, new TextMapExtractAdapter(tracingKeys));

                spanBuilder = tracer.BuildSpan(operationName);
                if (parentSpanCtx != null)
                {
                    spanBuilder = spanBuilder.AsChildOf(parentSpanCtx);
                }
            }
            catch (Exception)
            {
                spanBuilder = tracer.BuildSpan(operationName);
            }

            return spanBuilder.WithTag(Tags.SpanKind, Tags.SpanKindConsumer).StartActive(true);
        }
    }
}
