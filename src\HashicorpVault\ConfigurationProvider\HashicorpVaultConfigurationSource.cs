﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Text;
using VaultSharp;
using VaultSharp.V1.AuthMethods;
using VaultSharp.V1.AuthMethods.AppRole;

namespace ITF.SharedLibraries.HashicorpVault.ConfigurationProvider
{
    public class HashicorpVaultConfigurationSource : IConfigurationSource
    {
        public Configuration Configuration { get; set; }
        public IVaultClient VaultClient { get; private set; }
        public HostBuilderContext HostBuilderContext { get; set; }
        public bool ReloadOnChange { get; set; }

        // Number of milliseconds that reload will wait before calling Load. This helps avoid triggering a reload before a change is completely saved. Default is 500.
        public int ReloadDelay { get; set; } = 500;


        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            IAuthMethodInfo authMethod =
                    new AppRoleAuthMethodInfo(Configuration.Role_id, Configuration.Secret_id);

            var vaultClientSettings = new VaultClientSettings(Configuration.Url, authMethod);
            VaultClient = new VaultClient(vaultClientSettings);
            
            return new HashicorpVaultConfigurationProvider(this);
        }
    }
}
