﻿using StackExchange.Redis;
using System;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace ITF.SharedLibraries.Redis
{
    public interface ICacheClient
    {
        IConnectionMultiplexer Connection { get; }
        IDatabase Database { get; }
        IServer Server { get; }
        Task<int> RevokeAsync(string key, string pattern = null);
        T Get<T>(RedisKey key, CommandFlags flags = CommandFlags.None,SerializerType serializerType = SerializerType.TextJson);
        Task<T> GetAsync<T>(RedisKey key, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson);
        bool Set(RedisKey key, object value, TimeSpan? expiry = null, When when = When.Always, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson);
        Task<bool> SetAsync(RedisKey key, object value, TimeSpan? expiry = null, When when = When.Always, CommandFlags flags = CommandFlags.None, SerializerType serializerType = SerializerType.TextJson);
    }
}