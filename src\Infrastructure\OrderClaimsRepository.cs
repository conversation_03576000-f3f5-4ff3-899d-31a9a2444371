﻿using IT.Microservices.OrderReactor.Domain;
using IT.SharedLibraries.CT.Repository;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Order;
using MongoDB.Driver;
using System.Linq;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public class OrderClaimsRepository(IMongoClient mongoClient) : MongoRepository<OrderClaim>(mongoClient, "it-florist", "order_claims"), IOrderClaimsRepository
    {
        public async Task<OrderClaim?> GetByOrderIdAsync(string orderId)
        {
            var elements = await Collection.FindAsync(x => x.OrderId == orderId);
            var elementsList = await elements.ToListAsync();

            return elementsList.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
        }
    }
}
