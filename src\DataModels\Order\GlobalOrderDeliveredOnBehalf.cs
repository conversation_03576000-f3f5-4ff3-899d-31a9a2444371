﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveredOnBehalf
    {
        public DateTime? DeliveryDate { get; set; }
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderDeliveredOnBehalf(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredOnBehalfMessage v)
        {
            return new GlobalOrderDeliveredOnBehalf { DeliveryDate = v?.Payload?.DeliveryDate, OrderIdentifier = v?.Payload.OrderIdentifier };
        }
    }
}
