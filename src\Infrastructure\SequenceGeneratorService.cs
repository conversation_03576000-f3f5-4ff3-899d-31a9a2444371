﻿using IT.Microservices.OrderReactor.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HttpClient = ITF.SharedLibraries.HttpClient.HttpClient;
using System.Threading.Tasks;
using System.Net.Http;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public class SequenceGeneratorService : HttpClient, ISequenceGeneratorService
    {
        public SequenceGeneratorService(System.Net.Http.HttpClient httpClient, IConfiguration config, ILogger<SequenceGeneratorService> logger) :
            base(httpClient, config, "SequenceGeneratorGetNextEndpoint", logger)
        {
        }

        public async Task<long> GetNext()
        {
            //HttpClient httpClient = new HttpClient();
            var httpResponseMessages = await GetAsync("/Sequence/GenerateNextSequence");

            httpResponseMessages.EnsureSuccessStatusCode();
            var json = await httpResponseMessages.Content.ReadAsStringAsync();

            if(long.TryParse(json, out var sequenceId)) { return sequenceId; }
            return -1;
        }
    }
}
