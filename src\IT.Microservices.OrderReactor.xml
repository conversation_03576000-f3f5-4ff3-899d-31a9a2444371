<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IT.Microservices.OrderReactor</name>
    </assembly>
    <members>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandler.ExecuteWithRetry``1(System.Func{System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            Handles retry logic for operations that might encounter ConcurrentModificationException
            </summary>
            <typeparam name="TResult">The return type of the operation</typeparam>
            <param name="operation">The operation to execute</param>
            <param name="operationName">Name of the operation for logging</param>
            <returns>Result of the operation</returns>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandlerExtensions.Handle409Conflict``1(IT.Microservices.OrderReactor.Infrastructure.RetryHandler,System.Func{``0,System.Threading.Tasks.Task{commercetools.Sdk.Api.Models.Orders.IOrder}},``0,System.String)">
            <summary>
            Handle 409 conflicts with retry for single parameter methods
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandlerExtensions.Handle409Conflict``2(IT.Microservices.OrderReactor.Infrastructure.RetryHandler,System.Func{``0,``1,System.Threading.Tasks.Task{commercetools.Sdk.Api.Models.Orders.IOrder}},``0,``1,System.String)">
            <summary>
            Handle 409 conflicts with retry for two parameter methods
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandlerExtensions.Handle409Conflict``3(IT.Microservices.OrderReactor.Infrastructure.RetryHandler,System.Func{``0,``1,``2,System.Threading.Tasks.Task{commercetools.Sdk.Api.Models.Orders.IOrder}},``0,``1,``2,System.String)">
            <summary>
            Handle 409 conflicts with retry for three parameter methods
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandlerExtensions.Handle409Conflict``4(IT.Microservices.OrderReactor.Infrastructure.RetryHandler,System.Func{``0,``1,``2,``3,System.Threading.Tasks.Task{commercetools.Sdk.Api.Models.Orders.IOrder}},``0,``1,``2,``3,System.String)">
            <summary>
            Handle 409 conflicts with retry for four parameter methods
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Infrastructure.RetryHandlerExtensions.Handle409Conflict``1(IT.Microservices.OrderReactor.Infrastructure.RetryHandler,System.Func{System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            Generic retry handler for any operation
            </summary>
        </member>
        <member name="T:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController">
            <summary>
            Controller for manually triggering order synchronization processes
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.#ctor(IT.Microservices.OrderReactor.Domain.IOrderUseCase,IT.SharedLibraries.CT.Orders.IOrderService,Microsoft.Extensions.Logging.ILogger{IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController},ITF.SharedLibraries.RAO.IRAOSupplierHttpService,ITF.SharedLibraries.Alerting.ISlackAlertService)">
            <summary>
            Controller for manually triggering order synchronization processes
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderCreated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCreatedMessage)">
            <summary>
            Process Legacy Order Created Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderAssigned(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignedMessage)">
            <summary>
            Process Legacy Order Assigned Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderCancelled(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCancelledMessage)">
            <summary>
            Process Legacy Order Cancelled Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveryTimeUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryTimeUpdatedMessage)">
            <summary>
            Process Legacy Order Delivery Time Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveryStatusUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryStatusUpdatedMessage)">
            <summary>
            Process Legacy Order Delivery Status Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveryCostUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryCostUpdatedMessage)">
            <summary>
            Process Legacy Order Delivery Cost Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveryDateUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryDateUpdatedMessage)">
            <summary>
            Process Legacy Order Delivery Date Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderCardMessageUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCardMessageUpdatedMessage)">
            <summary>
            Process Legacy Order Card Message Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveryAddressUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryAddressUpdatedMessage)">
            <summary>
            Process Legacy Order Delivery Address Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderNotesUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderNotesUpdatedMessage)">
            <summary>
            Process Legacy Order Notes Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderRecipientCoordinatesUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientCoordinatesUpdatedMessage)">
            <summary>
            Process Legacy Order Recipient Coordinates Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDeliveredOnBehalf(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredOnBehalfMessage)">
            <summary>
            Process Legacy Order Delivered On Behalf Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderAccepted(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedMessage)">
            <summary>
            Process Legacy Order Accepted Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderRejected(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRejectedMessage)">
            <summary>
            Process Legacy Order Rejected Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderDelivered(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredMessage)">
            <summary>
            Process Legacy Order Delivered Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderItemUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemUpdatedMessage)">
            <summary>
            Process Legacy Order Item Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderItemExecutorAmountUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemExecutorAmountUpdatedMessage)">
            <summary>
            Process Legacy Order Item Executor Amount Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderAssignationRemoved(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignationRemovedMessage)">
            <summary>
            Process Legacy Order Assignation Removed Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderAcceptedOnBehalf(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedOnBehalfMessage)">
            <summary>
            Process Legacy Order Accepted On Behalf Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderRejectedOnBehalf(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRejectedOnBehalfMessage)">
            <summary>
            Process Legacy Order Rejected On Behalf Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderSent(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderSentMessage)">
            <summary>
            Process Legacy Order Sent Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderRecipientNameUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientNameUpdatedMessage)">
            <summary>
            Process Legacy Order Recipient Name Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessLegacyOrderRecipientPhoneNumberUpdated(ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientPhoneNumberUpdatedMessage)">
            <summary>
            Process Legacy Order Recipient Phone Number Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderPlaced(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderPlacedMessage)">
            <summary>
            Process Order Placed Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderAssignment(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderAssignmentMessage)">
            <summary>
            Process Order Assignment Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderUpdated(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderUpdatedMessage)">
            <summary>
            Process Order Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderManagementStatus(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderManagementStatusMessage)">
            <summary>
            Process Order Management Status Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderDeliveryCourierUpdated(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderDeliveryCourierUpdatedMessage)">
            <summary>
            Process Order Delivery Courier Updated Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderDeliveryCourierReseted(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderDeliveryCourierResetedMessage)">
            <summary>
            Process Order Delivery Courier Reseted Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessOrderDeliveryCourierInitialized(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.OrderDeliveryCourierInitializedMessage)">
            <summary>
            Process Order Delivery Courier Initialized Message
            </summary>
        </member>
        <member name="M:IT.Microservices.OrderReactor.Presentation.OrderSynchronizationController.ProcessInvoice(ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1.InvoiceMessage)">
            <summary>
            Process Invoice Message
            </summary>
        </member>
    </members>
</doc>
