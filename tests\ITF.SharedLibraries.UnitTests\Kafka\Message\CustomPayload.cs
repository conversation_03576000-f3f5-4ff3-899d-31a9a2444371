﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedLibraries.UnitTests.Kafka.Message
{
    public class CustomPayload : IPayload
    {
        public string Id { get; set; }
        public string Prop1 { get; set; }
        public int Prop2 { get; set; }
        public double Prop3 { get; set; }
        public DateTime Prop4 { get; set; }

        public Class1 Class1 { get; set; }
        public List<Class2> Class2s { get; set; } = new();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class Class1
    {
        public float Prop1 { get; set; }
        public uint Prop2 { get; set; }
        public ulong Prop3 { get; set; }
        public object Prop4 { get; set; }
        public short Prop5 { get; set; }
        public dynamic Prop6 { get; set; }
        public char Prop7 { get; set; }
        public bool Prop8 { get; set; }
    }
    public class Class2
    {
        public float Prop1 { get; set; }
        public uint Prop2 { get; set; }
        public ulong Prop3 { get; set; }
        public object Prop4 { get; set; }
        public short Prop5 { get; set; }
        public dynamic Prop6 { get; set; }
        public char Prop7 { get; set; }
        public bool Prop8 { get; set; }
        public Class3 Class3 { get; set; } = new();
    }
    public class Class3
    {
        public float Prop1 { get; set; }
        public uint Prop2 { get; set; }
        public ulong Prop3 { get; set; }
        public object Prop4 { get; set; }
        public short Prop5 { get; set; }
        public dynamic Prop6 { get; set; }
        public char Prop7 { get; set; }
        public bool Prop8 { get; set; }
    }
}
