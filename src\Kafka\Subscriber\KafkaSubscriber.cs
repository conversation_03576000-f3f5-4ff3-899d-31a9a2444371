﻿using commercetools.Sdk.Api.Serialization;
using Confluent.Kafka;
using CSharpFunctionalExtensions;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.HealthCheck;
using ITF.SharedLibraries.Kafka.Publisher;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Text;
using System.Text.Json;

namespace ITF.SharedLibraries.Kafka.Subscriber
{
    public class KafkaSubscriber<TKey, TValue> : IKafkaSubscriber<TKey, TValue>
    {
        private readonly Configuration _configuration;
        private readonly IEnumerable<IMessageHandler> _multipleSubscribers;
        private readonly SerializerService _serializerService;
        private readonly ILogger<KafkaSubscriber<TKey, TValue>> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IList<Subscribers<TKey, TValue>> _subscribers;

        public KafkaSubscriber(
            Configuration configuration,
            IEnumerable<IMessageHandler> multipleSubscribers,
            ILogger<KafkaSubscriber<TKey, TValue>> logger,
            IServiceProvider serviceProvider,
            SerializerService serializerService = null)
        {
            _subscribers = new List<Subscribers<TKey, TValue>>();
            _configuration = configuration;
            _multipleSubscribers = multipleSubscribers;
            _serializerService = serializerService;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public IList<Subscribers<TKey, TValue>> GetSubscribers() => _subscribers;

        private static Deserializer GetDeserializer(Deserializer? deserializer)
            => deserializer == null ? Deserializer.Standard : deserializer.Value;

        private Deserializer CorrectSerializer(string clrType, Deserializer deserializer)
        {
            Deserializer currentDeserializer = deserializer;
            try
            {
                if (clrType.ToLower().Contains("commercetools") &&
                    deserializer != Deserializer.CommerceTools &&
                    _serializerService != null)
                    return Deserializer.CommerceTools;

                if (!clrType.ToLower().Contains("commercetools") &&
                    deserializer == Deserializer.CommerceTools)
                    return Deserializer.Standard;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error on correction on serializer choice, current one {serializer} is returned", currentDeserializer.ToString());
                return currentDeserializer;
            }

            return currentDeserializer;
        }
        
        private static string GetClrTypeFromHeaders(ConsumeResult<TKey, TValue> consumeResult)
        {
            string clrType = null;
            if (consumeResult.Message.Headers.Any())
            {
                foreach(var header in consumeResult.Message.Headers)
                {
                    if (header.Key == "ClrType")
                    {
                        var clrTypeBuffer = header.GetValueBytes();
                        if (clrTypeBuffer is not null)
                            clrType = Encoding.ASCII.GetString(clrTypeBuffer);

                        break;
                    }
                }
            }

            return clrType;
        }

        private static string GetClrTypeFromMessage(ConsumeResult<TKey, TValue> consumeResult)
        {
            try
            {
                JObject jsonObject = JObject.Parse(consumeResult.Message.Value.ToString());
                return jsonObject["ClrType"].ToString();
            }
            catch
            {
                return null;
            }
        }

        private Type GetType(string clrType)
        {
            // Allows to handle the case of a Version making impossible the deserialization
            var type = Type.GetType(clrType);
            if (type == null)
            {
                // "ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Product.Resource.Messages+V1+ProductResourceUpdatedMessage, ITF.SharedModels, Version=6.16.0.0, Culture=neutral, PublicKeyToken=null"
                var types = clrType.Split(',');
                var sb = new StringBuilder();
                foreach (var t in types)
                {
                    if (!t.Trim().ToLower().StartsWith("version"))
                        sb.Append(t).Append(',');
                }
                type = Type.GetType(sb.ToString().TrimEnd(','));
                if (type == null)
                {
                    _logger.LogWarning("Error on the GetType Method : Impossible to Get the Type from the ClrType ({clrtype}) -> we skip this message for this consumer and continue to read for no blocking the whole reading process",clrType);
                    return null;
                }
            }
            return type;
        }
        
        private void SetUnhealthy()
        {
            try
            {
                var hc = _serviceProvider?.GetService<HealthCheckProvider>();
                if (hc is null)
                    _logger.LogError("Can't set application unhealthy from Kafka handler, no {className} available", nameof(HealthCheckProvider));

                hc?.SetUnHealthy();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Unable to set the Kafka subscriber unhealthy!");
            }
        }

        public async Task ConsumerCheckAsync(Subscribers<TKey, TValue> subscriber, CancellationToken stoppingToken)
        {
            // Any registered handler for it ?
            IMessageHandler handler = _multipleSubscribers?.FirstOrDefault(m => m?.GetType()?.Name?.ToLower() == subscriber?.ClassName?.ToLower());
            Deserializer chosenDeserializer = GetDeserializer(subscriber.SubscriberConfiguration.Deserializer);

            if (handler == default(IMessageHandler))
            {
                _logger.LogError("Kafka : no handler class found for subscriber {subscriber} - Problem with settings", subscriber?.ClassName?.ToLower());
                SetUnhealthy();
                throw new Exception($"Kafka : no handler class found for subscriber {subscriber?.ClassName?.ToLower()} - Problem with settings");
            }

            _logger.LogInformation("Kafka consumer is starting for subscriber {subscriber} on topic {topic}", subscriber?.ClassName?.ToLower(), subscriber.SubscriberConfiguration.TopicName);

            while (!stoppingToken.IsCancellationRequested)
            {
                TValue message = default;
                try
                {
                    // Read the stream
                    var consumeResult = subscriber.Consumer.Consume(stoppingToken);

                    if (consumeResult.IsPartitionEOF)
                        continue;

                    // A message has been supplied ?
                    message = consumeResult.Message.Value;

                    if (message is null)
                        continue;

                    object unserializedTypedObject = null;
                    var msg = message as string;
                    string clrType = null;

                    // Get the ClrType from message headers (always set by default in our publish method in KafkaPublisher.cs)
                    clrType = GetClrTypeFromHeaders(consumeResult) ?? GetClrTypeFromMessage(consumeResult);

                    if (clrType is not null)
                    {
                        // May automatically correct serializer if the one chosen appears not the expected one
                        chosenDeserializer = CorrectSerializer(clrType, chosenDeserializer);

                        // Typed object

                        // Check if we can the Get the Type from the ClrType of the Message
                        var type = GetType(clrType);

                        if(type is not null)
                        {
                            try
                            {
                                // Commercetools serializer is specific to their provided events
                                // see https://github.com/commercetools/commercetools-dotnet-core-sdk-v2/issues/115
                                if (chosenDeserializer == Deserializer.CommerceTools)
                                    unserializedTypedObject = _serializerService.Deserialize(type, msg);
                                else
                                    unserializedTypedObject = JsonSerializer.Deserialize(msg, type);
                            }
                            catch (Exception e)
                            {
                                _logger.LogWarning(e, "Error on deserializing the message {message} with ClrType {clrType} - we skip this message for this consumer and continue to read for no blocking the whole reading process", msg, clrType);
                                await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                                {
                                    BoundedContext = "Kafka",
                                    RelatedKafkaMessage = msg,
                                    MessageId = Guid.NewGuid().ToString(),
                                    Severity = "Medium",
                                    Message = $"Exception raised when trying to Deserialize with {clrType} in lowLevel ConsumerCheckAsync / Skip message",
                                    ErrorType = "KafkaConsumerDeserializeException",
                                    ExceptionCaptured = e.ToString(),
                                    RelatedMethod = "ConsumerCheckAsync",
                                    RelatedMs = "ITF.SharedLibraries",
                                    RelateProcess = "KafkaSubscriber"
                                });
                            }

                        }
                        else
                            _logger.LogWarning("The current Message : {mess} , cant be deserialize using the provided ClrType ({clrType}) in the header/payload of the message", msg , clrType);

                    }
                    else
                    {
                        try
                        {
                            // Untyped object
                            _logger.LogWarning("No Clrtype has been provided for the current message {message} in the header or payload , it is deserialized as dynamic", msg);
                            unserializedTypedObject = JsonSerializer.Deserialize<dynamic>(msg);
                        }
                        catch (Exception e)
                        {
                            _logger.LogWarning(e, "Error on deserializing the message {message} as dynamic - we skip this message for this consumer and continue to read for no blocking the whole reading process", msg);
                            await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                            {
                                BoundedContext = "Kafka",
                                RelatedKafkaMessage = msg,
                                MessageId = Guid.NewGuid().ToString(),
                                Severity = "Medium",
                                Message = $"Exception raised when trying to Deserialize with dynamic in lowLevel ConsumerCheckAsync / Skip message",
                                ErrorType = "KafkaConsumerDeserializeException",
                                ExceptionCaptured = e.ToString(),
                                RelatedMethod = "ConsumerCheckAsync",
                                RelatedMs = "ITF.SharedLibraries",
                                RelateProcess = "KafkaSubscriber"
                            });
                        }

                    }

                    // Finally handle the message from subscriber
                    await handler.HandleMessage(unserializedTypedObject, consumeResult.Topic, consumeResult.Partition, consumeResult.Offset);

                    // Manual commit requested in configuration every X events
                    if (subscriber.SubscriberConfiguration.EnableAutoCommit.HasValue && !subscriber.SubscriberConfiguration.EnableAutoCommit.Value
                        && subscriber.SubscriberConfiguration.ManualCommitPeriod.HasValue
                        && consumeResult.Offset % subscriber.SubscriberConfiguration.ManualCommitPeriod.Value == 0)
                    {
                        // The Commit method sends a "commit offsets" request to the Kafka
                        // cluster and synchronously waits for the response. This is very
                        // slow compared to the rate at which the consumer is capable of
                        // consuming messages. A high performance application will typically
                        // commit offsets relatively infrequently and be designed handle
                        // duplicate messages in the event of failure.
                        try
                        {
                            subscriber?.Consumer?.Commit(consumeResult);
                        }
                        catch (KafkaException e)
                        {
                            _logger.LogError(e, "Error on Kafka subscriber on commit operation on topic {topic} for the current message {message}", subscriber?.SubscriberConfiguration?.TopicName, message as string);
                            await handler?.HandleExceptions(e);
                            await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                            {
                                BoundedContext = "Kafka",
                                RelatedKafkaMessage = msg,
                                MessageId = Guid.NewGuid().ToString(),
                                Severity = "High",
                                Message = "KafkaException raised on the commit action Process when ConsumerCheckAsync / App Reboot because Unhealthy",
                                ErrorType = "KafkaConsumerException",
                                ExceptionCaptured = e.ToString(),
                                RelatedMethod = "ConsumerCheckAsync / Commit action",
                                RelatedMs = "ITF.SharedLibraries",
                                RelateProcess = "KafkaSubscriber"
                            });
                            SetUnhealthy();
                            throw;
                        }
                    }
                }
                catch (ConsumeException e)
                {
                    _logger.LogError(e, "Error on Kafka subscriber handling operation on topic {topic} for the current message {message}", subscriber?.SubscriberConfiguration?.TopicName, message as string);
                    await handler?.HandleExceptions(e);
                    await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                    {
                        BoundedContext = "Kafka",
                        RelatedKafkaMessage = message as string,
                        MessageId = Guid.NewGuid().ToString(),
                        Severity = "High",
                        Message = "ConsumeException raised when ConsumerCheckAsync / App Reboot because Unhealthy",
                        ErrorType = "KafkaConsumerException",
                        ExceptionCaptured = e.ToString(),
                        RelatedMethod = "ConsumerCheckAsync",
                        RelatedMs = "ITF.SharedLibraries",
                        RelateProcess = "KafkaSubscriber"
                    });
                    SetUnhealthy();
                    throw;
                }
                catch (OperationCanceledException e)
                {
                    // Ensure the consumer leaves the group cleanly and final offsets are committed.
                    _logger.LogWarning("Kafka multiple consumers handler has been requested to shutdown on topic {topic} for the current message {message}", subscriber?.SubscriberConfiguration?.TopicName, message as string);
                    subscriber?.Consumer?.Close();
                    await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                    {
                        BoundedContext = "Kafka",
                        RelatedKafkaMessage = message as string,
                        MessageId = Guid.NewGuid().ToString(),
                        Severity = "High",
                        Message = "OperationCanceledException raised when ConsumerCheckAsync / App Reboot because Unhealthy",
                        ErrorType = "KafkaConsumerException",
                        ExceptionCaptured = e.ToString(),
                        RelatedMethod = "ConsumerCheckAsync",
                        RelatedMs = "ITF.SharedLibraries",
                        RelateProcess = "KafkaSubscriber"
                    });
                    SetUnhealthy();
                    throw;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Kafka exception in BackgroundService on {topic} with {errorMessage} for the current message {message}", subscriber?.SubscriberConfiguration?.TopicName, e.Message, message as string);
                    await handler?.HandleExceptions(e);
                    await SendAlertMessageToKafkaAndSlack(new ErrorMessage
                    {
                        BoundedContext = "Kafka",
                        RelatedKafkaMessage = message as string,
                        MessageId = Guid.NewGuid().ToString(),
                        Severity = "High",
                        Message = "Exception raised when ConsumerCheckAsync / App Reboot because Unhealthy",
                        ErrorType = "KafkaConsumerException",
                        ExceptionCaptured = e.ToString(),
                        RelatedMethod = "ConsumerCheckAsync",
                        RelatedMs = "ITF.SharedLibraries",
                        RelateProcess = "KafkaSubscriber"
                    });
                    SetUnhealthy();
                    throw;
                }
            }
        }

        private async Task SendAlertMessageToKafkaAndSlack(ErrorMessage error)
        {
            var kp = _serviceProvider?.GetService<IKafkaAlertingPublisher>();
            if (kp is null)
                _logger.LogError("Can't get IKafkaAlertingPublisher service from DI , no ErrorMessage has been sent to kafka");
            var sa = _serviceProvider?.GetService<ISlackAlertService>();
            if (sa is null)
                _logger.LogWarning("Can't get ISlackAlertService service from DI , no ErrorMessage has been sent to Slack");
            else
                await Result.Try(async () => await (sa?.SendErrorAlertAsync($"Error in KafkaSubscriber ConsumerCheckAsync Low level Method , see ErrorMessage : {error?.Serialize()}") ?? Task.CompletedTask));
        }

        private static List<TopicPartition> GetTopicPartitions(Configuration configuration, string topicValue)
        {
            var tp = new List<TopicPartition>();

            IAdminClient adminClient = null;

            if (string.IsNullOrWhiteSpace(configuration.Username))
            {
                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers }).Build();
            }
            else
            {
                SaslMechanism saslMechanism = SaslMechanism.Plain;
                SecurityProtocol securityProtocol = SecurityProtocol.SaslSsl;

                Enum.TryParse(configuration.SaslMechanism, true, out saslMechanism);
                Enum.TryParse(configuration.SecurityProtocol, true, out securityProtocol);

                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers, SaslUsername = configuration.Username, SaslPassword = configuration.Password, SaslMechanism = saslMechanism, SecurityProtocol = securityProtocol }).Build();
            }

            using (adminClient)
            {
                var meta = adminClient.GetMetadata(TimeSpan.FromSeconds(60));
                meta.Topics.ForEach(topic =>
                {
                    if (topic.Topic == topicValue)
                    {
                        foreach (PartitionMetadata partition in topic.Partitions)
                        {
                            tp.Add(new TopicPartition(topic.Topic, partition.PartitionId));
                        }
                    }
                });
            }
            return tp;
        }
        private bool CheckIfTopicExist(string topicName)
        {
            try
            {
                IAdminClient adminClient = null;

                if (string.IsNullOrWhiteSpace(_configuration.Username))
                {
                    adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = _configuration.BootStrapServers }).Build();
                }
                else
                {
                    SaslMechanism saslMechanism = SaslMechanism.Plain;
                    SecurityProtocol securityProtocol = SecurityProtocol.SaslSsl;

                    Enum.TryParse(_configuration.SaslMechanism, true, out saslMechanism);
                    Enum.TryParse(_configuration.SecurityProtocol, true, out securityProtocol);

                    adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = _configuration.BootStrapServers , SaslUsername = _configuration.Username , SaslPassword = _configuration.Password , SaslMechanism = saslMechanism , SecurityProtocol = securityProtocol}).Build();
                }

                using (adminClient)
                {
                    if (adminClient.GetMetadata(TimeSpan.FromSeconds(60))?.Topics?.Select(tp => tp.Topic)?.ToList()?.Contains(topicName) == false)
                        return false;
                    return true;
                }
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Error on getting metadata from Kafka - continue the init process");
                return true; // just return true to bo non blocking and continue the init process
            }
        }
        public void InitKafka()
        {
            // Instanciate all subscribers
            foreach (var s in _configuration.SubscriberConfigurations)
            {
                try
                {
                    // Any registered handler for it ?
                    var handler = _multipleSubscribers.FirstOrDefault(m => m.GetType().Name.ToLower() == s?.ClassName.ToLower());

                    if (handler == default(IMessageHandler))
                    {
                        _logger.LogError("Kafka : no handler class found for subscribers {subscriber} - Problem with settings", s?.ClassName?.ToLower());
                        throw new Exception($"Kafka : no handler class found for subscribers {s?.ClassName?.ToLower()} - Problem with settings");
                    }

                    if (CheckIfTopicExist(s?.TopicName) == false)
                    {
                        _logger.LogError("Kafka : no topic {topic} found for subscribers {subscriber} - Problem with kafka topic please create the topic first", s?.TopicName, s?.ClassName?.ToLower());
                        if (s.ThrowOnMissingTopic)
                            throw new Exception($"Kafka : no topic {s?.TopicName} found for subscribers {s?.ClassName?.ToLower()} - Problem with kafka topic please create the topic first");
                    }

                    if (s.Deserializer == Deserializer.CommerceTools && _serializerService == null)
                    {
                        _logger.LogError("The commercetools serializer haven't been supplied throught the DI for subscribers {subscriber}", s?.ClassName?.ToLower());
                        throw new Exception($"The commercetools serializer haven't been supplied throught the DI for subscribers {s?.ClassName?.ToLower()}");
                    }

                    var consumerConfig = new ConsumerConfig();
                    consumerConfig.GroupId = string.IsNullOrEmpty(s.GroupId) ? Guid.NewGuid().ToString() : s.GroupId;
                    consumerConfig.BootstrapServers = _configuration.BootStrapServers;

                    if (s.EnableAutoCommit.HasValue)
                        consumerConfig.EnableAutoCommit = s.EnableAutoCommit.Value;

                    if (s.MaxPollIntervalMs.HasValue)
                        consumerConfig.MaxPollIntervalMs = s.MaxPollIntervalMs.Value;

                    if (s.StatisticsIntervalMs.HasValue)
                        consumerConfig.StatisticsIntervalMs = s.StatisticsIntervalMs.Value;

                    if (s.SessionTimeouMs.HasValue)
                        consumerConfig.SessionTimeoutMs = s.SessionTimeouMs.Value;

                    if (s.AutoOffsetReset.HasValue)
                        consumerConfig.AutoOffsetReset = (AutoOffsetReset)s.AutoOffsetReset;

                    if (s.EnablePartitionEof.HasValue)
                        consumerConfig.EnablePartitionEof = s.EnablePartitionEof.Value;

                    if (s.SaslMechanism.HasValue)
                        consumerConfig.SaslMechanism = (SaslMechanism)s.SaslMechanism;

                    // create consumer  
                    var consumerBuilder = new ConsumerBuilder<TKey, TValue>(consumerConfig);
                    if (s.SetErrorHandler.HasValue && s.SetErrorHandler.Value)
                        consumerBuilder = consumerBuilder.SetErrorHandler(async (_, e) =>
                        {
                            await handler.HandleError(e);
                        });

                    if (s.SetLogHandler.HasValue && s.SetLogHandler.Value)
                        consumerBuilder = consumerBuilder.SetLogHandler(async (_, e) =>
                        {
                            await handler.HandleLog(e);
                        });

                    if (s.SetStatisticsHandler.HasValue && s.SetStatisticsHandler.Value)
                        consumerBuilder = consumerBuilder.SetStatisticsHandler(async (_, e) =>
                        {
                            await handler.HandleStatistics(e);
                        });

                    if (s.SetPartitionsAssignedHandler.HasValue && s.SetPartitionsAssignedHandler.Value)
                        consumerBuilder = consumerBuilder.SetPartitionsAssignedHandler(async (_, e) =>
                        {
                            await handler.HandleTopicsPartitionsAssigned(e);
                        });

                    if (s.SetPartitionsRevokedHandler.HasValue && s.SetPartitionsRevokedHandler.Value)
                        consumerBuilder = consumerBuilder.SetPartitionsRevokedHandler(async (_, e) =>
                        {
                            await handler.HandleTopicsPartitionsRevoked(e);
                        });

                    if (s.SetOffsetsCommittedHandler.HasValue && s.SetOffsetsCommittedHandler.Value)
                        consumerBuilder = consumerBuilder.SetOffsetsCommittedHandler(async (_, e) =>
                        {
                            await handler.HandleOffsetCommited(e);
                        });

                    var consumer = consumerBuilder.Build();

                    if (s.ProcessMessagesStartingDateOffset.HasValue)
                    {
                        _logger.LogInformation("Use a manual datetime offset {dateTimeOffset} for topic {topic}", s.ProcessMessagesStartingDateOffset.Value.ToString("yyyy-MM-dd HH:mm:ss"), s.TopicName);
                        // https://stackoverflow.com/questions/53968623/how-to-consume-from-specific-topicpartitionoffset-with-confluent-kafka-in-net
                        // https://stackoverflow.com/questions/60549752/re-consume-kafka-messages-from-a-given-time
                        var topicPartitions = GetTopicPartitions(_configuration, s.TopicName);

                        var offsets = new List<TopicPartitionTimestamp>();
                        foreach (var tp in topicPartitions)
                        {
                            offsets.Add(new TopicPartitionTimestamp(tp, new Timestamp(s.ProcessMessagesStartingDateOffset.Value)));
                        }

                        var seekedOffsets = consumer.OffsetsForTimes(offsets, TimeSpan.FromSeconds(60));
                        consumer.Assign(seekedOffsets);
                    }
                    else
                        // Offset according to AutoOffsetReset configuration field
                        consumer.Subscribe(s.TopicName);

                    var sub = new Subscribers<TKey, TValue>
                    {
                        ClassName = s.ClassName,
                        Consumer = consumer,
                        SubscriberConfiguration = s
                    };

                    // Feed the collection
                    _subscribers.Add(sub);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error on Kafka subscribers initialisation");
                    SetUnhealthy();
                    throw;
                }
            }
        }
    }
}
