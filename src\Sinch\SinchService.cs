﻿using Sinch;
using Sinch.SMS;
using Sinch.SMS.Batches;
using Sinch.SMS.Batches.Send;

namespace ITF.SharedLibraries.Sinch
{
    public interface ISinchService
    {
        Task<SinchOperationResult> SendSMS(List<string> recipientPhoneNumbers, string messageBody);
    }

    public class SinchService(SinchConfig sinchConfig) : ISinchService
    {
        public async Task<SinchOperationResult> SendSMS(List<string> recipientPhoneNumbers, string messageBody)
        {
            try
            {
                SinchClient? sinch = new(sinchConfig.ProjectId, sinchConfig.AccessKey, sinchConfig.AccessSecret,
                                options => { options.SmsRegion = SmsRegion.Eu; });

                IBatch response = await sinch.Sms.Batches.Send(new SendTextBatchRequest
                {
                    Body = messageBody,
                    From = sinchConfig.SinchPhoneNumber,
                    To = recipientPhoneNumbers
                });

                if (response.Canceled.HasValue && response.Canceled.Value)
                {
                    return new() { Success = false, ErrorMessage = "The batch has been canceled" };
                }

                return new() { Success = true };
            }
            catch (Exception ex)
            {
                return new() { Success = false, ErrorMessage = "An exception occured: " + ex };
            }
        }
    }

    public class SinchOperationResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; } = null;
    }
}
