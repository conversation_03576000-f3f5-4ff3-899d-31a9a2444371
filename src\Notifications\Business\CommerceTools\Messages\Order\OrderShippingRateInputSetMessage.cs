﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Order;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Order
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class OrderShippingRateInputSetMessage : BaseMessage<OrderShippingRateInputSetPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.OrderShippingRateInputSetMessage?.Resource?.Id;
            }
        }
    }
}
