using System.ComponentModel.DataAnnotations;
using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity
{
    [SwaggerSchema(Description = "NPS Email payload")]
    public class NPSEmail
    {
        [Required]
        [SwaggerSchema("The user's email address")]
        public string Email { get; set; }

        [Required]
        [SwaggerSchema("The order number associated with the email")]
        public string OrderNumber { get; set; }
    }
}