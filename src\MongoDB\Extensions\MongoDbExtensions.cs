﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ITF.SharedLibraries.EnvironmentVariable;
using MongoDB.Driver;
using MongoDB.Driver.Core.Clusters;
using System.Linq;
using MongoDB.Bson.Serialization;
using Microsoft.AspNetCore.Builder;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.DomainDrivenDesign.Interfaces;
using System;
using System.Threading.Tasks;
using Elastic.Apm.MongoDb;
using MongoDB.Bson.Serialization.Serializers;
using Serilog;
using MongoDB.Driver.Core.Events;
using Marten.Events;
using MongoDB.Driver.Core.Servers;

namespace ITF.SharedLibraries.MongoDB.Extensions
{
    public static class MongoDbExtensions
    {
        public static IServiceCollection UseMongoDb(this IServiceCollection services, IConfiguration config, string varEnv = "MongoDb")
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);

            if (!configuration.SerializeDateAsUTC)
            {
                try
                {
                    BsonSerializer.RegisterSerializer(DateTimeSerializer.LocalInstance);
                }
                catch (Exception e)
                {
                    Log.Logger.Warning(e, "Mongo BSON serializer for DateTime is already defined");
                }
            }

            services.AddSingleton<IMongoClient>(c =>
            {
                var settings = MongoClientSettings.FromConnectionString(configuration.ConnectionString);

                var eventSubscriber = new MongoDbEventSubscriber();

                settings.ClusterConfigurator = builder =>
                {
                    // Subscribe dynamically for all event types
                    builder.Subscribe<IEvent>(e =>
                    {
                        if (eventSubscriber.TryGetEventHandler<IEvent>(out var handler))
                        {
                            handler(e);
                        }
                    });
                };

                if (configuration.MinConnectionPoolSize.HasValue)
                    settings.MinConnectionPoolSize = configuration.MinConnectionPoolSize.Value;

                if (configuration.MaxConnectionPoolSize.HasValue)
                    settings.MaxConnectionPoolSize = configuration.MaxConnectionPoolSize.Value;

                if (configuration.MaxConnecting.HasValue)
                    settings.MaxConnecting = configuration.MaxConnecting.Value;

                if (configuration.WaitQueueTimeoutInMilliseconds.HasValue)
                    settings.WaitQueueTimeout = TimeSpan.FromMilliseconds(configuration.WaitQueueTimeoutInMilliseconds.Value);

                //[Deprecated]
                //settings.WaitQueueSize = 2000; // https://stackoverflow.com/questions/68046353/mongodb-mongoclientsettings-property-deprecation
                return new MongoClient(settings);
            });

            return services;
        }
        public static void RegisterClassMap<T>()
        {
            BsonClassMap.RegisterClassMap<T>();
        }

        public static void WarmUpMongoRepository<T, TId>(this IApplicationBuilder app, Func<T, Task> warmUpAction = null) 
            where T : IMongoRepository<TId>
            where TId : BaseClass<string>
        {
            var repo = app.ApplicationServices.GetService<T>();
            if (repo != null)
            {
                if (warmUpAction != null)
                {
                    var task = warmUpAction.Invoke(repo);
                    task.Wait();
                }
                else
                    repo.WarmUp();
            }
        }
        public static void WarmUpMongoRepository<H, T, TId>(this IApplicationBuilder app, Func<H, Task> warmUpAction = null)
        where H : IMongoAggregateStoreRepository<T, TId>
        where T : AggregateRoot<TId>
        where TId : IValueId
        {
            var repo = app.ApplicationServices.GetService<H>();
            if (repo != null)
            {
                if (warmUpAction != null)
                {
                    var task = warmUpAction.Invoke(repo);
                    task.Wait();
                }
                else
                    repo.WarmUp();
            }
        }

        public static bool AreTransactionsAllowed(this IClientSessionHandle clientSessionHandle)
        {
            var clusterDescription = clientSessionHandle.Client.Cluster.Description;

            // Check if there are no servers or if all servers are standalone
            if (!clusterDescription.Servers.Any())
            {
                // No servers available; treat this as "Unknown"
                return false;
            }

            // Check if the cluster is standalone
            var isStandalone = clusterDescription.Servers.All(server =>
                server.Type.ToString().Equals("Standalone", StringComparison.OrdinalIgnoreCase));

            // Transactions are not allowed on standalone or unknown clusters
            return !isStandalone;
        }
    }
}
