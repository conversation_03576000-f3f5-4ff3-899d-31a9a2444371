using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;

namespace ITF.SharedLibraries.Emarsys;

public static partial class Messages
{
    public static partial class V1
    {
        public class NPSEmailMessage : BaseMessage<NPSEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.NPSEmails.FirstOrDefault()?.Email;
        }
    }
}

public class NPSEmailPayload : IPayload
{
    public List<NPSEmail> NPSEmails { get; set; } = new();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}