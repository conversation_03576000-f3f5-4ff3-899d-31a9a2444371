﻿using ITF.SharedLibraries.EnvironmentVariable;
using <PERSON><PERSON><PERSON>;
using Jaeger.Reporters;
using Jaeger.Samplers;
using Jaeger.Senders.Thrift;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OpenTracing;
using OpenTracing.Util;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace ITF.SharedLibraries.Jaeger.Extensions
{
    public static class JaegerExtensions
    {
        public static IServiceCollection AddJaeger(this IServiceCollection services, IConfiguration config, string varEnv = "Jaeger")
        {
            var configuration = config.Get<Configuration>(varEnv);
            string serviceName = Assembly.GetEntryAssembly().GetName().Name;

            services.AddSingleton<ITracer>(serviceProvider =>
            {
                var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
                var endpoint = configuration.Jaeger;

                var remoteReporter = new RemoteReporter.Builder()
                .WithLoggerFactory(loggerFactory) // optional, defaults to no logging
                .WithMaxQueueSize(100)            // optional, defaults to 100
                .WithFlushInterval(TimeSpan.FromSeconds(1)) // optional, defaults to TimeSpan.FromSeconds(1)
                .WithSender(new HttpSender(endpoint))   // optional, defaults to UdpSender("localhost", 6831, 0)
                .Build();

                var loggingReporter = new LoggingReporter(loggerFactory);
                var compositeReporter = new CompositeReporter(remoteReporter, loggingReporter);
                var sampler = new ConstSampler(true);

                ITracer tracer = new Tracer.Builder(serviceName)
                    .WithSampler(sampler)
                    .WithReporter(compositeReporter)
                    .Build();

                // Usefull for OpenTracingSink logger
                GlobalTracer.Register(tracer);

                return tracer;
            });


            return services;
        }
    }
}
