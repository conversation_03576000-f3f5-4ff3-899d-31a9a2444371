﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Order;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Order
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class OrderBillingAddressSetMessage : BaseMessage<OrderBillingAddressSetPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.OrderBillingAddressSetMessage?.Resource?.Id;
            }
        }
    }
}
