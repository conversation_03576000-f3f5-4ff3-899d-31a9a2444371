﻿using ITF.SharedLibraries.Kafka.Publisher;
using ITF.SharedLibraries.Kafka.Subscriber;
using System.Collections.Generic;

namespace ITF.SharedLibraries.Kafka
{
    public class Configuration
    {
        public Configuration()
        {
            SubscriberConfigurations = new List<SubscriberConfiguration>();
            TopicsToCreateConfigurations = new List<TopicsToCreateConfiguration>();
        }

        public string BootStrapServers { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string SaslMechanism { get; set; }
        public string SecurityProtocol { get; set; }
        public double? LingerMs { get; set; }
        public int? QueueBufferingMaxKbytes { get; set; }
        public int? BatchSize { get; set; }
        public int? CompressionType { get; set; }
        public int? Acks { get; set; }
        public string KafkaProducerTopicName { get; set; }
        public int? MessageSendMaxRetries { get; set; }
        public int? RetryBackoffMs { get; set; }
        public bool? EnableIdempotence { get; set; }
        public int? MessageTimeoutMs { get; set; }
        public int? MessageMaxBytes { get; set; }
        public bool? UseAlerting { get; set; } = true;

        public List<TopicsToCreateConfiguration> TopicsToCreateConfigurations { get; set; }
        public List<SubscriberConfiguration> SubscriberConfigurations { get; set; }
    }
}
