﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.Readyness.Extensions
{
    public static class ReadynessExtensions
    {
        public static IApplicationBuilder AddReadynessRoute(this IApplicationBuilder applicationBuilder, string routeName = "/readyness", string message = "Ready")
        {
            return applicationBuilder.Map($"{routeName}", versionApp =>
                    versionApp.UseMiddleware<ReadynessMiddleware>(message));
        }

        public static void UseReadynessRoute(this IEndpointRouteBuilder endpointRouteBuilder, string url = "/readyness", string message = "Ready")
        {
            endpointRouteBuilder.MapGet(url, context =>
            {
                return context.Response.WriteAsync(message);
            });
        }
    }                  
}
