﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderRecipientNameUpdatedMessage : BaseMessage<LegacyOrderRecipientNameUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                => Payload?.OrderIdentifier + "-" + Payload?.RecipientFirstName + "-" + Payload?.RecipientLastName + "-" + Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderRecipientNameUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderRecipientNameUpdatedMessage = new LegacyOrderRecipientNameUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderRecipientNameUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            RecipientFirstName = payload.Recipient.FirstName,
                            RecipientLastName = payload.Recipient.LastName
                        }
                    };


                    return legacyOrderRecipientNameUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderRecipientNameUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderRecipientNameUpdatedPayload>
    {
        public string RecipientFirstName { get; set; }
        public string RecipientLastName { get; set; }
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrderRecipientNameUpdatedPayload parameter)
        {
            return (RecipientFirstName == parameter.RecipientFirstName &&
                RecipientLastName == parameter.RecipientLastName &&
                OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderRecipientNameUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            RecipientFirstName,
            RecipientLastName,
            OrderIdentifier
        }.GetHashCode();
    }
}
