﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using System.Net;
using System.Text.Json;

namespace ITF.SharedLibraries.HttpClient;

public static class HttpResponseExtensions
{
    public static async Task<T?> GetJsonPropertyAsync<T>(this HttpResponseMessage? response, string propertyName)
    {
        if (!(response?.IsSuccessStatusCode ?? false))
            return default;

        var jsonContent = await response.Content.ReadAsStringAsync();
        using var document = JsonDocument.Parse(jsonContent);

        if (document.RootElement.TryGetProperty(propertyName, out var element))
            return element.Deserialize<T>();

        return default;
    }
}

public static class Extensions
{

    public static IHttpClientBuilder AddHttpClientWithDefaultPolicy<T, H>(this IServiceCollection app, IConfiguration config, string varEnv)
        where T : class, IHttpClient
        where H : class, T
    {
        var configuration = config.Get<Configuration>(varEnv);

        if (configuration.DefaultConnectionLimit != 0)
            ServicePointManager.DefaultConnectionLimit = configuration.DefaultConnectionLimit;

        ServicePointManager.Expect100Continue = !configuration.DisableExpect100ToContinue;
        ServicePointManager.UseNagleAlgorithm = !configuration.DisableNagleAlgorithm;

        if (configuration.WorkerThreads > 0 && configuration.CompletionPortThreads > 0)
            System.Threading.ThreadPool.SetMinThreads(configuration.WorkerThreads, configuration.CompletionPortThreads);

        int httpTimeout = 60;
        if (configuration.HttpTimeoutInSeconds > 0)
            httpTimeout = configuration.HttpTimeoutInSeconds;

        int handlerLifetime = 5;
        if (configuration.HandlerLifetime > 0)
            handlerLifetime = configuration.HandlerLifetime;

        int policyTimeout = 20;
        if (configuration.PolicyTimeoutInSeconds > 0)
            policyTimeout = configuration.PolicyTimeoutInSeconds;

        var httpClient = app.AddHttpClient<T, H>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(httpTimeout);
        });

        if (configuration.DisableCookieAffinity)
        {
            httpClient.ConfigurePrimaryHttpMessageHandler(() =>
            {
                return new HttpClientHandler()
                {
                    UseCookies = !configuration.DisableCookieAffinity,
                };
            });
        }

        return httpClient
        .SetHandlerLifetime(TimeSpan.FromMinutes(handlerLifetime))
        .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(policyTimeout));
    }
}
