﻿using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.RabbitMQ.Subscriber
{
    public class RabbitMQMultipleSubscribers : BackgroundService
    {
        private readonly ILogger _logger;
        private IList<Subscribers> _subscribers;
        private readonly Configuration _configuration;
        private readonly IList<IMessageHandler> _multipleSubscribers;  

        public RabbitMQMultipleSubscribers(ILoggerFactory loggerFactory , Configuration configuration, IEnumerable<IMessageHandler> multipleSubscribers)
        {
            _logger = loggerFactory.CreateLogger<RabbitMQMultipleSubscribers>();
            _subscribers = new List<Subscribers>();
            _configuration = configuration;
            _multipleSubscribers = multipleSubscribers.ToList();
            InitRabbitMQ();         
        }

        private void InitRabbitMQ()
        {
            var factory = new ConnectionFactory()
            {
                HostName = _configuration.HostName,
                UserName = _configuration.UserName,
                Password = _configuration.Password,
                Port = _configuration.Port,
                VirtualHost = _configuration.VHost,
            };

            // Instanciate all subscribers
            foreach (var s in _configuration.SubscriberConfigurations)
            {
                try
                {
                    // Any registered handler for it ?
                    var handler = _multipleSubscribers.FirstOrDefault(m => m.GetType().Name.ToLower() == s.ClassName.ToLower());

                    if (handler == default(IMessageHandler))
                        continue;

                    // create connection  
                    var connection = factory.CreateConnectionAsync().GetAwaiter().GetResult();

                    // create channel  
                    var channel = connection.CreateChannelAsync().GetAwaiter().GetResult();

                    channel.ExchangeDeclareAsync(s.Exchange, ExchangeType.Topic, true).GetAwaiter().GetResult();
                    channel.QueueDeclareAsync(s.Queue, true, false, false, null).GetAwaiter().GetResult();
                    channel.QueueBindAsync(s.Queue, s.Exchange, s.RoutingKey, null).GetAwaiter().GetResult();
                    channel.BasicQosAsync(0, 1, false).GetAwaiter().GetResult();

                    connection.ConnectionShutdownAsync += handler.RabbitMQ_ConnectionShutdown;

                    var sub = new Subscribers
                    {
                        ClassName = s.ClassName,
                        Channel = channel,
                        Connection = connection,
                        Consumer = null,
                        SubscriberConfiguration = s
                    };

                    // Feed the collection
                    _subscribers.Add(sub);

                    handler.InitSubscriber(sub);
                }
                catch (Exception e)
                {
                   _logger.LogError(e, "Error on RabbitMq subscribers initialisation");
                    throw;
                }
            }
        }

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            stoppingToken.ThrowIfCancellationRequested();

            // Handling all queues processing
            foreach(var s in _subscribers.ToList())
            {
                // Any registered handler for it ?
                var handler = _multipleSubscribers.FirstOrDefault(m => m.GetType().Name.ToLower() == s.ClassName.ToLower());

                if (handler == default(IMessageHandler))
                    continue;

                s.Consumer = new AsyncEventingBasicConsumer(s.Channel);

                s.Consumer.ReceivedAsync += handler.HandleMessageAsync;
                s.Consumer.ShutdownAsync += handler.OnConsumerShutdown;
                s.Consumer.RegisteredAsync += handler.OnConsumerRegistered;
                s.Consumer.UnregisteredAsync += handler.OnConsumerUnregistered;

                s.Channel.BasicConsumeAsync(s.SubscriberConfiguration.Queue, s.SubscriberConfiguration.AutoAck, s.Consumer).GetAwaiter().GetResult();
            }

            return Task.CompletedTask;
        }

        public override void Dispose()
        {
            // Handling all queues processing
            _subscribers.ToList().ForEach(s =>
            {
                s.Channel.CloseAsync().GetAwaiter().GetResult();
                s.Connection.CloseAsync().GetAwaiter().GetResult();
            });

            base.Dispose();
        }
    }
}
