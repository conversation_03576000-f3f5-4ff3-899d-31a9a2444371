﻿using ITF.Lib.Common.DomainDrivenDesign;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ElasticSearch.Constants;

namespace ITF.SharedLibraries.ElasticSearch.Repository
{
    // https://www.elastic.co/guide/en/elasticsearch/client/net-api/7.x/nest-getting-started.html

    // Solve diskpace low => read only
    // https://selleo.com/til/posts/esrgfyxjee-how-to-fix-elasticsearch-forbidden12index-read-only
    public abstract class ElasticSearchRepository<T, K> : IElasticSearchRepository<T, K> 
        where T : BaseClass<K>, IMappings<T>, new()
    {
        private readonly IElasticClient _elasticClient;
        private readonly string _index;
        private readonly bool _readOnlyMode;

        public ElasticSearchRepository(IElasticClient elasticClient, string index, bool readOnly = false)
        {
            _elasticClient = elasticClient;
            _readOnlyMode = readOnly;
            _index = index.ToLower();

            if (_readOnlyMode)
                return;

            var exist = _elasticClient.Indices.Exists(_index);
            if (!exist.Exists)
            {
                var res = _elasticClient.Indices.Create(_index,
                    c => c
                        .Settings(s => s.Analysis(a => a
                            .Normalizers(n => n.Custom(NORMALIZER, c => c.Filters(LOWERCASE)))
                        )))
                    ;

                if (!res.IsValid && res.OriginalException is not null)
                    throw res.OriginalException;
            }

            MapFields<T>();
        }

        private void MapFields<J>() where J : IMappings<T>, new()
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var instance = Activator.CreateInstance<J>();
            var mappingSettings = instance.GetMappingProperties();
            var res = _elasticClient.Map<T>(x => x.Index(_index).AutoMap().Properties(mappingSettings));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;
        }

        public virtual async Task<GetResponse<T>> GetById(DocumentPath<T> Id)
        {
            var res = await _elasticClient.GetAsync(Id, idx => idx.Index(_index));
            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public virtual async Task<IEnumerable<IMultiGetHit<T>>> GetByIds(IEnumerable<string> Ids)
        {
            var res = await _elasticClient.GetManyAsync<T>(Ids, _index);
            return res;
        }


        // https://towardsdatascience.com/deep-dive-into-querying-elasticsearch-filter-vs-query-full-text-search-b861b06bd4c0
        // https://stackoverflow.com/a/37714606/4734707
        // https://stackoverflow.com/a/35585489/4734707
        // https://www.elastic.co/guide/en/elasticsearch/client/net-api/current/writing-queries.html
        public virtual async Task<IReadOnlyCollection<T>> Get(Func<QueryContainerDescriptor<T>, QueryContainer> query)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Query(query));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Size(size).Query(query));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;
            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get(int size, Func<SourceFilterDescriptor<T>, ISourceFilter> source, Func<QueryContainerDescriptor<T>, QueryContainer> query)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Size(size).Source(source).Query(query));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;
            return searchResponse.Documents;
        }

        public virtual async Task<CountResponse> CountAsync(Func<QueryContainerDescriptor<T>, QueryContainer> query)
        {
            var count = await _elasticClient.CountAsync<T>(c => c.Index(_index).Query(query));
            if (!count.IsValid && count.OriginalException is not null)
                throw count.OriginalException;

            return count;
        }

        public virtual async Task<bool> AnyAsync()
        {
            var count = await _elasticClient.CountAsync<T>(c => c.Index(_index));
            if (!count.IsValid && count.OriginalException is not null)
                throw count.OriginalException;

            return count.Count > 0;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query, Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Size(size).Query(query).Sort(sort));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get(
            int size,
            Func<SourceFilterDescriptor<T>, ISourceFilter> source,
            Func<ScriptFieldsDescriptor, IPromise<IScriptFields>> scripts,
            Func<QueryContainerDescriptor<T>, QueryContainer> query,
            Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort,
            string index = null)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(
                s => s.Index(index is null ? _index : index.ToLower())
                .Size(size)
                .Source(source)
                .ScriptFields(scripts)
                .Query(query)
                .Sort(sort));

            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get(int size, int from , Func<QueryContainerDescriptor<T>, QueryContainer> query)
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(
                s => s.Index(_index)
                .Size(size)
                .Query(query)
                .From(from));

            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> GetAll()
        {
            var finalResults = new List<T>();
            var scanResults = await _elasticClient.SearchAsync<T>(s => s.Index(_index)
                            .From(0)
                            .Size(100)
                            .MatchAll()
                            .SearchType(Elasticsearch.Net.SearchType.QueryThenFetch)
                            .Scroll("5M")
                        );

            if (!scanResults.IsValid && scanResults.OriginalException is not null)
                throw scanResults.OriginalException;

            finalResults.AddRange(scanResults.Documents);
            var results = await _elasticClient.ScrollAsync<T>("10m", scanResults.ScrollId);

            if (!results.IsValid && results.OriginalException is not null)
                throw results.OriginalException;

            do
            {
                if(results.Documents.Count > 0)
                    finalResults.AddRange(results.Documents);

                var nbFinalResults = finalResults.Count;
                var nbFresults = results.Documents.Count;

                results = await _elasticClient.ScrollAsync<T>("10m", results.ScrollId);
                if (!results.IsValid && results.OriginalException is not null)
                    throw results.OriginalException;

                nbFresults = results.Documents.Count;
            } while (results.Documents.Any());

            return finalResults;
        }



        public virtual async Task<(IReadOnlyCollection<H> results, long totalCount)> Get<H>(
            int size,
            int from,
            Func<SourceFilterDescriptor<T>, ISourceFilter> source,
            Func<ScriptFieldsDescriptor, IPromise<IScriptFields>> scripts,
            Func<QueryContainerDescriptor<T>, QueryContainer> query,
            Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort,
            string scriptField,
            string index = null) 
            where H : BaseClass<K>, IBaseScriptFields

        {
            var searchResponse = await _elasticClient.SearchAsync<T, H>(
                s => s.Index(index is null ? _index : index.ToLower())
                .From(from)
                .Size(size)
                .Source(source)
                .ScriptFields(scripts)
                .Query(query)
                .Sort(sort));

            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            var results = searchResponse.Documents;

            foreach (var hit in searchResponse.Hits)
            {
                var iterateResult = results.FirstOrDefault(r => hit.Id == r.Id?.ToString());
                if (iterateResult != null)
                {
                    iterateResult.Fields = hit.Fields;
                    if (hit.Fields.Select(s => s.Key).Contains(scriptField))
                        iterateResult.ScriptFields = hit.Fields[scriptField].As<List<object>>();
                }                  
            }

            return (results, searchResponse.Total);
        }

        public virtual async Task<IndexResponse> Add(T entity)
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var res = await _elasticClient.IndexAsync(entity, idx => idx.Index(_index));
            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public virtual async Task<BulkResponse> AddMany(List<T> entities)
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var res = await _elasticClient.IndexManyAsync(entities, _index);
            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }
            

        // https://stackoverflow.com/a/58832740/4734707
        public virtual async Task<UpdateResponse<T>> Update(DocumentPath<T> Id, T newEntity)
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var res = await _elasticClient.UpdateAsync(Id,
                u => u.Index(_index)
                .Doc(newEntity));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public virtual async Task<BulkResponse> UpdateMany(List<T> newEntities , int? retriesOnConflict = 10)
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var res = await _elasticClient.BulkAsync(b => b
                .Index(_index)
                .UpdateMany(newEntities, (d,doc) => d
                    .RetriesOnConflict(retriesOnConflict)
                    .Doc(doc)
                    .Upsert(doc)));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public virtual async Task<DeleteResponse> Remove(DocumentPath<T> Id)
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var res = await _elasticClient.DeleteAsync(Id,
                u => u.Index(_index));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public string GetIndex() => _index;
    }
}
