﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Customer
{
    public class CustomerAddressAddedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public CustomerAddressAddedMessage CustomerAddressAddedMessage { get; set; }
    }
}
