﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;

namespace ITF.SharedModels.Notifications.Business.Legacy.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class OrderDeliveryCourierUpdatedMessage : BaseMessage<OrderDeliveryCourierUpdatedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.OrderId;
            }
        }
    }
}
