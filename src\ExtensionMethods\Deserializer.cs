﻿using commercetools.Sdk.Api.Serialization;
using MessagePack;
using MessagePack.Resolvers;
using Newtonsoft.Json;
using Serilog;
using System;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace ITF.SharedLibraries.ExtensionMethods
{
    public static class Deserializer
    {

        private static IFormatterResolver FormatterResolver => CompositeResolver.Create(
                 // resolver custom types first
                 NativeDateTimeResolver.Instance,
                ContractlessStandardResolver.Instance,
                TypelessContractlessStandardResolver.Instance
            );

        public static MessagePackSerializerOptions MessagePackSerializerOptions =>
            MessagePackSerializerOptions.Standard.WithResolver(FormatterResolver);

        public static T Deserialize<T>(this string o, SerializerType serializerType = SerializerType.TextJson, SerializerService serializerService = null , JsonSerializerSettings settings = null)
        {
            try
            {
                if (string.IsNullOrEmpty(o))
                    return default;

                switch (serializerType)
                {
                    case SerializerType.NewtonSoft:
                        return JsonConvert.DeserializeObject<T>(o,settings);
                    case SerializerType.TextJson:
                        return System.Text.Json.JsonSerializer.Deserialize<T>(o);
                    case SerializerType.Utf8Json:
                        return Utf8Json.JsonSerializer.Deserialize<T>(o);
                    case SerializerType.MessagePack:
                        return MessagePackSerializer.Deserialize<T>(MessagePackSerializer.ConvertFromJson(o, MessagePackSerializerOptions), MessagePackSerializerOptions);
                    case SerializerType.CommerceTools:
                        if (serializerService is null)
                            throw new Exception($"SerializerService must be supplied when using {SerializerType.CommerceTools} Deserializer");
                        return serializerService.Deserialize<T>(o);
                    default:
                        return JsonConvert.DeserializeObject<T>(o);
                }
            }
            catch(Exception e)
            {
                Log.Logger.Error(e, e.Message);
                return default;
            }
        }
    }
}
