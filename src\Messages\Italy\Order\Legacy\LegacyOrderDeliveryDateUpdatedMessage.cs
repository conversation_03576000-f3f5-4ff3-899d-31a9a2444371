﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderDeliveryDateUpdatedMessage : BaseMessage<LegacyOrderDeliveryDateUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderDeliveryDateUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryDateUpdatedMessage = new LegacyOrderDeliveryDateUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryDateUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Date = payload.Delivery.Date
                        }
                    };


                    return legacyOrderDeliveryDateUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderDeliveryDateUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderDeliveryDateUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public DateTime Date { get; set; }

        public bool Equals(LegacyOrderDeliveryDateUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Date == parameter.Date
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderDeliveryDateUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Date
        }.GetHashCode();
    }

}
