﻿using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Order;
using MongoDB.Driver;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public interface IOrderCustomerFeedbackRepository : IMongoRepository<OrderCustomerFeedback>
    {
    }
    public class OrderCustomerFeedbackRepository(IMongoClient mongoClient) : MongoRepository<OrderCustomerFeedback>(mongoClient, "it-florist", "order_customerFeedback"), IOrderCustomerFeedbackRepository
    {
       
    }
}
