﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class AbandonedCartEmailMessage : BaseMessage<AbandonedCartEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.CartEmails?.FirstOrDefault()?.Customer?.Email;
        }
    }
}

public class AbandonedCartEmailPayload : IPayload
{
    public List<CartEmail> CartEmails { get; set; } = new List<CartEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
