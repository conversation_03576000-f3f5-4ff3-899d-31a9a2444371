﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderAcceptedOnBehalfMessage : BaseMessage<LegacyOrdeAcceptedOnBehalfPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyOrdeAcceptedOnBehalfPayload : LegacyPayload, IEquatable<LegacyOrdeAcceptedOnBehalfPayload>
    {
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrdeAcceptedOnBehalfPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrdeAcceptedOnBehalfPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier
        }.GetHashCode();
    }
}
