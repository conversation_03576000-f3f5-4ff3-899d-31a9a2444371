﻿using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Retry;
using System;

namespace ITF.SharedLibraries.Polly
{
    public static class PollyPolicyFactory
    {
        public static AsyncRetryPolicy GetAsyncPolicy<T>(ILogger logger, int nbRetryCount = 2, int secondsToWaitBetweenTries = 2) where T : Exception
            => Policy
            .Handle<T>()
            .WaitAndRetryAsync(nbRetryCount, retryAttempt => TimeSpan.FromSeconds(Math.Pow(secondsToWaitBetweenTries, retryAttempt)), (e, t, n, c) =>
            {
                logger?.LogWarning("Polly is retrying ({attemptNb} attempts) current process on error '{errorMessage}' after {elapsed} seconds elapsed", n, e?.ToString(), t);
            });

        // https://github.com/App-vNext/Polly/issues/21#issuecomment-70949481
        public static AsyncRetryPolicy GetAsyncPolicyWithExceptionPredicate<T>(ILogger logger, Func<T, bool> exceptionPredicate, int nbRetryCount = 2, int secondsToWaitBetweenTries = 2) where T : Exception
            => Policy
            .Handle<T>(exceptionPredicate)
            .WaitAndRetryAsync(nbRetryCount, retryAttempt => TimeSpan.FromSeconds(Math.Pow(secondsToWaitBetweenTries, retryAttempt)), (e, t, n, c) =>
            {
                logger?.LogWarning("Polly is retrying ({attemptNb} attempts) current process on error '{errorMessage}' after {elapsed} seconds elapsed", n, e?.ToString(), t);
            });

        public static RetryPolicy GetPolicy<T>(ILogger logger, int nbRetryCount = 2, int secondsToWaitBetweenTries = 2) where T : Exception
            => Policy
            .Handle<T>()
            .WaitAndRetry(nbRetryCount, retryAttempt => TimeSpan.FromSeconds(Math.Pow(secondsToWaitBetweenTries, retryAttempt)), (e, t, n, c) =>
            {
                logger?.LogWarning("Polly is retrying ({attemptNb} attempts) current process on error '{errorMessage}' after {elapsed} seconds elapsed", n, e?.ToString(), t);
            });

        // https://github.com/App-vNext/Polly/issues/21#issuecomment-70949481
        public static RetryPolicy GetPolicyWithExceptionPredicate<T>(ILogger logger, Func<T, bool> exceptionPredicate, int nbRetryCount = 2, int secondsToWaitBetweenTries = 2) where T : Exception
            => Policy
            .Handle<T>(exceptionPredicate)
            .WaitAndRetry(nbRetryCount, retryAttempt => TimeSpan.FromSeconds(Math.Pow(secondsToWaitBetweenTries, retryAttempt)), (e, t, n, c) =>
            {
                logger?.LogWarning("Polly is retrying ({attemptNb} attempts) current process on error '{errorMessage}' after {elapsed} seconds elapsed", n, e?.ToString(), t);
            });
    }
}
