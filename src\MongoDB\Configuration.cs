﻿namespace ITF.SharedLibraries.MongoDB;

public class Configuration
{
    public string DatabaseName { get; set; }
    public string DatabaseNameSubset { get; set; }
    public string ConnectionString { get; set; }
    public string CollectionName { get; set; }
    public int? MinConnectionPoolSize { get; set; }
    public int? MaxConnectionPoolSize { get; set; }
    public int? MaxConnecting { get; set; }
    public int? WaitQueueTimeoutInMilliseconds { get; set; }
    public bool SerializeDateAsUTC { get; set; }

    // TTL Index Configuration
    public string TtlFieldName { get; set; } = "createdAt";
    public int? TtlExpirationSeconds { get; set; }
    public bool TtlIndexCreationOnExistingCollection { get; set; } = false;
    public bool ForceTtlIndexCreation { get; set; } = false;
}
