﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using Unleash;
using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.FeatureManagement;
using System.Collections.Generic;
using Serilog;

namespace ITF.SharedLibraries.FeatureFlags
{
    public static class FeatureFlagsExtensions
    {
        public static IServiceCollection UseFeatureFlags(this IServiceCollection services, IConfiguration config)
        {
            try
            {
                // Feature management
                services.AddFeatureManagement();

                // Façade
                services.AddSingleton<IFeatureFlags, FeatureFlags>();

                // Global
                var configuration = config.Get<Configuration>("FeatureFlags");
                services.AddSingleton(configuration);

                // Unleash
                services.UseUnleash(config);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Can't use FeatureFlags, some settings are missing. The application continues running anyway");
            }
            return services;
        }

        private static IServiceCollection UseUnleash(this IServiceCollection services, IConfiguration config)
        {
            // Unleash
            var unleashConfiguration = config.Get<Unleash.Configuration>("Unleash");
            services.AddSingleton(unleashConfiguration);

            Dictionary<string, string> CustomHttpHeaders = new()
            {
                { "Authorization", unleashConfiguration.Key }
            };
            services.AddSingleton<IUnleash>(c => new DefaultUnleash(new UnleashSettings()
            {
                UnleashApi = new Uri(unleashConfiguration.Url),
                AppName = unleashConfiguration.ApplicationName,
                ProjectId = unleashConfiguration.ProjectId,
                SendMetricsInterval = TimeSpan.FromSeconds(unleashConfiguration.SendMetricsIntervalInSeconds),
                FetchTogglesInterval = TimeSpan.FromSeconds(unleashConfiguration.FetchTogglesIntervalInSeconds),
                CustomHttpHeaders = CustomHttpHeaders,
                Environment = unleashConfiguration.Environment
            }));

            return services;
        }
    }
}
