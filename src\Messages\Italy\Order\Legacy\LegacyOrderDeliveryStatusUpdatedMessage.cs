﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Shopopop.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderDeliveryStatusUpdatedMessage : BaseMessage<LegacyOrderDeliveryStatusUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderDeliveryStatusUpdatedMessage((string ctOrderId, OrderDeliveryCourierUpdatedMessage message, DeliveryStatusEnum deliveryStatusEnum)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryStatusUpdatedMessage = new LegacyOrderDeliveryStatusUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryStatusUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            DeliveryStatus = src.deliveryStatusEnum,
                            OrderIdentifier = src.ctOrderId
                        }
                    };


                    return legacyOrderDeliveryStatusUpdatedMessage;
                }

                public static implicit operator LegacyOrderDeliveryStatusUpdatedMessage((string ctOrderId, OrderDeliveryCourierResetedMessage message) src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryStatusUpdatedMessage = new LegacyOrderDeliveryStatusUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryStatusUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            DeliveryStatus = DeliveryStatusEnum.CANCELLED,
                            OrderIdentifier = src.ctOrderId
                        }
                    };


                    return legacyOrderDeliveryStatusUpdatedMessage;
                }

                public static implicit operator LegacyOrderDeliveryStatusUpdatedMessage((string ctOrderId, OrderDeliveryCourierInitializedMessage message) src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryStatusUpdatedMessage = new LegacyOrderDeliveryStatusUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryStatusUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            DeliveryStatus = DeliveryStatusEnum.SCHEDULED,
                            OrderIdentifier = src.ctOrderId
                        }
                    };


                    return legacyOrderDeliveryStatusUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderDeliveryStatusUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderDeliveryStatusUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public DeliveryStatusEnum DeliveryStatus { get; set; }

        public bool Equals(LegacyOrderDeliveryStatusUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                DeliveryStatus == parameter.DeliveryStatus
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderDeliveryStatusUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            DeliveryStatus
        }.GetHashCode();
    }

}
