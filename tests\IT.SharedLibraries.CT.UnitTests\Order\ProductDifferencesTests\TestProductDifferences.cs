﻿using commercetools.Sdk.Api;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Serialization;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using Yggdrasil;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace IT.SharedLibraries.CT.UnitTests.Order.ProductDifferencesTests
{
    public class TestProductDifferences
    {
        private SerializerService GetSerializerService()
        {
            var services = new ServiceCollection();
            services.UseCommercetoolsApiSerialization();
            var serviceProvider = services.BuildServiceProvider();
            var serializerService = serviceProvider.GetService<IApiSerializerService>();
            return (SerializerService)serializerService;
        }
        [Fact]
        public void MultiTestDebugThroughJson()//Uncomment if you want test with json resourcefile, but recomment if you want avoir pipeline building about lack of embedded resource file
        {
            //List<KeyValuePair<OrderDifference, object>> differences = new();
            //var orderService = new OrderService(null, null, new NullLogger<OrderService>(), null, null, null, null, null, null, null, null, null);
            //string ctLineItemsJson = File.ReadAllText("CtLineItems.json");
            //string raoLineItemsJson = File.ReadAllText("RaoLineItemsIncoming.json");
            //SerializerService serializerService = GetSerializerService();

            //var ctLineItems = serializerService.Deserialize<List<ILineItem>>(ctLineItemsJson);
            //var raoLineItems = JsonConvert.DeserializeObject<List<ProductInformations>>(raoLineItemsJson);

            //var ctProductList = ctLineItems.MapOrderLineItemsToRAOLineItems();
            //var incomingProducts = raoLineItems.GetEligibleProducts();

           

            //orderService.HandleProductDifferences(differences, incomingProducts, ctProductList);


            Assert.True(true);
        }
    }
}
