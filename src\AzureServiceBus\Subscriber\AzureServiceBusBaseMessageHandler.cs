﻿using Azure.Messaging.ServiceBus;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public abstract class AzureServiceBusBaseMessageHandler : IMessageHandler
    {
        public virtual Task HandleMessage(ProcessMessageEventArgs message, string topic = null, string subscription = null)
        {
            throw new NotImplementedException();
        }
    }
}
