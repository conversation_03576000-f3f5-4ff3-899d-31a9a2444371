﻿using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using ITF.SharedLibraries.ElasticSearch.Repository;
using System.Threading.Tasks;
using EventStore.Client;

namespace ITF.SharedLibraries.EventSourcing.Projections.Repositories
{
    public abstract class ElasticSearchCheckPointRepository<T> : IEventStoreCheckpointStore where T : ElasticSearchCheckPointPosition
    {
        protected readonly string _checkPointName;
        protected readonly IElasticSearchRepository<T, string> _checkPointRepository;

        public ElasticSearchCheckPointRepository(IElasticSearchRepository<T, string> checkPointRepository, string checkPointName)
        {
            _checkPointRepository = checkPointRepository;
            _checkPointName = checkPointName;
        }

        public async virtual Task<Position> GetCheckpoint()
        {
            var result = await _checkPointRepository.GetById(_checkPointName);

            if (!result.IsValid || result.Source is null)
            {
                var position = new Position(0, 0);
                await InsertCheckpoint(position);
                return position;
            }

            return new Position(result.Source.CommitPosition, result.Source.PreparePosition);
        }

        protected async virtual Task InsertCheckpoint(Position checkpoint)
        {
            var data = new ElasticSearchCheckPointPosition(_checkPointName, checkpoint.PreparePosition, checkpoint.CommitPosition);
            await _checkPointRepository.Add(data as T);
        }

        public async virtual Task StoreCheckpoint(Position checkpoint)
        {
            var data = await _checkPointRepository.GetById(_checkPointName);

            data.Source.CommitPosition = checkpoint.CommitPosition;
            data.Source.PreparePosition = checkpoint.PreparePosition;
            await _checkPointRepository.Update(_checkPointName, data.Source);
        }
    }
}
