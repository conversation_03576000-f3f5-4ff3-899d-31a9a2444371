﻿using FS.Keycloak.RestApiClient.Api;
using FS.Keycloak.RestApiClient.Authentication.ClientFactory;
using FS.Keycloak.RestApiClient.Authentication.Flow;
using FS.Keycloak.RestApiClient.Client;
using FS.Keycloak.RestApiClient.Model;
using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Security.Policy;

namespace ITF.SharedLibraries.Keycloak
{
    public class KeycloakService : IKeycloakService
    {
        private readonly ILogger<KeycloakService> _logger;
        private readonly KeycloakSettings _settings;
        private UsersApi _usersApi;
        public KeycloakService(ILogger<KeycloakService> logger, IConfiguration config, string varEnv = "Keycloak")
        {
            _settings = config.Get<KeycloakSettings>(varEnv);
            _logger = logger;



            var credentials = new ClientCredentialsFlow
            {
                KeycloakUrl = _settings.Url,
                Realm = _settings.Realm,
                ClientId = _settings.ClientId,
                ClientSecret = _settings.ClientSecret
            };

            
            //logger.LogInformation("KeycloakUrl {KeycloakUrl}, Realm {Realm}, ClientId {ClientId}, ClientSecret {ClientSecret}", _settings.Url, _settings.Realm, _settings.ClientId, _settings.ClientSecret);
            var httpClient = AuthenticationHttpClientFactory.Create(credentials);
            _usersApi = FS.Keycloak.RestApiClient.ClientFactory.ApiClientFactory.Create<UsersApi>(httpClient);

        }
        public async Task<List<UserRepresentation>> GetUsers()
        {
            return await _usersApi.GetUsersAsync(_settings.Realm);
        }
        public async Task<UserRepresentation> GetUserById(string userId, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(userId) } };
            var user = await SendAsync("GET", _settings.Realm, string.Format("admin/realms/{0}/users/{1}", _settings.Realm, userId), pathParameters, null, cancellationToken);
            return JsonConvert.DeserializeObject<UserRepresentation>(user.Data.ToString());
        }

        public async Task<bool> IsUserByUsernameExists(string username)
        {
            try
            {
                var user = await GetKeycloackUsersAsync(username);
                return true;
            }
            catch (Exception ex) {
                return false;
            }
        }
        public async Task<string> GetUserIdByUsername(string username)
        {
            var user = await GetKeycloackUsersAsync(username);       
            return user.Id;
        }
        public async Task<UserRepresentation> GetKeycloackUsersAsync(string username)
        {
            var users = await _usersApi.GetUsersAsync(_settings.Realm, username: username);
            if (users.Count() == 0)
                users = await _usersApi.GetUsersAsync(_settings.Realm, username: $"it{username}@interflora.it");// Handle Azure Users migrated in keycloak
            
            return users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) || u.Username.Equals($"it{username}@interflora.it", StringComparison.OrdinalIgnoreCase)) ?? throw new Exception($"No keycloack user with Username {username} was found");
        }
        public async Task<ApiResponse<object>> CreateUser(UserRepresentation user, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(user.Id) } };
            return await SendAsync("POST", _settings.Realm, string.Format("admin/realms/{0}/users", _settings.Realm), pathParameters, user, cancellationToken);
        }

        public async Task<ApiResponse<object>> DeleteUser(string userId, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(userId) } };
            return await SendAsync("DELETE", _settings.Realm, string.Format("admin/realms/{0}/users/{1}", _settings.Realm, userId), pathParameters, null, cancellationToken);
        }

        public async Task<ApiResponse<object>> UpdateUser(UserRepresentation user, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(user.Id) } };
            return await SendAsync("PUT", _settings.Realm, string.Format("admin/realms/{0}/users/{1}", _settings.Realm, user.Id), pathParameters, user, cancellationToken);
        }
        public async Task<ApiResponse<object>> ResetEmailUserPasswordByUserId(string userId, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(userId) } };
            var body = new string[] { "UPDATE_PASSWORD" };
            return await SendAsync("PUT", _settings.Realm, string.Format("admin/realms/{0}/users/{1}/execute-actions-email", _settings.Realm, userId), pathParameters, body, cancellationToken);
        }

        public async Task<ApiResponse<object>> ResetUserPasswordByUserId(string userId, string newPassword, bool forceChangePasswordNextSignIn = false, CancellationToken cancellationToken = default)
        {
            Dictionary<string, string> pathParameters = new Dictionary<string, string> { { "id", ClientUtils.ParameterToString(userId) } };
            var body = new CredentialRepresentation { Type = "password", Temporary = forceChangePasswordNextSignIn, Value = newPassword };
            return await SendAsync("PUT", _settings.Realm, string.Format("admin/realms/{0}/users/{1}/reset-password", _settings.Realm, userId), pathParameters, body, cancellationToken);
        }

        private async Task<ApiResponse<object>> SendAsync(string httpMethod, string realm, string path, Dictionary<string, string> pathParameters = null, object body = null, CancellationToken cancellationToken = default)
        {
            if (realm == null)
            {
                throw new ApiException(400, "Missing required parameter 'realm' when calling UsersApi->");
            }

            RequestOptions requestOptions = new RequestOptions();
            string[] contentTypes = new string[1] { "application/json" };
            string[] accepts = new string[0];
            string text = ClientUtils.SelectHeaderContentType(contentTypes);
            if (text != null)
            {
                requestOptions.HeaderParameters.Add("Content-Type", text);
            }

            string text2 = ClientUtils.SelectHeaderAccept(accepts);
            if (text2 != null)
            {
                requestOptions.HeaderParameters.Add("Accept", text2);
            }

            if (pathParameters != null)
                requestOptions.PathParameters = pathParameters;
            requestOptions.PathParameters.Add("realm", ClientUtils.ParameterToString(realm));

            if (body != null)
                requestOptions.Data = body;

            if (!string.IsNullOrEmpty(_usersApi.Configuration.AccessToken) && !requestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                requestOptions.HeaderParameters.Add("Authorization", "Bearer " + _usersApi.Configuration.AccessToken);
            }

            ApiResponse<object> apiResponse = null;

            switch (httpMethod)
            {
                case "POST":
                    apiResponse = await _usersApi.ApiClient.PostAsync<object>(path, requestOptions, _usersApi.Configuration, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
                    break;
                case "PUT":
                    apiResponse = await _usersApi.ApiClient.PutAsync<object>(path, requestOptions, _usersApi.Configuration, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
                    break;
                case "GET":
                    apiResponse = await _usersApi.ApiClient.GetAsync<object>(path, requestOptions, _usersApi.Configuration, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
                    break;
                case "DELETE":
                    apiResponse = await _usersApi.ApiClient.DeleteAsync<object>(path, requestOptions, _usersApi.Configuration, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
                    break;
            }

            if (_usersApi.ExceptionFactory != null)
            {
                Exception ex = _usersApi.ExceptionFactory(path, apiResponse);
                if (ex != null)
                {
                    throw ex;
                }
            }

            return apiResponse;
        }
    }
}

