﻿using ITF.SharedLibraries.CustomBackgroundService;
using ITF.SharedLibraries.HealthCheck;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public class AzureServiceBusSubscriber : CriticalBackgroundService, IDisposable
    {
        private readonly IServiceBusTopicSubscription _serviceBusTopicSubscription;

        public AzureServiceBusSubscriber(
            ILogger<AzureServiceBusSubscriber> logger,
            IServiceBusTopicSubscription serviceBusTopicSubscription,
            IHostApplicationLifetime applicationLifetime)
            : base(applicationLifetime, logger)
        {
            _serviceBusTopicSubscription = serviceBusTopicSubscription;
        }

        protected override async Task InfiniteProcessAsync(CancellationToken stoppingToken)
        {
            _logger.LogDebug("Starting the Azure service bus Topic consumer BackgroundService");
            await _serviceBusTopicSubscription.HandleMessages(stoppingToken).ConfigureAwait(false);
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogDebug("Stopping the Azure service bus Topic consumer BackgroundService");
            await _serviceBusTopicSubscription.CloseSubscriptionAsync(stoppingToken).ConfigureAwait(false);
            await base.StopAsync(stoppingToken);
        }

        public override async void Dispose()
        {
            await Dispose(true);
            GC.SuppressFinalize(this);
            base.Dispose();
        }

        protected virtual async Task Dispose(bool disposing)
        {
            if (disposing)
                await _serviceBusTopicSubscription.DisposeAsync();
        }
    }
}
