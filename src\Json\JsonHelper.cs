﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;

namespace ITF.SharedLibraries.Json
{
    public static class JsonHelper
    {
        // Default == null --> no Modification from Object to Serialize
        public static void SetSerializerNamingStrategy(NamingStrategy namingStrategy = null)
        {
            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver { NamingStrategy = namingStrategy == null ? new DefaultNamingStrategy() : namingStrategy }
            };
        }

        public static IMvcBuilder UseNewtonsoft(this IMvcBuilder services, Action<MvcNewtonsoftJsonOptions> opt = null)
        {
            if(opt == null)
                services.AddNewtonsoftJson(o => o.UseMemberCasing());
            else
                services.AddNewtonsoftJson(opt);

            return services;
        }

        public static IMvcBuilder UsePascalCase(this IMvcBuilder services)
        {
            services.AddJsonOptions(options =>
            {
                // Use the default property (Pascal) casing.
                options.JsonSerializerOptions.PropertyNamingPolicy = null;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
            });

            return services;
        }
    }
}
