﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderDeliveryAddressUpdatedMessage : BaseMessage<LegacyOrderDeliveryAddressUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderDeliveryAddressUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryAddressUpdatedMessage = new LegacyOrderDeliveryAddressUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryAddressUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Address = payload.Recipient.Street,
                            City = payload.Recipient.City,
                            ZipCode = payload.Recipient.ZipCode,
                            Latitude = payload.Recipient.Latitude,
                            Longitude = payload.Recipient.Longitude
                        }
                    };


                    return legacyOrderDeliveryAddressUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderDeliveryAddressUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderDeliveryAddressUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public bool Equals(LegacyOrderDeliveryAddressUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Address == parameter.Address &&
                City == parameter.City &&
                ZipCode == parameter.ZipCode &&
                Longitude == parameter.Longitude &&
                Latitude == parameter.Latitude
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderDeliveryAddressUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Address,
            City,
            ZipCode,
            Latitude
        }.GetHashCode();
    }

}
