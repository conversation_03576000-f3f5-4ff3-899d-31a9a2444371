﻿using ITF.SharedLibraries.RabbitMQ.Subscriber;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.RabbitMQ
{
    public class DelegateHandler
    {
        public delegate void RabbitMqDelegateHandler(IServiceCollection services);

        public static void RabbitMqHandlerSupplier<TService, TImplementation>(IServiceCollection services)
            where TService : class, IMessageHandler
            where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }
    }
}
