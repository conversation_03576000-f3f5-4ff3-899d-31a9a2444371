﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Readyness
{
    public class ReadynessMiddleware
    {
        readonly RequestDelegate _next;
        readonly string _message;

        public ReadynessMiddleware(RequestDelegate next, string message)
        {
            _next = next;
            _message = message;
        }

        public async Task Invoke(HttpContext context)
        {
            context.Response.StatusCode = 200;
            await context.Response.WriteAsync(_message);
        }
    }
}
