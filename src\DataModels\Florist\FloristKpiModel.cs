﻿using ITF.Lib.Common.DomainDrivenDesign;

namespace ITF.SharedModels.DataModels.Florist
{
    public class FloristKpiModel : BaseProjectedEvents, IEquatable<FloristKpiModel>
    {
        public string FloristId { get; set; }

        public DateTime Date { get; set; }
        public KpiElementType Type { get; set; }
        public decimal Amount { get; set; }
        public int Count { get; set; }

        public override void SetId() => Id = Guid.NewGuid().ToString();

        public int GetHashCode(FloristKpiModel obj)
        {
            return obj.FloristId.GetHashCode();
        }

        public bool Equals(FloristKpiModel? other)
        {
            throw new NotImplementedException();
        }

        public bool Equals(FloristKpiModel? x, FloristKpiModel? y)
        {
            throw new NotImplementedException();
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as FloristKpiModel);
        }
    }

    public enum KpiElementType
    {
        TRANSMISSION,
        EXECUTION
    }
}