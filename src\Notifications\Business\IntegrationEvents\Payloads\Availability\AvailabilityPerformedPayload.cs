﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Availability
{
    public class AvailabilityPerformedPayload : IPayload
    {
        public bool IsAnyFloristAvailable { get; set; }
        public bool IsGFS { get; set; }
        public bool IsOGF { get; set; }
        public bool IsInterflora { get; set; }
        public bool IsExternalNetwork { get; set; }
        public string GlobalError { get; set; }
        public string ExceptionMessage { get; set; }
        public int SearchRadius { get; set; }
        public decimal? Amount { get; set; }
        public string OrderId { get; set; }
        public DateTime OrderDate { get; set; }
        public bool IsGeocodedByGoogleMaps { get; set; }
        public string DeliveryStreet { get; set; }
        public string DeliveryCity { get; set; }
        public string DeliveryZipCode { get; set; }
        public string DeliveryCountry { get; set; }
        public DateTime DeliveryDate { get; set; }
        public string DeliveryWindow { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public DateTime ProcessedOn { get; set; }
        public List<string> OrderDetails { get; set; } = new();
        public List<FloristAvalability> Florists { get; set; } = new();

        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
    }

    public class FloristAvalability
    {
        public string FloristId { get; set; }
        public bool Availability { get; set; }
        public List<string> AvailabilityContext { get; set; } = new();
        public List<string> MissingReferences { get; set; } = new();
        public List<string> ReferencesOutOfStock { get; set; } = new();
    }
}
