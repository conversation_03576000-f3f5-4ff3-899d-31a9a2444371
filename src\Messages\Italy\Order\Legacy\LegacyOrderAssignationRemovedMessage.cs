﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ITF.SharedModels.Messages.Italy.Florist.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderAssignationRemovedMessage : BaseMessage<LegacyOrderAssignationRemovedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier + "-" + Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderAssignationRemovedMessage((string ctOrderId, OrderAssignmentMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderAssignedMessage = new LegacyOrderAssignationRemovedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderAssignationRemovedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            FloristIdentifier = payload.FloristId,
                            OrderIdentifier =src.ctOrderId,
                            LegacyOrderNumber = payload.OrderId
                        }
                    };


                    return legacyOrderAssignedMessage;
                }
            }
        }
    }

    public class LegacyOrderAssignationRemovedPayload : LegacyPayload, IEquatable<LegacyOrderAssignationRemovedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string OrderIdentifier { get; set; }
        public string? LegacyOrderNumber { get; set; } = string.Empty;
        public bool Equals(LegacyOrderAssignationRemovedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderAssignationRemovedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            OrderIdentifier
        }.GetHashCode();
    }
}
