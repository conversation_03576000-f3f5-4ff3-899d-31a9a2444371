﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using ITF.SharedLibraries.ElasticSearch.Repository;
using Nest;
using System;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Projections.Repositories
{
    public abstract class ElasticSearchProjectionRepository<T> : IProjection where T : BaseClass<string>
    {
        protected readonly IElasticSearchRepository<T, string> _esRepository;
        public ElasticSearchProjectionRepository(IElasticSearchRepository<T, string> esRepository)
        {
            _esRepository = esRepository;
        }

        public abstract Task Project(object @event, string eventId);

        protected virtual async Task Insert(T model)
        {
            await _esRepository.Add(model);
        }

        protected virtual async Task UpdateAllFiltered(Func<QueryContainerDescriptor<T>, QueryContainer> query, Action<T> update)
        {
            var results = await _esRepository.Get(query);
            foreach (var item in results)
            {
                update(item);
                await _esRepository.Update(item.Id, item);
            }
        }

        protected virtual async Task UpdateById(string id, Action<T> update)
        {
            var result = await _esRepository.GetById(id);
            update(result.Source);
            await _esRepository.Update(id, result.Source);
        }

        protected virtual async Task DeleteById(string id, Action<T> update)
        {
            await _esRepository.Remove(id);
        }
    }
}
