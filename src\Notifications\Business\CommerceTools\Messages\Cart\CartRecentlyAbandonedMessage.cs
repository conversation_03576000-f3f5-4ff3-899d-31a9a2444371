﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Cart;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Cart
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CartRecentlyAbandonedMessage : BaseMessage<CartRecentlyAbandonedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.Cart?.Id;
            }

            public class CartAbandonedFromLongPeriodMessage : BaseMessage<CartAbandonedFromLongPeriodPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.Cart?.Id;
            }
        }
    }
}
