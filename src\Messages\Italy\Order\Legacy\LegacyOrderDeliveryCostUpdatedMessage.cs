﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Shopopop.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderDeliveryCostUpdatedMessage : BaseMessage<LegacyOrderDeliveryCostUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderDeliveryCostUpdatedMessage((string ctOrderId, OrderDeliveryCourierUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryCostUpdatedMessage = new LegacyOrderDeliveryCostUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryCostUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            DeliveryCost = payload.Price.HasValue ? Convert.ToDecimal(payload.Price.Value) : 0,
                        }
                    };


                    return legacyOrderDeliveryCostUpdatedMessage;
                }

            }
        }
    }

    public class LegacyOrderDeliveryCostUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderDeliveryCostUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public decimal DeliveryCost { get; set; }

        public bool Equals(LegacyOrderDeliveryCostUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                DeliveryCost == parameter.DeliveryCost
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderDeliveryCostUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            DeliveryCost
        }.GetHashCode();
    }

}
