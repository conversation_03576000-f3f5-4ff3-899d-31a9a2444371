﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class GfsCategoryPayload : IPayload
    {
        public string CountryCode { get; set; }
        public List<Category> Categories { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
    public class Category
    {
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int? ParentId { get; set; }
        public DateTime LastUpdate { get; set; }
    }
}
