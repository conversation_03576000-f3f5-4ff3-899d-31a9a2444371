﻿using commercetools.Sdk.Api.Models.OrderEdits;
using commercetools.Sdk.Api.Models.Orders;
using CSharpFunctionalExtensions;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace IT.SharedLibraries.CT.Orders
{
    public interface IOrderService
    {
        Task Initialize();
        Task<IOrder> HandleOrderCreated(GlobalOrderModel globalOrderModel);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderAssigned"></param>
        /// <param name="internalOrderId"></param>
        /// <returns></returns>
        Task<IOrder?> HandleOrderAssigned(GlobalOrderAssigned orderAssigned, string internalOrderId, StatusEnum defaultStatus = StatusEnum.ASSIGNED, string executingfloristInvoiceUrl = null);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderCancelled"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderCancelled(GlobalOrderCancelled orderCancelled);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryTimeUpdated"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderDeliveryTimeUpdated(GlobalOrderDeliveryTimeUpdated orderDeliveryTimeUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryStatusUpdated"></param>
        /// <returns></returns>*
        Task<IOrder> HandleOrderDeliveryStatusUpdated(GlobalOrderDeliveryStatusUpdated orderDeliveryStatusUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryCostUpdated"></param>
        /// <returns></returns>*
        Task<IOrder> HandleOrderDeliveryCostUpdated(GlobalOrderDeliveryCostUpdated orderDeliveryCostUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryDateUpdated"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderDeliveryDateUpdated(GlobalOrderDeliveryDateUpdated orderDeliveryDateUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderCardMessageUpdated"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderCardMessageUpdated(GlobalOrderCardMessageUpdated orderCardMessageUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderNotesUpdated"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderNotesUpdated(GlobalOrderNotesUpdated orderNotesUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryAddressUpdated"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderDeliveryAddressUpdated(GlobalOrderDeliveryAddressUpdated orderDeliveryAddressUpdated);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderDeliveredOnBehalf(GlobalOrderDeliveredOnBehalf payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="globalOrderAccepted"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderAccepted(GlobalOrderAccepted globalOrderAccepted);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderRejected(GlobalOrderRejected payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderDelivered(GlobalOrderDelivered payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderItemUpdated(GlobalOrderItemUpdated payload);
        Task<IOrder> HandleOrderLineItemsUpdated((string ctOrderId, List<IOrderEditUpdateAction> actions) param);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderItemExecutorAmountUpdated(GlobalOrderItemExecutorAmountUpdated payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderAssignationRemoved(GlobalOrderAssignationRemoved payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderAcceptedOnBehalf(GlobalOrderAcceptedOnBehalf payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderRejectedOnBehalf(GlobalOrderRejectedOnBehalf payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderSent(GlobalOrderSent payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderRecipientNameUpdated(GlobalOrderRecipientNameUpdated payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderRecipientPhoneNumberUpdated(GlobalOrderRecipientPhoneNumberUpdated payload);
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        Task<IOrder> HandleOrderRecipientCoordinatesUpdated(GlobalOrderRecipientCoordinatesUpdated payload);
        Task<IOrder> HandleOrderExternalDeliveryFieldsUpdated(GlobalOrderExternalDeliveryFieldsUpdated payload);
        Task<IOrder> HandleOrderExternalDeliveryStatusUpdated(LegacyOrderDeliveryStatusUpdatedPayload payload);

        Task<Result> UpdateOrderFromRAOToCT(IOrder ctOrder, List<KeyValuePair<OrderDifference, object>> fieldsUpdated);
        Task<List<IOrder>> GetOrdersByExecutorAndDeliveryDate(string floristIdentifier, string deliveryDate_yyyy_MM_dd);
        Task<IOrder> GetById(string id);
        Task<IOrder> GetByOrderNumber(string orderNumber);
        Task<List<KeyValuePair<OrderDifference, object>>> GetDifferencesForUpdate<T>(T? payload, IOrder ctOrder) where T : BaseOrderPayload;
        Task<bool> IsOrderAlreadyExists(GlobalOrderModel globalOrderModel);

        #region RAO France 
        Task<Result> HandleRAOLegacyOrderUpdate<T>(T? payload, IOrder ctOrder, List<KeyValuePair<OrderDifference, object>> fieldsUpdated) where T : BaseOrderPayload;
        Task<bool> HandleRAOLegacyOrderManagementStatus(OrderManagementStatusMessage message);
        #endregion
        Task<T> BuildOrderMessageFromRaoOrderMessage<T, T1, T2, T3>(T2 RAOMessage, string ctOrderId = "")
           where T : BaseMessage<T1>, new()
           where T1 : IPayload
           where T2 : BaseMessage<T3>
           where T3 : BaseOrderPayload;

        Task<IOrder> PostOrderUpdateWithRetry(OrderUpdate orderUpdate, string orderId, string orderNumber);
        Task<IOrder> CallOrderUpdate(IOrder order, List<IOrderUpdateAction> actions);
        Task<IOrder> HandleOrderCTFieldsUpdated((string ctOrderId, List<IOrderUpdateAction> actions) param);
        OrderUpdate CreateOrderUpdate(IOrder order, List<KeyValuePair<OrderDifference, object>> fieldsUpdated);
    }
}
