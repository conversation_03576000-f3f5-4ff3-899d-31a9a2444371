﻿namespace ITF.SharedLibraries.Authentication.Keycloack
{
    public class Configuration
    {
        public bool RequireHttpsMetadata { get; set; }
        public bool IncludeErrorDetails { get; set; }
        public string Authority { get; set; }
        public bool ValidateAudience { get; set; }
        public string Audience { get; set; }
        public bool ValidateIssuerSigningKey { get; set; }
        public bool ValidateIssuer { get; set; }
        public string Issuer { get; set; }
        public bool ValidateLifetime { get; set; }
    }

    public class BasicConfiguration
    {
        public bool RequireHttpsMetadata { get; set; } // true
        public bool IncludeErrorDetails { get; set; }
        public bool ValidateAudience { get; set; } // true
        public bool ValidateIssuer { get; set; } // true

        public string Authority { get; set; }
        public string MetadataAddress { get; set; } // = authority url + /.well-known/openid-configuration
        public string[] Audiences { get; set; }
        public string Issuer { get; set; } // = authority for Keycloak
    }
}
