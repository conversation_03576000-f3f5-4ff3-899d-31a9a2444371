﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class RegisterCustomerEmailMessage : BaseMessage<RegisterCustomerEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.CustomerEmails?.FirstOrDefault()?.Email;
        }
    }
}

public class RegisterCustomerEmailPayload : IPayload
{
    public List<CustomerEmail> CustomerEmails { get; set; } = new List<CustomerEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
