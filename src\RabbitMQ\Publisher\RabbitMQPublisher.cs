﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.RabbitMQ.Publisher
{
    // cf. https://www.c-sharpcorner.com/article/publishing-rabbitmq-message-in-asp-net-core/
    public class RabbitMQPublisher : IRabbitMQPublisher
    {
        private readonly DefaultObjectPool<IChannel> _objectPool;
        private readonly ILogger _logger;

        public RabbitMQPublisher(ILoggerFactory loggerFactory, IPooledObjectPolicy<IChannel> objectPolicy)
        {
            _logger = loggerFactory.CreateLogger<RabbitMQPublisher>();
            _objectPool = new DefaultObjectPool<IChannel>(objectPolicy, Environment.ProcessorCount * 2);
        }

        public async Task Publish<T>(T message, string exchangeName, string exchangeType, string routeKey) where T : class
        {
            if (message == null)
                return;

            var channel = _objectPool.Get();

            try
            {
                await channel.ExchangeDeclareAsync(exchangeName, exchangeType, true, false, null);

                var sendBytes = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(message));

                var properties = new BasicProperties
                {
                    Persistent = true
                };

                properties.Persistent = true;

                await channel.BasicPublishAsync(
                    exchange: exchangeName,
                    routingKey: routeKey,
                    mandatory: false,
                    basicProperties: properties,
                    body: new ReadOnlyMemory<byte>(sendBytes));  // Need to wrap bytes in ReadOnlyMemory
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error while publishing the message {message} to {exchangeName} and routekey {routeKey} with RabbitMq", message, exchangeName, routeKey);
                throw;
            }
            finally
            {
                _objectPool.Return(channel);
            }
        }
    }
}
