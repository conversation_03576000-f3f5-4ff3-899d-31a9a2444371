﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacySpecialDayAddedMessage : BaseMessage<LegacySpecialDayAddedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.Start.ToString("yyyyMMdd") + Payload?.End.ToString("yyyyMMdd") + Payload?.Type.ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacySpecialDayAddedPayload : LegacyPayload, IEquatable<LegacySpecialDayAddedPayload>
    {
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public SpecialDayTypeEnum Type { get; set; }
        public List<string> Florists { get; set; } = new();

        public bool Equals(LegacySpecialDayAddedPayload parameter)
        {
            return (Start == parameter.Start &&
                End == parameter.End &&
                Type == parameter.Type &&
                Florists.Equals(parameter.Florists)
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacySpecialDayAddedPayload);
        }

        public override int GetHashCode() => new
        {
            Start,
            End,
            Type,
            Florists
        }.GetHashCode();
    }
}
