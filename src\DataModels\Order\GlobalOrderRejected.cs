﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderRejected
    {
        public string FloristIdentier { get; set; }
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderRejected(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRejectedMessage v)
        {
            return new GlobalOrderRejected
            {
                FloristIdentier = v?.Payload?.FloristIdentifier,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
