﻿namespace ITF.SharedLibraries.Availability.dto
{
    public class AvailabilityOutputDTO_DeliveryFee
    {
        public string Name { get; set; }
        public decimal Price { get; set; }
        public decimal Tax { get; set; }

    }

    public class AvailabilityOutputDTO_DeliveryTexts
    {
        public string Name { get; set; }
        public string ShortInfo { get; set; }
        public string LongtInfo { get; set; }

    }

    public class AvailabilityOutputDTO_DeliveryInfo
    {
        public decimal Price { get; set; }
        public decimal Compensation { get; set; }
        public bool SelectedByDistributionQuota { get; set; }
        public AvailabilityOutputDTO_DeliveryTexts Texts { get; set; } = new();
        public List<AvailabilityOutputDTO_DeliveryFee> Fees { get; set; } = new();

    }

    public class FloristDistanceDto
    {
        public string FloristId { get; set; }
        public string ShopName { get; set; }
        public double Distance { get; set; }
        public string PhoneNumber { get; set; }
        public double QualityFactor { get; set; }
    }

    public class AvailabilityOutputDTO
    {
        public bool Result { get; set; }
        public DateTime? SuggestedDeliveryDate { get; set; }
        public AvailabilityOutputDTO_DeliveryInfo? DeliveryInfo { get; set; }
        public List<FloristDistanceDto> ExecutingFlorists { get; set; } = [];
    }
}
