﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.France.Order;
public static partial class Messages
{
    public static partial class V1
    {
        public class OrderCustomerFeedbackMessage : BaseMessage<OrderCustomerFeedbackMessagePayload>, IMessageKey, IDistributedTracing
        {
            public string GetMessageKey()
                => Payload?.Version == 0 ? Payload.OrderId : Payload?.OrderId + "-" + Payload?.Version;

            public void SetDistributedTracingData(string distributedTracingData)
            {
                DistributedTracingData = distributedTracingData;
            }
        }
    }
}

public sealed class OrderCustomerFeedbackMessagePayload : LegacyPayload
{
    public int Version { get; set; }
    public string BusinessUnit { get; set; } = string.Empty;
    public string NpsOderId { get; set; } = string.Empty;
    public DateTime DeliveryDate { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime ResponseDate { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public string FloristId { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public string Score { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
}
