﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Shopopop.Payloads
{
    public class DeliveryPriceUpdatedPayload : IPayload
    {
        public string OrderId { get; set; }
        public double Price { get; set; }

        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
    }
}
