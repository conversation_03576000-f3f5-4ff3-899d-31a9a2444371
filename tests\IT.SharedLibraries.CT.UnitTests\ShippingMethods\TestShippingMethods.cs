using Microsoft.AspNetCore.TestHost;
using Microsoft.AspNetCore.Hosting;
using Xunit;
using Microsoft.Extensions.Configuration;
using IT.SharedLibraries.CT.ShippingMethods;
using IT.SharedLibraries.CT.Zones;
using System.Threading.Tasks;
using commercetools.Sdk.Api.Models.Zones;
using Microsoft.AspNetCore.Builder;
using commercetools.Sdk.Api;
using FluentAssertions.Common;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using commercetools.Base.Client;
using Microsoft.Extensions.Hosting;
using System.Linq;

namespace IT.SharedLibraries.CT.UnitTests.ShippingMethods
{
    public static class Settings
    {
        public static string ProjectKey { get; private set; }

        public static void SetCurrentProjectKey(string projectKey)
        {
            ProjectKey = projectKey;
        }
    }

    public class TestShippingMethods : TestShippingMethodsSetUp
    {

        [Fact]
        public async Task Test_Update_ShippingRate__Other_Countries__Pfs_international_italy()
        {
            var app = GetWebApplication();

            var zoneService = app.Services.GetService(typeof(IZoneService)) as IZoneService;
            var shippingMethodService = app.Services.GetService(typeof(IShippingMethodService)) as IShippingMethodService;
            var zone = await zoneService.GetByKey("other-countries");

            var updatedShippingMethod = await shippingMethodService.UpdateSetFixedShippingRate("pfs-international-italy", (Zone)zone, 12.59m, "EUR");
            Assert.NotNull(updatedShippingMethod);
            Assert.Single(updatedShippingMethod.ZoneRates);
            Assert.Equal(1259, updatedShippingMethod.ZoneRates.FirstOrDefault().ShippingRates.FirstOrDefault().Price.CentAmount);

        }
    }
}
