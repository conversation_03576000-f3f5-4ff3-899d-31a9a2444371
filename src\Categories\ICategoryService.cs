﻿using commercetools.Sdk.Api.Models.Categories;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Products
{
    public interface ICategoryService
    {
        Task<ICategory> GetByKey(string key);
        Task<IList<ICategory>> GetAll();
        Task<ICategory?> Create(ICategoryDraft categoryDraft);
        Task<ICategory> Update(ICategory existingCategory, List<ICategoryUpdateAction> updateActions);
    }
}
