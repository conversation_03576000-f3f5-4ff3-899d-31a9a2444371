﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Group.Enums
{
    public enum MassiveCalendarMorningAfternoonEnum
    {
        OPEN,
        CLOSED
    }

    public static class MassiveCalendarMorningAfternoonHelper
    {
        public static string GetStringValue(MassiveCalendarMorningAfternoonEnum value)
        {
            var stringValue = Enum.GetName(typeof(MassiveCalendarMorningAfternoonEnum), value);
            return stringValue;
        }
    }
}
