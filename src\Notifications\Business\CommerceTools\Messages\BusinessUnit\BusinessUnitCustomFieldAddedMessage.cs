﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.BusinessUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.BusinessUnit;
public static partial class Messages
{
    public static partial class V1
    {
        public class BusinessUnitCustomFieldAddedMessage : BaseMessage<BusinessUnitCustomFieldAddedPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.BusinessUnitCustomFieldAddedMessage?.Resource?.Id;
        }
    }
}
