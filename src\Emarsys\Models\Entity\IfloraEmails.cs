﻿using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;
public class IfloraCustomerEmail
{
    [Required]
    [SwaggerSchema("Customer's Email", Nullable = false)]
    public string Email { get; set; }
    [SwaggerSchema("Customer's Civility")]
    public string Salutation { get; set; }
    public string LastName { get; set; }
    [SwaggerSchema(Nullable = false)]
    [Required]
    public string OrderId { get; set; }
    public string RecipientPhone { get; set; }
    public string UrlIflora { get; set; }
}

public class IfloraRecipientEmail
{
    [SwaggerSchema("Recipient's Email")]
    public string Email { get; set; }
    [SwaggerSchema("Recipient's Civility")]
    public string Salutation { get; set; }
    public string LastName { get; set; }
    public string FirstName { get; set; }
    [SwaggerSchema(Nullable = false)]
    [Required]
    public string OrderId { get; set; }
    public string DeliveryDate { get; set; }
    [SwaggerSchema("Moment of Delivery should be translated/handled in Hybris side 'M' --> '8h 13h' etc ")]
    public string DeliveryHours { get; set; }
    [SwaggerSchema("Type of Delivery Place (e.g. Home, Work,...)")]
    public string DeliveryPlace { get; set; }

    [SwaggerSchema("Recipient's Address")]
    public string Street { get; set; }

    [SwaggerSchema("Recipient's Address")]
    public string AdditionalAddress { get; set; }

    [SwaggerSchema("Recipient's Address")]
    public string ZipCode { get; set; }

    [SwaggerSchema("Recipient's Address")]
    public string City { get; set; }

    [SwaggerSchema("Recipient's Address")]
    public string Region { get; set; }

    [SwaggerSchema("Recipient's Address in 2-letters format (e.g. FR, ES, PT, IT, SE, DK)")]
    public string Country { get; set; }
}
