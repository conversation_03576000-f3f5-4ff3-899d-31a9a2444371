﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Channels;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Channels
{
    public class ChannelService : IChannelService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ChannelService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public ChannelService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ChannelService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }
        public async Task<IChannel> GetByKey(string channelKey)
        {
            IChannel channel = null;
            try
            {
                var resultSet = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Channels()
                    .Get()
                    .WithWhere($"key = \"{channelKey}\"")
                    .ExecuteAsync();

                if (resultSet != null)
                {
                    channel = resultSet.Results[0];
                }
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading channel with key={channelKey}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading channel with key={channelKey} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return channel;
        }
    }
}
