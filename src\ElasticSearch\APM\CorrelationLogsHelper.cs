﻿using Elastic.Apm;
using ITF.SharedLibraries.ExtensionMethods;
using System;

namespace ITF.SharedLibraries.ElasticSearch.APM
{
    public static class CorrelationLogsHelper
    {
        public static string GetCorrelationLogsId() => 
                (Agent.Tracer.CurrentSpan?.OutgoingDistributedTracingData
                    ?? Agent.Tracer.CurrentTransaction?.OutgoingDistributedTracingData)?.SerializeToString();

        public static string GetCausationTraceId() =>
        (Agent.Tracer.CurrentSpan?.TraceId
            ?? Agent.Tracer.CurrentTransaction?.TraceId);

        public static string GetCausationTransactionId() =>
            (Agent.Tracer.CurrentSpan?.TransactionId
                ?? Agent.Tracer.CurrentTransaction?.Id);

        public static void TagTransaction(string key, string data)
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            if (string.IsNullOrWhiteSpace(data))
                return;

            var transaction = Agent.Tracer.CurrentTransaction;
            transaction?.SetLabel(key, data);
        }

        public static void TagTransaction(string key, int? data)
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            if (!data.HasValue)
                return;

            var transaction = Agent.Tracer.CurrentTransaction;
            transaction?.SetLabel(key, data.Value);
        }

        public static void TagTransaction(string key, long? data)
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            if (!data.HasValue)
                return;

            var transaction = Agent.Tracer.CurrentTransaction;
            transaction?.SetLabel(key, data.Value);
        }

        public static string ExtractFloristIdAndTagTransaction(dynamic payload)
        {
            if (payload is null)
                return null;

            string extractedKey = null;
            if (Operations.IsPropertyExist(payload, "Payload") &&
                Operations.IsPropertyExist(payload.Payload, "FloristId"))
            {
                extractedKey = payload.Payload.FloristId;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("FloristId", extractedKey);
            }
            else if (Operations.IsPropertyExist(payload, "FloristId"))
            {
                extractedKey = payload.FloristId;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("FloristId", extractedKey);
            }
            return extractedKey;
        }

        public static string ExtractOrderIdAndTagTransaction(dynamic payload)
        {
            if (payload is null)
                return null;

            string extractedKey = null;
            if (Operations.IsPropertyExist(payload, "Payload") &&
                Operations.IsPropertyExist(payload.Payload, "OrderId"))
            {
                extractedKey = payload.Payload.OrderId;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("OrderId", extractedKey);
            }
            else if (Operations.IsPropertyExist(payload, "OrderId"))
            {
                extractedKey = payload.OrderId;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("OrderId", extractedKey);
            }

            return extractedKey;
        }

        public static int ExtractOrderVersionAndTagTransaction(dynamic payload)
        {
            if(payload is null)
                return 0;

            int extractedKey = 0;
            if (Operations.IsPropertyExist(payload, "Payload") &&
                Operations.IsPropertyExist(payload.Payload, "Version"))
            {
                extractedKey = payload.Payload.Version;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("Version", extractedKey);
            }
            else if (Operations.IsPropertyExist(payload, "Version"))
            {
                extractedKey = payload.Version;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("Version", extractedKey);
            }

            return extractedKey;
        }

        public static string ExtractProductReferenceAndTagTransaction(dynamic payload)
        {
            if (payload is null)
                return null;

            string extractedKey = null;
            if (Operations.IsPropertyExist(payload, "Payload") &&
                Operations.IsPropertyExist(payload.Payload, "ProductReference"))
            {
                extractedKey = payload.Payload.ProductReference;
                var transaction = Agent.Tracer.CurrentTransaction;
                transaction?.SetLabel("ProductReference", extractedKey);
            }
            return extractedKey;
        }

        public static void TagTransaction(Elastic.Apm.Api.ITransaction transaction, string key, string data)
        {
            if (transaction is null)
                return;

            if (string.IsNullOrWhiteSpace(key))
                return;

            if (string.IsNullOrWhiteSpace(data))
                return;

            transaction?.SetLabel(key, data);
        }

        public static void TagTransaction(Elastic.Apm.Api.ITransaction transaction, string key, int? data)
        {
            if (transaction is null)
                return;

            if (string.IsNullOrWhiteSpace(key))
                return;

            if (!data.HasValue)
                return;

            transaction?.SetLabel(key, data.Value);
        }

        public static void TagTransaction(Elastic.Apm.Api.ITransaction transaction, string key, bool data)
        {
            if (transaction is null)
                return;

            if (string.IsNullOrWhiteSpace(key))
                return;

            transaction?.SetLabel(key, data);
        }

        public static void TagTransaction(Elastic.Apm.Api.ITransaction transaction, string key, long? data)
        {
            if (transaction is null)
                return;

            if (string.IsNullOrWhiteSpace(key))
                return;

            if (!data.HasValue)
                return;

            transaction?.SetLabel(key, data.Value);
        }

        public static void CaptureErrorTransaction(Elastic.Apm.Api.ITransaction transaction, string message, string culprit)
        {
            transaction?.CaptureError(message, culprit, null);
        }

        public static void CaptureErrorTransaction(string message, string culprit)
        {
            var transaction = Agent.Tracer.CurrentTransaction;
            transaction?.CaptureError(message, culprit, null);
        }

        public static void CaptureExceptionTransaction(Elastic.Apm.Api.ITransaction transaction, Exception e, string culprit, bool isHandled = true)
        {
            transaction?.CaptureException(e, culprit, isHandled);
        }

        public static void CaptureExceptionTransaction(Exception e, string culprit, bool isHandled = true)
        {
            var transaction = Agent.Tracer.CurrentTransaction;
            transaction?.CaptureException(e, culprit, isHandled);
        }
    }
}
