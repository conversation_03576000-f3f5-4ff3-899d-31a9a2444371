﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.CartDiscounts;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Models.Payments;
using commercetools.Sdk.Api.Models.ShippingMethods;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ObjectsComparer;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Carts
{
    public class CartService : ICartService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CartService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public CartService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CartService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }
        public async Task<ICart> Create(ICartDraft newInstance)
        {
            ICart cart;
            try
            {
                cart = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Carts()
                    .Post(newInstance)
                    .ExecuteAsync();
            }
            catch (BadRequestException bre)
            {
                _logger.LogError(bre, $"Error while creating a cart, body: {bre.Body} because of {bre.Message} - {bre.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while creating a cart because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return cart;
        }

        public async Task<ICartDiscount> CreateDiscountCart(ICartDiscountDraft newInstance)
        {
            ICartDiscount cart;
            try
            {
                cart = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .CartDiscounts()
                    .Post(newInstance)
                    .ExecuteAsync();
            }
            catch (BadRequestException bre)
            {
                _logger.LogError(bre, $"Error while creating a discountCart, body: {bre.Body} because of {bre.Message} - {bre.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while creating a discountCart because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return cart;
        }

        public async Task<ICart> GetById(string id)
        {
            ICart cart = null;
            try
            {
                cart = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Carts()
                    .WithId(id)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving cart with id {id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving cart with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving cart with id {id} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return cart;
        }

        public async Task<ICart> UpdateInatCartAddShippingInfo(ICart oldTInstance, ICart newInstance, decimal? inatOrderShippingTaxRate, decimal? inatOrderShippingTaxAmount, string deliveryCountryCode, string currencyCode)
        {
            CartUpdate update = null;
            var actions = new List<ICartUpdateAction>();

            var comparerIShippingInfo = new ObjectsComparer.Comparer<IShippingInfo>();
            IEnumerable<Difference> differences;
            if (!comparerIShippingInfo.Compare(oldTInstance.ShippingInfo, newInstance.ShippingInfo, out differences))
            {
                var comparerIShippingMethodState = new ObjectsComparer.Comparer<IShippingMethodState>(new ComparisonSettings { UseDefaultIfMemberNotExist = true });
                if ((oldTInstance.ShippingInfo == null) || !comparerIShippingMethodState.Compare(oldTInstance.ShippingInfo.ShippingMethodState, newInstance.ShippingInfo.ShippingMethodState))
                {
                    if (newInstance?.ShippingInfo?.ShippingMethodName != null && newInstance?.ShippingInfo?.ShippingRate != null && inatOrderShippingTaxRate.HasValue)
                    {
                        actions.Add(new CartSetCustomShippingMethodAction
                        {
                            ShippingMethodName = newInstance?.ShippingInfo?.ShippingMethodName,
                            ShippingRate = new ShippingRateDraft { Price = new TypedMoney { CentAmount = (long)(inatOrderShippingTaxAmount * 100), FractionDigits = 2, CurrencyCode = currencyCode } },
                            ExternalTaxRate = new ExternalTaxRateDraft { Name = "External tax rate PFS", Amount = (long)inatOrderShippingTaxRate, Country = deliveryCountryCode }
                        });
                    }
                }
            }
            ICart updatedCart = null;
            if (actions.Count > 0)
            {

                update = new CartUpdate
                {
                    Version = oldTInstance.Version,
                    Actions = actions
                };
                try
                {
                    updatedCart = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Carts()
                    .WithId(oldTInstance.Id)
                    .Post(update)
                    .ExecuteAsync();
                }
                catch (BadRequestException ex)
                {
                    _logger.LogError(ex, $"Error while updating inat cart with id {oldTInstance.Id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                    throw;
                }
                catch (commercetools.Base.Client.Error.NotFoundException nfex)
                {
                    _logger.LogError(nfex, $"Error 404 while updating inat cart with id {oldTInstance.Id} because of {nfex.Message} - {nfex.StackTrace}");
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while updating inat cart with id {oldTInstance.Id} because of {ex.Message} - {ex.StackTrace}");
                    throw;
                }
            }
            return updatedCart;
        }

        public async Task<ICart> Update(ICart oldTInstance, ICart newInstance)
        {
            CartUpdate update = null;
            var actions = new List<ICartUpdateAction>();

            var comparerIShippingInfo = new ObjectsComparer.Comparer<IShippingInfo>();
            IEnumerable<Difference> differences;
            if (!comparerIShippingInfo.Compare(oldTInstance.ShippingInfo, newInstance.ShippingInfo, out differences))
            {
                //if(differences)

                var comparerIShippingMethodState = new ObjectsComparer.Comparer<IShippingMethodState>(new ComparisonSettings { UseDefaultIfMemberNotExist = true });
                if ((oldTInstance.ShippingInfo == null) || !comparerIShippingMethodState.Compare(oldTInstance.ShippingInfo.ShippingMethodState, newInstance.ShippingInfo.ShippingMethodState))
                {
                    if (newInstance?.ShippingInfo?.ShippingMethod?.Id != null)
                    {
                        actions.Add(new CartSetShippingMethodAction { ShippingMethod = new ShippingMethodResourceIdentifier { Id = newInstance.ShippingInfo.ShippingMethod.Id } });
                    }
                }
            }

            var comparerIPaymentInfo = new ObjectsComparer.Comparer<IPaymentInfo>();
            if (!comparerIPaymentInfo.Compare(oldTInstance.PaymentInfo, newInstance.PaymentInfo, out differences))
            {
                //if(differences)

                var comparerIPaymentReference = new ObjectsComparer.Comparer<IList<IPaymentReference>>(new ComparisonSettings { UseDefaultIfMemberNotExist = true });
                if ((oldTInstance.PaymentInfo == null) || !comparerIPaymentReference.Compare(oldTInstance.PaymentInfo.Payments, newInstance.PaymentInfo.Payments))
                {
                    foreach (var payment in newInstance.PaymentInfo.Payments)
                    {
                        actions.Add(new CartAddPaymentAction { Payment = new PaymentResourceIdentifier { Id = payment.Id } });
                    }
                }
            }


            ICart updatedCart = null;
            if (actions.Count > 0)
            {

                update = new CartUpdate
                {
                    Version = oldTInstance.Version,
                    Actions = actions
                };
                try
                {
                    updatedCart = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Carts()
                    .WithId(oldTInstance.Id)
                    .Post(update)
                    .ExecuteAsync();
                }
                catch (BadRequestException ex)
                {
                    _logger.LogError(ex, $"Error while updating cart with id {oldTInstance.Id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                    throw;
                }
                catch (commercetools.Base.Client.Error.NotFoundException nfex)
                {
                    _logger.LogError(nfex, $"Error 404 while updating cart with id {oldTInstance.Id} because of {nfex.Message} - {nfex.StackTrace}");
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while updating cart with id {oldTInstance.Id} because of {ex.Message} - {ex.StackTrace}");
                    throw;
                }
            }
            return updatedCart;
        }
    }
}
