﻿namespace ITF.SharedModels.Notifications.Business.Legacy
{
    public enum OrderAssignmentType
    {
        ASSIGNED,
        UNASSIGNED,
        UPDATED,
        REJECTED,
        CANCELED,
        UNASSIGNABLE
    }

    public enum OrderNotificationType
    {
        ORDER,
        MODIFICATION,
        T<PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        R<PERSON><PERSON><PERSON><PERSON>,
        REFUSED
    }

    public enum MessageStatus
    {
        NEW,
        SENT,
        READ
    }
}
