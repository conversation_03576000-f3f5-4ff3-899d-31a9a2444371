﻿using System.Collections.Generic;
using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;

public class OrderEmail
{
    [SwaggerSchema(Nullable = false)]
    public string OrderId { get; set; }
    [SwaggerSchema(Format = "yyyy-MM-dd")]
    public string DeliveryDate { get; set; }
    public string DeliveryHours { get; set; }
    public string Store { get; set; }

    [SwaggerSchema("Type of the order")]
    public string Type { get; set; }
    public string PaymentMethod { get; set; }

    [SwaggerSchema("Is a mourning order")]
    public bool IsMourning { get; set; } = false;

    [SwaggerSchema("Order is for a wedding")]
    public bool IsWedding { get; set; } = false;

    [SwaggerSchema("Is a iFlora order")]
    public bool IsIflora { get; set; } = false;

    [SwaggerSchema("Is a Floral Subscription order")]
    public bool IsFloralSubscription { get; set; } = false;
    public string FloristId { get; set; }
    public string AbsenceReason { get; set; }
    public bool OptInSms { get; set; } = false;
    public bool OptInNewsletter { get; set; } = false;
    public List<SubscriptionEmail> Subscriptions { get; set; } = new();
    public ContactDeliveryEmail ContactDelivery { get; set; } = new();
    public MessageCardEmail MessageCard { get; set; } = new();
    public CustomerEmail Customer { get; set; } = new();
    public DeliveryAddressEmail DeliveryAddress { get; set; } = new();
    public BillingAddressEmail BillingAddress { get; set; } = new();
    public List<ProductEmail> ProductItems { get; set; } = new();
    public ShippingEmail Shipping { get; set; } = new();
    public List<VoucherEmail> Vouchers { get; set; } = new();
    public List<VoucherEmail> OfferedVouchers { get; set; } = new();
    public Total Total { get; set; } = new();
}
public class Total
{
    public decimal TotalProductItems { get; set; }
    public decimal TotalDiscounts { get; set; }
    public decimal TotalShippingCost { get; set; }
    public decimal TotalPaid { get; set; }
}