﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderCancelledMessage : BaseMessage<LegacyOrderCancelledPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderCancelledMessage((string ctOrderId, OrderAssignmentMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderCanceledMessage = new LegacyOrderCancelledMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderCancelledPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Reason = string.Empty
                        }
                    };

                    return legacyOrderCanceledMessage;
                }
            }
        }
    }

    public class LegacyOrderCancelledPayload : LegacyPayload, IEquatable<LegacyOrderCancelledPayload>
    {
        public string OrderIdentifier { get; set; }
        public String Reason { get; set; }
        public bool Equals(LegacyOrderCancelledPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Reason == parameter.Reason
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderCancelledPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Reason
        }.GetHashCode();
    }
}
