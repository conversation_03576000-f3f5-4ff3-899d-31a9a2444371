﻿using IT.Microservices.OrderReactor.Domain;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.Kafka.Publisher;
using ITF.SharedModels.Messages.Italy;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using static ITF.SharedModels.Messages.Group.Order.Messages.V1;

namespace IT.Microservices.OrderReactor.Application
{
    public class KafkaPublisherHelper : IKafkaPublisherHelper
    {
        private readonly ILogger _logger;
        private readonly IKafkaPublisher _kafkaPublisher;

        public KafkaPublisherHelper(
            ILogger<KafkaPublisherHelper> logger,
            IKafkaPublisher kafkaPublisher)
        {
            _logger = logger;
            _kafkaPublisher = kafkaPublisher;
        }

        public async Task<PublishKafkaResult> Publish(OrderNewHistoryRecordMessage data, string kafkaTopic)
        {
            return await Publish<OrderNewHistoryRecordMessage>(data, kafkaTopic);
        }

        public async Task<PublishKafkaResult> Publish(OrderNumberGeneratedMessage data, string kafkaTopic)
        {
            return await Publish<OrderNumberGeneratedMessage>(data, kafkaTopic);
        }

        public async Task<PublishKafkaResult> Publish<T>(object message, string kafkaTopic, string partitionKey = null) where T : class, IClrType, IDistributedTracing
        {
            try
            {
                // Cast to T
                var entity = message?.Serialize()?.Deserialize<T>();

                if (entity == default)
                    return new PublishKafkaResult(false, "Entity was not deserialized");

                await _kafkaPublisher.PublishAsync(entity, kafkaTopic, partitionKey);
                return (PublishKafkaResult)true;

            }
            catch (Exception e)
            {
                _logger.LogError(e, "Fail to publish back from Elk to Kafka the {message}", message);
                return new PublishKafkaResult(false, e.Message);
            }
        }

    }
}
