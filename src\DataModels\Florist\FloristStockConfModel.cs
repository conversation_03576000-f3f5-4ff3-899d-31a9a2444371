﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedModels.DataModels.Order;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.DataModels.Florist;

[BsonIgnoreExtraElements]
public class FloristStockConfModel : BaseProjectedEvents
{
    public FloristStockConfModel(string id, string name)
    {
        Id = id;
        Name = name;
    }

    public string Name { get; set; }

    public List<string> Variants { get; set; } = new();

    public override void SetId() => throw new NotImplementedException();
    
}
