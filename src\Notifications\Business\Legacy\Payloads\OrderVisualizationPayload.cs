﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class OrderVisualizationPayload : IPayload
    {
        public string FloristId { get; set; }
        public string OrderId { get; set; }
        public int Version { get; set; }
        public string VisualizationState { get; set; } // READ
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }

        public string GetId() => $"{OrderId}_{FloristId}";
    }
}
