﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderNotesUpdatedMessage : BaseMessage<LegacyOrderNotesUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderNotesUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderNotesUpdatedMessage = new LegacyOrderNotesUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderNotesUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Notes = payload.Delivery.Instructions

                        }
                    };


                    return legacyOrderNotesUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderNotesUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderNotesUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public string Notes { get; set; }

        public bool Equals(LegacyOrderNotesUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Notes == parameter.Notes
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderNotesUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Notes
        }.GetHashCode();
    }

}
