﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderCancelled
    {
        public string OrderIdentifier { get; set; }
        public String Reason { get; set; }

        public static implicit operator GlobalOrderCancelled(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCancelledMessage v)
        {
            return new GlobalOrderCancelled
            {
                OrderIdentifier = v?.Payload?.OrderIdentifier,
                Reason = v?.Payload?.Reason,
            };
        }
    }
}
