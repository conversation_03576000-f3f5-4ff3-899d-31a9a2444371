﻿using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Products;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using System.Diagnostics.CodeAnalysis;
using System.Drawing;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public static class DeliveryInformationsExtensions
    {

        public static MomentEnum WindowRaoToMomentCT(this DeliveryInformations deliveryInfo)
        {
            if (deliveryInfo.Window.Equals("J") && (deliveryInfo.Instructions?.Equals("Soir (17h - 20h)") ?? false))
                return MomentEnum.Evening;

            return deliveryInfo?.Window switch
            {
                "M" => MomentEnum.Morning,
                "A" => MomentEnum.Afternoon,
                "J" => MomentEnum.Wholeday,
                _ => MomentEnum.Wholeday,
            };
        }

        public static string GreetingsRaoToRecipientTitleCT(this string greetings)
        {
            return greetings?.ToUpper() switch
            {
                null => "MRS",
                "" => "MRS",
                "M" => "MR",
                "M." => "MR",
                "MR" => "MR",
                "MLE" => "MRS",
                "MLLE" => "MRS",
                "MME" => "MRS",
                "MRMME" => "MRS",
                "MRS" => "MRS",
                _ => "MRS"
            };
        }
    }

    public class Common
    {

        public class StatusInformations
        {
            public string Status { get; set; }
            public string? ManagerStatus { get; set; }
            public string? NotificationProcessingStatus { get; set; } // Order.ProcessingStatus
            public string? ReadStatus { get; set; } // Order.FloristProcessingStatus

            public string GetCtStatus() => Status switch
            {
                "ATT" => StatusHelper.GetStringValue(StatusEnum.NEW_ORDER),
                "ATF" => StatusHelper.GetStringValue(StatusEnum.IN_PROGRESS),
                "DA" => StatusHelper.GetStringValue(StatusEnum.ABSENT),
                "AFF" => StatusHelper.GetStringValue(StatusEnum.ACCEPTED),
                "ATM" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
                "ATC" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
                "NPA" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
                "LFA" => StatusHelper.GetStringValue(StatusEnum.DELIVERED),
                "LFM" => StatusHelper.GetStringValue(StatusEnum.DELIVERED),
                "ANN" => StatusHelper.GetStringValue(StatusEnum.CANCELLED),
                "ANR" => StatusHelper.GetStringValue(StatusEnum.CANCELLED),
                _ => StatusHelper.GetStringValue(StatusEnum.NEW_ORDER),
            };
        }

        public class EmissionInformations
        {
            public string AssignmentState { get; set; } // ASSIGNED / UNASSIGNED / UPDATED / REJECTED / CANCELED (order canceled or ANR for previous florist)
            public DateTime? EventDate { get; set; }
            public string? Comment { get; set; }
        }

        public class RecipientInformations
        {
            public string? Greetings { get; set; }
            public string? LastName { get; set; }
            public string? FirstName { get; set; }
            public string? Street { get; set; }
            public string? ZipCode { get; set; }
            public string? City { get; set; }
            public string? CountryCode { get; set; }
            public string? MainPhone { get; set; }
            public string? SecondPhone { get; set; }
            public double Latitude { get; set; }
            public double Longitude { get; set; }
        }

        public class DeliveryInformations
        {
            public string? Instructions { get; set; }
            public DateTime Date { get; set; }
            public DateTime? PreviousDate { get; set; }
            public string Window { get; set; } = "J";
            public string? Time { get; set; }
            public string? LocationType { get; set; } // Order.DeliveryLocationType (c, d etc...)
            public string? Place { get; set; } // Order.DeliveryLocation (lieu dit)
            public string? AdditionalAddress { get; set; } // Order.AdditionalAddress (complément d'adresse)
            public double? TripCost { get; set; }
            public double? AdditionnalTripCost { get; set; }
            public string? ContactLastName { get; set; }
            public string? ContactFirstName { get; set; }
            public int? TripCostComputationType { get; set; } // Order.DeliveryCostCalculationType
        }

        public class ProductInformations : IEquatable<ProductInformations>      
        {
            public string ProductId { get; set; }
            public string Size { get; set; }
            public string Style { get; set; }
            public int Quantity { get; set; }
            public double Privex { get; set; }
            public string? RibbonText { get; set; }
            public string? Description { get; set; }
            public string? DiscountCode { get; set; }
            public double? Margin { get; set; } // OrderDetails.MarketingSupplement
            public int LineNumber { get; set; } // OrderDetails.OrderDetailId
            public string? BundleId { get; set; }
            public string? IntercatCode { get; set; }
            public double Price { get; set; }
            public string? Label { get; set; }
            public double FloristFee { get; set; } // OrderDetails.FloristConsideration
            public string? Type { get; set; }
            

            public bool Equals(ProductInformations? other)
            {
                return other != null && ProductId == other.ProductId;
            }

            public override int GetHashCode()
            {
                return ProductId?.GetHashCode() ?? 0;
            }

      
        }
        

        public class CustomerInformations
        {
            public string? Type { get; set; }
            public string? Id { get; set; }
            public string? Title { get; set; }
            public string? Email { get; set; }
            public string? Phone { get; set; }
            public string? FirstName { get; set; }
            public string? LastName { get; set; }
            public string? CompanyName { get; set; }
            public string? EmployeeId { get; set; }
            public string? PartnerCode { get; set; }
        }

        public class PaymentInformations
        {
            public string? Mode { get; set; }
            public string? Provider { get; set; }
            public string? Id { get; set; }
            public string? Status { get; set; }
            public string? ContractNumber { get; set; }
            public string? Schedule { get; set; }
        }

        public class BillingInformations
        {
            public string? City { get; set; }
            public string? Street { get; set; }
            public string? ZipCode { get; set; }
        }
    }
    public static class ProductInformationsExtensions
    {
        public static bool IsDiscountLineItem(this ProductInformations product) => product.ProductId.StartsWith("REM");
        public static bool IsBundlePart(this ProductInformations product) => !string.IsNullOrWhiteSpace(product.BundleId);
        public static string GetProductKey(this ProductInformations product)
        {
            if (product.ProductId.StartsWith("PSM-"))
                return "PSM-" + product.ProductId.Last();

            return product.ProductId;
        }

        public static string GetVariantKey(this ProductInformations product)
        {
            if ((product.Type?.Equals("ACC", StringComparison.OrdinalIgnoreCase) ?? false) || 
                (product.Type?.Equals("COLI", StringComparison.OrdinalIgnoreCase) ?? false) ||
                (product.Type?.Equals("REM", StringComparison.OrdinalIgnoreCase) ?? false))
                return product.ProductId;

            if (product.IsBundlePart())
                return product.BundleId!;

            return string.Format("{0}-{1}-{2}", product.GetProductKey(), product.Size, product.Style).ToUpper();
        }
        public static decimal? GetExecutingFloristAmount(this ProductInformations product) => Convert.ToDecimal(product.Privex + product.FloristFee);
    }


    
}
