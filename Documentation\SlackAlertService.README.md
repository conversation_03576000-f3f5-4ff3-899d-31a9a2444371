# SlackAlertService - Developer Guide

## Overview

The `SlackAlertService` is a robust, production-ready service for sending alerts to Slack channels with advanced features including:
- **Environment & Country Filtering**: Send alerts only to specific environments and countries
- **Dynamic Channel Generation**: Automatically generate channel names based on environment and country
- **Circuit Breaker Pattern**: Resilient error handling with automatic recovery
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Fallback Logging**: Log alerts when Slack is unavailable

## Quick Start

### 1. Service Registration

Add the service to your `Startup.cs` or `Program.cs`:

```csharp
public void ConfigureServices(IServiceCollection services)
{
    // Register SlackAlertService
    services.AddSlackAlertService(Configuration);
}
```

### 2. Basic Usage

Inject and use the service in your controllers/services:

```csharp
public class OrderController : ControllerBase
{
    private readonly ISlackAlertService _slackAlertService;

    public OrderController(ISlackAlertService slackAlertService)
    {
        _slackAlertService = slackAlertService;
    }

    public async Task<IActionResult> ProcessOrder()
    {
        try
        {
            // Your business logic
            return Ok();
        }
        catch (Exception ex)
        {
            // Send error alert to Slack
            await _slackAlertService.SendErrorAlertAsync(
                "Critical error in order processing", 
                ex);
            throw;
        }
    }
}
```

## Configuration

### Environment Variables (Required)

Set these environment variables in your charts values files (should be already present so check):

```yaml
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "prod"  # dev, recette, perf, preprod, prod
  
  - name: ASPNETCORE_COUNTRY  
    value: "fr"    # fr, it, es, pt, dk, se
```

### Vault Configuration (Secrets)

Configure the Slack API token via Vault:

```yaml
podAnnotations:
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/your-microservice'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/your-microservice" -}}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}
```

### Application Configuration

#### Basic Configuration (appsettings.json)

```json
{
  "SlackAlert": {
    "DefaultChannel": "alerts-ms-fr",
    "SendMessageUrl": "https://slack.com/api/chat.postMessage",
    "BotName": "Error Alert Bot",
    "BotEmoji": ":warning:",
    "MaxRetryAttempts": 3,
    "RetryInitialDelayMs": 1000,
    "TimeoutMs": 10000,
    "IncludeStackTrace": true,
    "MaxMessageLength": 15000,
    "EnableFallbackLogging": true,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDurationSeconds": 60,
    "Enabled": true
  }
}
```

#### Advanced Configuration with Filtering

```json
{
  "SlackAlert": {
    "DefaultChannel": "alerts-ms-fr",
    "BotName": "Error Alert Bot",
    "MaxRetryAttempts": 3,
    "DefaultAllowedEnvironments": ["preprod", "prod"],
    "DefaultAllowedCountries": ["fr", "it"],
    "Enabled": true
  }
}
```

## Helm Chart Configuration

### Production France Example

```yaml
# values-prod-france.yaml
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "prod"
  - name: ASPNETCORE_COUNTRY
    value: "fr"

podAnnotations:
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/itf-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/itf-microservices" -}}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}
```

### Preprod Italy Example

```yaml
# values-preprod-italy.yaml
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "preprod"
  - name: ASPNETCORE_COUNTRY
    value: "it"

podAnnotations:
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/iti-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/iti-microservices" -}}
      export SlackAlert__ApiToken={{ .Data.slack_webhook }}
    {{- end }}
```

### Development Environment

```yaml
#  values-dev-france.yaml
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "dev"
  - name: ASPNETCORE_COUNTRY
    value: "fr"
```

## Dynamic Channel Generation

The service automatically generates channel names when no explicit channel is provided:

### Channel Naming Rules

| Environment | Country | Generated Channel |
|-------------|---------|-------------------|
| `prod` | `fr` | `alerts-ms-fr` |
| `preprod` | `it` | `alerts-ms-it-preprod` |
| `dev` | `es` | `alerts-ms-es-dev` |

### Pattern Rules
- **Production**: `alerts-ms-{country}` (removes environment suffix)
- **Development**: `alerts-ms-{country}-debug` (uses "debug" instead of "development")
- **Other environments**: `alerts-ms-{country}-{environment}`

## Usage Examples

### 1. Basic Error Alert

```csharp
try
{
    // Your code
}
catch (Exception ex)
{
    await _slackAlertService.SendErrorAlertAsync(
        "Payment processing failed", 
        ex);
}
```

### 2. Custom Alert with Fields

```csharp
await _slackAlertService.SendCustomAlertAsync(
    "Deployment Completed",
    "New version deployed successfully",
    fields: new Dictionary<string, string>
    {
        { "Version", "1.2.3" },
        { "Environment", "Production" },
        { "Deployed By", "CI/CD Pipeline" }
    },
    color: "good"); // Green color
```

### 3. Environment-Specific Alerts

```csharp
// Only send to production environments
var prodOnly = new AlertLevel(AlertEnvironment.Prod);
await _slackAlertService.SendErrorAlertAsync(
    "Critical production issue",
    ex,
    alertLevel: prodOnly);
```

### 4. Country-Specific Alerts

```csharp
// Send to France and Italy, preprod and prod only
var environments = new[] { AlertEnvironment.Preprod, AlertEnvironment.Prod };
var countries = new[] { AlertCountry.Fr, AlertCountry.It };
var alertLevel = new AlertLevel(environments, countries);

await _slackAlertService.SendCustomAlertAsync(
    "Regional Maintenance Notice",
    "Scheduled maintenance for France and Italy",
    alertLevel: alertLevel);
```

### 5. Explicit Channel Override

```csharp
// Send to specific channel regardless of dynamic generation
await _slackAlertService.SendCustomAlertAsync(
    "Custom Alert",
    "This goes to a specific channel",
    channel: "alerts-custom-channel");
```

## Environment & Country Filtering

### Filtering Priority (Highest to Lowest)

1. **Method-level `AlertLevel`** - Overrides everything
2. **Configuration-level defaults** - `DefaultAllowedEnvironments` + `DefaultAllowedCountries`
3. **No filtering** - Send to all environments/countries (original behavior)

### Supported Values

**Environments**: `dev`, `recette`, `perf`, `preprod`, `prod`, `development`
**Countries**: `fr`, `it`, `es`, `pt`, `dk`, `se`

### Configuration Examples

```json
{
  "SlackAlert": {
    // Send only to production and preprod by default
    "DefaultAllowedEnvironments": ["preprod", "prod"],
    
    // Send only to France and Italy by default  
    "DefaultAllowedCountries": ["fr", "it"]
  }
}
```

## Error Handling & Resilience

### Circuit Breaker
- Trips after 5 consecutive failures (configurable)
- Stays open for 60 seconds (configurable)
- Automatically recovers when Slack becomes available

### Retry Logic
- 3 retry attempts with exponential backoff (configurable)
- Initial delay: 1 second, doubles each retry

### Fallback Logging
- When Slack is unavailable, alerts are logged to the application logger
- Includes all alert details for debugging

## Troubleshooting

### Common Issues

1. **Alerts not appearing in Slack**
   - Check `ASPNETCORE_ENVIRONMENT` and `ASPNETCORE_COUNTRY` environment variables
   - Verify Slack API token in Vault
   - Check filtering configuration

2. **Wrong channel being used**
   - Verify environment variables are set correctly
   - Check dynamic channel generation rules
   - Consider using explicit channel parameter

3. **Circuit breaker tripped**
   - Check Slack API status
   - Review application logs for connection errors
   - Wait for automatic recovery (60 seconds by default)

### Debugging

Enable debug logging to see filtering decisions:

```json
{
  "Logging": {
    "LogLevel": {
      "ITF.SharedLibraries.Alerting": "Debug"
    }
  }
}
```

## Best Practices

1. **Use environment filtering** for production alerts to avoid spam in development
2. **Set appropriate retry limits** based on your alert criticality
3. **Use meaningful alert titles** and include relevant context in fields
4. **Test alerts in development** before deploying to production
5. **Monitor circuit breaker status** to ensure Slack connectivity
6. **Use explicit channels** for critical alerts that must reach specific teams

## Integration with Existing Services

The SlackAlertService integrates seamlessly with:
- **Kafka error handling** (automatic alerts on consumer failures)
- **Global exception handlers** (catch-all error reporting)
- **Health checks** (alert on service degradation)
- **Business logic validation** (custom alerts for business rule violations)

## Configuration Reference

### Complete Configuration Options

```json
{
  "SlackAlert": {
    // Required (via Vault)
    "ApiToken": "xoxb-your-slack-bot-token",

    // Channel Configuration
    "DefaultChannel": "alerts-ms-fr",
    "SendMessageUrl": "https://slack.com/api/chat.postMessage",

    // Bot Appearance
    "BotName": "Error Alert Bot",
    "BotEmoji": ":warning:",

    // Retry & Timeout Configuration
    "MaxRetryAttempts": 3,
    "RetryInitialDelayMs": 1000,
    "TimeoutMs": 10000,

    // Circuit Breaker Configuration
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDurationSeconds": 60,

    // Message Configuration
    "IncludeStackTrace": true,
    "MaxMessageLength": 15000,
    "EnableFallbackLogging": true,

    // Filtering Configuration (optional)
    "DefaultAllowedEnvironments": ["preprod", "prod"],
    "DefaultAllowedCountries": ["fr", "it"],

    // Global Enable/Disable
    "Enabled": true
  }
}
```

### Environment-Specific Configurations

#### Production Configuration
```json
{
  "SlackAlert": {
    "DefaultAllowedEnvironments": ["prod"],
    "MaxRetryAttempts": 5,
    "CircuitBreakerThreshold": 3,
    "IncludeStackTrace": false,
    "EnableFallbackLogging": true
  }
}
```

#### Development Configuration
```json
{
  "SlackAlert": {
    "DefaultAllowedEnvironments": ["development", "dev"],
    "MaxRetryAttempts": 1,
    "CircuitBreakerThreshold": 10,
    "IncludeStackTrace": true,
    "EnableFallbackLogging": false
  }
}
```

## Advanced Usage Patterns

### 1. Global Exception Handler Integration

```csharp
public class GlobalExceptionMiddleware
{
    private readonly ISlackAlertService _slackAlertService;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            // Send critical alerts only for production
            var prodOnly = new AlertLevel(AlertEnvironment.Prod);
            await _slackAlertService.SendErrorAlertAsync(
                $"Unhandled exception in {context.Request.Path}",
                ex,
                alertLevel: prodOnly);
            throw;
        }
    }
}
```

### 2. Business Logic Alerts

```csharp
public class OrderService
{
    private readonly ISlackAlertService _slackAlertService;

    public async Task ProcessHighValueOrder(Order order)
    {
        if (order.Amount > 10000)
        {
            // Alert for high-value orders in all environments
            await _slackAlertService.SendCustomAlertAsync(
                "High Value Order Alert",
                $"Order {order.Id} with amount {order.Amount:C} requires attention",
                fields: new Dictionary<string, string>
                {
                    { "Order ID", order.Id },
                    { "Amount", order.Amount.ToString("C") },
                    { "Customer", order.CustomerEmail },
                    { "Country", order.DeliveryCountry }
                },
                color: "warning");
        }
    }
}
```

### 3. Health Check Integration

```csharp
public class DatabaseHealthCheck : IHealthCheck
{
    private readonly ISlackAlertService _slackAlertService;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context)
    {
        try
        {
            // Check database connectivity
            var isHealthy = await CheckDatabaseConnection();

            if (!isHealthy)
            {
                // Alert only production and preprod
                var alertLevel = new AlertLevel(
                    AlertEnvironment.Prod,
                    AlertEnvironment.Preprod);

                await _slackAlertService.SendCustomAlertAsync(
                    "Database Health Check Failed",
                    "Database connectivity issues detected",
                    color: "danger",
                    alertLevel: alertLevel);

                return HealthCheckResult.Unhealthy("Database connection failed");
            }

            return HealthCheckResult.Healthy();
        }
        catch (Exception ex)
        {
            await _slackAlertService.SendErrorAlertAsync(
                "Health check exception", ex);
            return HealthCheckResult.Unhealthy(ex.Message);
        }
    }
}
```

## Performance Considerations

### Memory Usage
- Service uses a semaphore to limit concurrent requests
- Circuit breaker maintains minimal state
- HTTP client is reused across requests

### Network Efficiency
- Configurable timeouts prevent hanging requests
- Exponential backoff reduces server load during outages
- Circuit breaker prevents cascade failures

### Async Best Practices
```csharp
// ✅ Good - Fire and forget for non-critical alerts
_ = Task.Run(async () => await _slackAlertService.SendCustomAlertAsync(
    "Info", "Non-critical notification"));

// ✅ Good - Await for critical alerts
await _slackAlertService.SendErrorAlertAsync("Critical error", ex);

// ❌ Avoid - Blocking async calls
_slackAlertService.SendErrorAlertAsync("Error", ex).Wait();
```

## Security Considerations

### API Token Management
- Store tokens in Vault, never in code or config files
- Use environment-specific tokens
- Rotate tokens regularly

### Channel Access
- Use private channels for sensitive alerts
- Implement channel naming conventions
- Review channel permissions regularly

### Data Sensitivity
```csharp
// ❌ Don't include sensitive data
await _slackAlertService.SendErrorAlertAsync(
    $"Login failed for {user.Email} with password {user.Password}", ex);

// ✅ Sanitize sensitive information
await _slackAlertService.SendErrorAlertAsync(
    $"Login failed for user {user.Email?.Substring(0, 3)}***", ex);
```

## Monitoring & Observability

### Metrics to Monitor
- Alert success/failure rates
- Circuit breaker state changes
- Average response times
- Retry attempt frequencies

### Logging Integration
```csharp
// The service automatically logs:
// - Configuration validation errors
// - Circuit breaker state changes
// - Retry attempts
// - Fallback logging when Slack is unavailable
```

### Health Check for SlackAlertService
```csharp
public async Task<bool> IsSlackHealthy()
{
    return await _slackAlertService.IsServiceAvailableAsync();
}
```

## Migration Guide

### From Legacy Alert Systems

1. **Replace direct Slack webhook calls**:
```csharp
// Old way
await httpClient.PostAsync(webhookUrl, content);

// New way
await _slackAlertService.SendCustomAlertAsync(title, message);
```

2. **Update configuration**:
   - Move API tokens to Vault
   - Add environment variables
   - Configure filtering rules

3. **Test thoroughly**:
   - Verify alerts in all environments
   - Test filtering behavior
   - Validate channel generation

## FAQ

**Q: Why aren't my alerts appearing in Slack?**
A: Check environment filtering configuration and ensure `ASPNETCORE_ENVIRONMENT`/`ASPNETCORE_COUNTRY` are set correctly.

**Q: Can I disable alerts temporarily?**
A: Set `"Enabled": false` in configuration or use environment-specific filtering.


**Q: What happens if Slack is down?**
A: The circuit breaker will trip and alerts will be logged instead. Service automatically recovers when Slack is available.

**Q: Can I customize the message format?**
A: Use `SendCustomAlertAsync` with custom fields and colors. The service handles Slack formatting automatically.
