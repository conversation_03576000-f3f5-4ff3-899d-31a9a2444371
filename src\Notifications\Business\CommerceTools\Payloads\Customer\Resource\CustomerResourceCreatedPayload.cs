﻿using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Customer.Resource
{
    public class CustomerResourceCreatedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public ICustomer Customer { get; set; }
        public ResourceCreatedDeliveryPayload ResourceCreated { get; set; }

    }
}
