﻿using commercetools.Base.Client;
using commercetools.Sdk.Api.Client.RequestBuilders.InStore;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedModels.Group.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Orders
{
    public class OrderUpdateService : IOrderUpdateService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<OrderUpdateService> _logger;
        private readonly string _projectKey;
        private readonly string _countryStoreKey;
        private readonly ByProjectKeyInStoreKeyByStoreKeyOrdersRequestBuilder ordersRequestBuilder;
        private readonly IOrderQueryService _orderQueryService;

        public OrderUpdateService(
            IClient commerceToolsClient,
            IConfiguration configuration,
            ILogger<OrderUpdateService> logger,
            IOrderQueryService orderQueryService)
        {
            _configuration = configuration;
            _logger = logger;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
            _countryStoreKey = _configuration.GetSection("CommerceToolCustomSettings:LocalCountryStoreKey").Value;
            ordersRequestBuilder = commerceToolsClient.WithApi().WithProjectKey(_projectKey).InStoreKeyWithStoreKeyValue(_countryStoreKey).Orders();
            _orderQueryService = orderQueryService;
        }
        
        public async Task<IOrder> UpdateCustomField(string orderId, long orderVersion, string orderNumber, string customFieldName, object customFieldValue, bool retry)
        {
            // we update the "read" field when the order is updated
            var orderUpdate = GetOrderUpdate(customFieldName, customFieldValue, orderVersion);
            orderUpdate.Actions.Add(GetOrderSetCustomFieldAction(CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST, true));

            if (retry)
            {
                return await PostOrderUpdateWithRetry(orderUpdate, orderId, orderNumber);
            }
            else
            {
                return await PostOrderUpdate(orderUpdate, orderId);
            }
        }

        public async Task<IOrder> UpdateReadByExecutingFloristCustomFieldForced(string orderId, long orderVersion, string orderNumber, bool value)
        {           
            var orderUpdate = GetOrderUpdate(CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST, value, orderVersion);
            return await PostOrderUpdateWithRetry(orderUpdate, orderId, orderNumber);
        }

        public async Task<IOrder> UpdateReadByExecutingFloristCustomField(string orderId, long orderVersion, string orderNumber, string executingFloristId, string transmitterFloristId)
        {

            if(!executingFloristId.Equals(transmitterFloristId))
            {
                var orderUpdate = GetOrderUpdate(CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST, true, orderVersion);
                return await PostOrderUpdateWithRetry(orderUpdate, orderId, orderNumber);
            }
            return null;
        }

        /// <summary>
        /// When an order is refused or reassigned, we reset executing florist, read, in delivery
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="orderVersion"></param>
        /// <param name="orderNumber"></param>
        /// <returns>The order</returns>
        public async Task<IOrder> RefuseOrder(string orderId, long orderVersion, string orderNumber)
        {
            var orderUpdate = GetOrderUpdate(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, null, orderVersion);
            orderUpdate.Actions.Add(GetOrderSetCustomFieldAction(CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST, false));
            orderUpdate.Actions.Add(GetOrderSetCustomFieldAction(CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS, false));
            return await PostOrderUpdateWithRetry(orderUpdate, orderId, orderNumber);
        }

        private async Task<IOrder> PostOrderUpdate(OrderUpdate orderUpdate, string orderId)
        {
            return await ordersRequestBuilder
            .WithId(orderId)
            .Post(orderUpdate)
            .ExecuteAsync();
        }

        private async Task<IOrder> PostOrderUpdateWithRetry(OrderUpdate orderUpdate, string orderId, string orderNumber)
        {
            try
            {
                return await PostOrderUpdate(orderUpdate, orderId);
            }
            catch (System.Exception e)
            {
                _logger.LogInformation(e, "Failed to update order {orderNumber} => retry with GetOder", orderNumber);

                IOrder order = await _orderQueryService.GetOrderWithoutExpand(orderNumber);
                if (order == null)
                {
                    _logger.LogError("Order {orderNumber} not found for retry", orderNumber);
                }
                else
                {
                    // we use the last version number from CT order
                    orderUpdate.Version = order.Version;

                    return await PostOrderUpdate(orderUpdate, order.Id);
                }
            }
            return null;
        }

        private static OrderUpdate GetOrderUpdate(string customFieldName, object customFieldValue, long version)
        {
            return new OrderUpdate()
            {
                Version = version,
                Actions = new List<IOrderUpdateAction>
                {
                   GetOrderSetCustomFieldAction(customFieldName, customFieldValue),
                }
            };
        }

        private static OrderSetCustomFieldAction GetOrderSetCustomFieldAction(string fieldName, object value)
        {
            return new OrderSetCustomFieldAction()
            {
                Name = fieldName,
                Value = value
            };
        }
    }
}
