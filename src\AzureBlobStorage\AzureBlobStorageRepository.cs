﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using System;
using System.IO;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.AzureBlobStorage
{
    public class AzureBlobStorageRepository : IAzureBlobStorageRepository
    {
        //private readonly BlobServiceClient _blobServiceClient;
        private BlobContainerClient _containerClient;
        private readonly string _container;

        public AzureBlobStorageRepository(Configuration configuration)
        {
            //_blobServiceClient = new BlobServiceClient(configuration.ConnectionString);
            _container = configuration.ContainerName;
            _containerClient = new BlobContainerClient(configuration.ConnectionString, _container);
        }

        public async Task<Uri> UploadAsync(string friendlyFileName, string path, string blobName, byte[] stream)
        {
            // https://github.com/Azure/azure-sdk-for-net/issues/11770
            await _containerClient.CreateIfNotExistsAsync();

            // ie /2021/06/hash
            var fullfileNamePath = Path.Combine(path, blobName);

            // Get a reference to a blob
            var blob = _containerClient.GetBlobClient(fullfileNamePath);

            // Headers
            var contentDisposition = $"inline; filename={friendlyFileName}";
            var headers = new BlobHttpHeaders
            {
                ContentType = "application/pdf",
                ContentDisposition = contentDisposition
            };

            var uploadedFile = await blob.UploadAsync(new MemoryStream(stream), headers);
            return blob.Uri;
        }
    }
}
