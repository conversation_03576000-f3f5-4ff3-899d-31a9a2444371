﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class OrderSentToDynamicsPayload : IPayload
    {
        public string OrderId { get; set; }
        public int Version { get; set; }
        public string FloristId { get; set; }
        public int DynamicsSendCounter { get; set; }
        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
