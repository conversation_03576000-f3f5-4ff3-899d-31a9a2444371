﻿using App.Metrics;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Prometheus;
using App.Metrics.Formatters.Prometheus;

namespace ITF.SharedLibraries.ApplicationMetrics.Extensions
{
    public static class MetricsExtensions
    {
        public static IServiceCollection AddAllMetrics(this IServiceCollection services)
        {
            var metrics = AppMetrics.CreateDefaultBuilder()
                                .OutputMetrics.AsPrometheusPlainText()
                                .OutputMetrics.AsPrometheusProtobuf()
                                .Build();

            services.AddMetrics(metrics);
            services.AddMetricsTrackingMiddleware();
            services.AddMetricsEndpoints(opt =>
            {
                opt.MetricsTextEndpointOutputFormatter = new MetricsPrometheusTextOutputFormatter();
                opt.MetricsEndpointOutputFormatter = new MetricsPrometheusTextOutputFormatter(); // MetricsPrometheusProtobufOutputFormatter => KO
                opt.EnvironmentInfoEndpointEnabled = false;
            });
            services.AddMetricsReportingHostedService();
            services.AddAppMetricsHealthPublishing();
            return services;
        }

        public static IApplicationBuilder UseAllMetricsMiddleware(this IApplicationBuilder app)
        {
            // Routes 
            // /metrics
            // /metrics-text
            // /env
            app.UseMetricsAllMiddleware();
            app.UseMetricsAllEndpoints();
            //app.UseMetricServer();
            //sapp.UseHttpMetrics();
            app.UseHttpMetrics(); // WARNING : after UseRouting in pipeline

            return app;
        }
    }
}
