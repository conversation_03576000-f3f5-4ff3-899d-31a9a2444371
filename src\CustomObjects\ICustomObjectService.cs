﻿using commercetools.Sdk.Api.Models.CustomObjects;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.CustomObjects
{
    public interface ICustomObjectService
    {
        Task<ICustomObject> CreateOrUpdate(ICustomObjectDraft draft);
        Task<ICustomObject> Delete(string container, string key);
        Task<List<ICustomObject>> GetByContainer(string container);
    }
}