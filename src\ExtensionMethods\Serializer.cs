﻿using commercetools.Sdk.Api.Serialization;
using MessagePack;
using MessagePack.Resolvers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Text.Json;

namespace ITF.SharedLibraries.ExtensionMethods
{
    public static class Serializer
    {
        private static IFormatterResolver FormatterResolver => CompositeResolver.Create(
                         // resolver custom types first
                         NativeDateTimeResolver.Instance,
                        ContractlessStandardResolver.Instance,
                        TypelessContractlessStandardResolver.Instance
                    );

        public static MessagePackSerializerOptions MessagePackSerializerOptions =>
            MessagePackSerializerOptions.Standard.WithResolver(FormatterResolver);


        public enum SerializerType
        {
            NewtonSoft,
            TextJson,
            Utf8Json,
            MessagePack,
            CommerceTools
        }

        public static string? Serialize(this object o, SerializerType serializerType = SerializerType.TextJson, SerializerService? serializerService = null, JsonSerializerOptions? options = null) {
            try
            {
                if (o is null)
                    return null;

                if (o is string)
                    return o as string;

                var res = ApplySerialization(o, serializerType, serializerService, options);
                return res;
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, e.Message);
                return null;
            }
        }

        private static string ApplySerialization(object obj, SerializerType serializerType, SerializerService? serializerService = null, JsonSerializerOptions? options = null)
        {
            switch (serializerType)
            {
                case SerializerType.NewtonSoft:
                    return JsonConvert.SerializeObject(obj);
                case SerializerType.TextJson:
                    return System.Text.Json.JsonSerializer.Serialize(obj, options);
                case SerializerType.Utf8Json:
                    return Utf8Json.JsonSerializer.ToJsonString(obj);
                case SerializerType.MessagePack:
                    var bin = MessagePackSerializer.Serialize(obj, MessagePackSerializerOptions);
                    return MessagePackSerializer.ConvertToJson(bin, MessagePackSerializerOptions);
                case SerializerType.CommerceTools:
                    if (serializerService is null)
                        throw new Exception($"SerializerService must be supplied when using {SerializerType.CommerceTools} serializer");
                    return serializerService.Serialize(obj);
                default:
                    return JsonConvert.SerializeObject(obj);
            }
        }

        public static bool IsValidJson(this string? input)
        {
            if (input is null)
                return false;

            input = input.Trim();
            if ((input.StartsWith("{") && input.EndsWith("}")) || //For object
                (input.StartsWith("[") && input.EndsWith("]"))) //For array
            {
                try
                {
                    //parse the input into a JObject
                    var jObject = JObject.Parse(input);

                    foreach (var jo in jObject)
                    {
                        JToken? value = jo.Value;

                        //if the element has a missing value, it will be Undefined - this is invalid
                        if (value is null || value.Type == JTokenType.Undefined)
                        {
                            return false;
                        }
                    }
                }
                catch //some other exception
                {
                    return false;
                }
            }
            else
            {
                return false;
            }

            return true;
            
        }
    }
}
