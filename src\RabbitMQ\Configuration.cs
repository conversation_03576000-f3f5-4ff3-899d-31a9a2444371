﻿using ITF.SharedLibraries.RabbitMQ.Subscriber;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.RabbitMQ
{
    public class Configuration
    {
        public Configuration()
        {
            SubscriberConfigurations = new List<SubscriberConfiguration>();
        }

        public string UserName { get; set; }

        public string Password { get; set; }

        public string HostName { get; set; }

        public int Port { get; set; } = 5672;

        public string VHost { get; set; } = "/";

        public List<SubscriberConfiguration> SubscriberConfigurations { get; set; }
    }
}
