﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.AzureBlobStorage
{
    public static class AzureBlobStorageExtensions
    {
        public static IServiceCollection UseAzureBlobStorage(this IServiceCollection services, IConfiguration config, string varEnv = "AzureBlobStorage")
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);
            services.AddSingleton<IAzureBlobStorageRepository, AzureBlobStorageRepository>();

            return services;
        }
    }
}
