﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class AbsentRecipientEmailMessage : BaseMessage<AbsentRecipientEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.OrderEmails?.FirstOrDefault()?.Customer?.Email;
        }
    }
}

public class AbsentRecipientEmailPayload : IPayload
{
    public List<OrderEmail> OrderEmails { get; set; } = new List<OrderEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}