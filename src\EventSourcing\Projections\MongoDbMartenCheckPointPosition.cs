﻿using ITF.Lib.Common.DomainDrivenDesign;
using System;

namespace ITF.SharedLibraries.EventSourcing.Projections
{
    public class MongoDbMartenCheckPointPosition : BaseClass<string>
    {
        public long Position { get; set; }

        public MongoDbMartenCheckPointPosition(string checkPointName, long position)
        {
            Id = checkPointName;
            Position = position;
        }
        public MongoDbMartenCheckPointPosition()
        {
        }
        public override void SetId()
        {
            throw new NotImplementedException();
        }
    }
}
