﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class CatalogTranslationPayload : IPayload
    {
        public List<CatalogTranslation> CatalogTranslations { get; set; } = new List<CatalogTranslation>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class CatalogTranslation
    {
        public string ProductNumber { get; set; }
        public string Name { get; set; }
        public string Language { get; set; }
        public string Description { get; set; }
        public DateTime LastModified { get; set; }
    }
}
