﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ITF.SharedLibraries.CustomBackgroundService;

// see this for explanation and details : https://blog.stephencleary.com/2020/06/backgroundservice-gotcha-application-lifetime.html
public abstract class CriticalBackgroundService : BackgroundService
{
    protected readonly IHostApplicationLifetime _applicationLifetime;
    protected readonly ILogger<CriticalBackgroundService> _logger;
    static TaskCompletionSource? startedSource = null;
    static TaskCompletionSource? cancelledSource = null;
    static CancellationTokenRegistration? startRegistration = null;

    protected CriticalBackgroundService(IHostApplicationLifetime applicationLifetime, ILogger<CriticalBackgroundService> logger)
    {
        _applicationLifetime = applicationLifetime;
        _logger = logger;
    }

    // The actual Process that should not exit -> if it exit we will Stop the Application
    protected abstract Task InfiniteProcessAsync(CancellationToken cancellationToken);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // see here for the wait Application start process : https://andrewlock.net/finding-the-urls-of-an-aspnetcore-app-from-a-hosted-service-in-dotnet-6/
        if (!await WaitForAppStartup(_applicationLifetime, stoppingToken))
        {
            _logger.LogError("Impossible to Start the BackgroundService , the Application Host is not started !");
            _applicationLifetime?.StopApplication();
            return;
        }

        try
        {
            await InfiniteProcessAsync(stoppingToken);
        }
        catch (Exception ex) when (False(() => _logger.LogCritical(ex, "Fatal error in BackgroundService"))) // to keep the exception context and log it
        {
            throw;
        }
        finally
        {
            _logger.LogCritical("We Stop the Application because of the BackgroundService crash");
            await Task.Delay(5000);
            _applicationLifetime?.StopApplication();
        }
    }

    private static async Task<bool> WaitForAppStartup(IHostApplicationLifetime lifetime, CancellationToken stoppingToken)
    {
        startedSource ??= new TaskCompletionSource();
        cancelledSource ??= new TaskCompletionSource();

        if (startRegistration == null)
        {
            startRegistration = lifetime.ApplicationStarted.Register(() => startedSource.SetResult());
            stoppingToken.Register(() => cancelledSource.SetResult());
        }

        Task completedTask = await Task.WhenAny(
            startedSource.Task,
            cancelledSource.Task).ConfigureAwait(false);

        // If the completed tasks was the "app started" task, return true, otherwise false
        return completedTask == startedSource.Task;
    }

    private static bool False(Action action)
    {
        action();
        return false;
    }
}