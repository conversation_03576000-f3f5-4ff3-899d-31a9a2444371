{
  "ElasticApm": {
    "ServerUrl": "http://apm:8200"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "http://elasticsearch:9200/"
  },
  "Unleash": {

    "Url": "http://unleash:4242/api/"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  },
  "Kafka": {
    "BootStrapServers": "kafka:9092",
    "SubscriberConfigurations": [
      {
        "AutoOffsetReset": 1,
        "ClassName": "OrderHandler",
        "EnableAutoCommit": false,
        "ManualCommitPeriod": 1,
        "EnablePartitionEof": false,
        "GroupId": "IT.Microservices.OrderReactor.consumer",
        "TopicName": "order"
      },
      {
        "AutoOffsetReset": 1,
        "ClassName": "RAOLegacyOrderHandler",
        "EnableAutoCommit": false,
        "ManualCommitPeriod": 1,
        "EnablePartitionEof": false,
        "GroupId": "IT.Microservices.OrderReactor.consumer",
        "TopicName": "legacy_order"
      }
    ],
    "TopicsToCreateConfigurations": [
      {
        "TopicName": "order",
        "ReplicationFactor": 1,
        "NumberOfPartitions": 1,
        "RetentionMs": 7200000 //2h
      },
      {
        "TopicName": "legacy_order",
        "ReplicationFactor": 1,
        "NumberOfPartitions": 1,
        "RetentionMs": 7200000 //2h
      }
    ]
  },
  "KafkaTopicsSettings": {
    "Order": "order"
  },
  //"Client": {
  //  "ClientId": "LR1T07lIzIexRQy9ojBZ0Ece",
  //  "ClientSecret": "CU9UpgBGVrFQUqy6RSbnGqK0iduR-6dV",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "myflower-dev", // replace with your project key
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/"
  //},

  //"Client": {
  //  "ClientId": "v3TQObln_UYSQeoDUy4O-JLE",
  //  "ClientSecret": "70jmEpRljUpt9_4bfm_EK2XLxShF1Oyr",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "interfloratest",
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
  //  "StoreProjectionKey": "ITI", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
  //  "ChannelKey": "interflora.it" // Channle used to get prices
  //},

  //Italy recette
  //"Client": {
  //  "ClientId": "v3TQObln_UYSQeoDUy4O-JLE",
  //  "ClientSecret": "70jmEpRljUpt9_4bfm_EK2XLxShF1Oyr",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "interfloratest",
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
  //  "StoreProjectionKey": "ITI", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
  //  "ChannelKey": "interflora.it" // Channle used to get prices
  //},
  //"CommerceToolCustomSettings": {
  //  "LocalCountryCode": "IT",
  //  "LocalCountryChannelKey": "interflora.it",
  //  "LocalCountryStoreKey": "ITI",
  //  "CtMoruningProductTypeKey": "mourning",
  //  "LocalCountryAccessoriesCategoryKey": "ACC",
  //  "LocalCountryProductsCategoryKey": "category",
  //  "OutboundOrderShippingMethodKey": "pfs-international-italy",
  //  "MourningShippingMethodKey": "mourning",
  //  "PfsShippingMethodKey": "pfs"
  //},

  //Spain recette
  //"Client": {
  //  "ClientId": "v3TQObln_UYSQeoDUy4O-JLE",
  //  "ClientSecret": "70jmEpRljUpt9_4bfm_EK2XLxShF1Oyr",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "interfloratest",
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
  //  "StoreProjectionKey": "ITE", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
  //  "ChannelKey": "interflora.es" // Channle used to get prices
  //},
  //"CommerceToolCustomSettings": {
  //  "LocalCountryCode": "ES",
  //  "LocalCountryChannelKey": "interflora.es",
  //  "LocalCountryStoreKey": "ITE",
  //  "CtMoruningProductTypeKey": "mourning",
  //  "LocalCountryAccessoriesCategoryKey": "ite-itp-general-accessories",
  //  "LocalCountryProductsCategoryKey": "category",
  //  "OutboundOrderShippingMethodKey": "pfs-international-spain",
  //  "MourningShippingMethodKey": "mourning",
  //  "PfsShippingMethodKey": "pfs",
  //  "MarketingFeeToBeSettedOnProductForInternationalOutbound": 9.99
  //},

  // France recette
  //"Client": {
  //  "ClientId": "v3TQObln_UYSQeoDUy4O-JLE",
  //  "ClientSecret": "70jmEpRljUpt9_4bfm_EK2XLxShF1Oyr",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "interfloratest",
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
  //  "StoreProjectionKey": "ITF", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
  //  "ChannelKey": "interflora.fr" // Channle used to get prices
  //},

  "CommerceToolCustomSettings": {
    "LocalCountryCode": "FR",
    "LocalCountryChannelKey": "interflora.fr",
    "LocalCountryStoreKey": "ITF",
    "CtMoruningProductTypeKey": "mourning",
    "LocalCountryAccessoriesCategoryKey": "ACC",
    "LocalCountryProductsCategoryKey": "category",
    "OutboundOrderShippingMethodKey": "pfs-international-italy",
    "MourningShippingMethodKey": "mourning",
    "PfsShippingMethodKey": "pfs"
  },

  // France preprod
  "Client": {
    "ClientId": "s9YUSf98nNFvgNqUZDLUvVw6",
    "ClientSecret": "AUuAKCQxCidtgDQsaI7Etju63-6tOKt1",
    "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
    "ProjectKey": "myflower-preprod",
    "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
    "StoreProjectionKey": "ITF", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
    "ChannelKey": "interflora.fr" // Channle used to get prices
  },

  //"CommerceToolCustomSettings": {
  //  "LocalCountryCode": "FR",
  //  "LocalCountryChannelKey": "interflora.fr",
  //  "LocalCountryStoreKey": "ITF",
  //  "CtMoruningProductTypeKey": "mourning",
  //  "LocalCountryAccessoriesCategoryKey": "ACC",
  //  "LocalCountryProductsCategoryKey": "category",
  //  "OutboundOrderShippingMethodKey": "pfs-international-italy",
  //  "MourningShippingMethodKey": "mourning",
  //  "PfsShippingMethodKey": "pfs"
  //},

  "RAOEndpoint": {
    "Url": "https://itf-tst-rao-aap.azurewebsites.net/",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },

  "SpanishOrderSettings": {
    "ITFPLUSproductKey": "ITFPLUS",
    "VatKey": "vat",
    "VatReducedKey": "vat-reduced",
    "PfsShippingMethodKey": "pfs",
    "PfsReducedShippingMethodKey": "pfs-reduced",
    "PfsMourningReducedShippingMethodKey": "reduced-mourning",
    "CityStates": [
      {
        "CityName": "Ceuta",
        "State": "ceuta"
      },
      {
        "CityName": "Melilla",
        "State": "melilla"
      }
    ],
    "States": [
      {
        "code": "16",
        "key": "basque country",
        "name": "País Basc"
      },
      {
        "code": "08",
        "key": "castilla-la mancha",
        "name": "Castella - la Manxa"
      },
      {
        "code": "10",
        "key": "valencian community",
        "name": "Comunitat Valenciana"
      },
      {
        "code": "01",
        "key": "andalusia",
        "name": "Andalusia"
      },
      {
        "code": "07",
        "key": "castile and leon",
        "name": "Castella i Lleó"
      },
      {
        "code": "11",
        "key": "extremadura",
        "name": "Extremadura"
      },
      {
        "code": "04",
        "key": "balearic islands",
        "name": "Illes Balears"
      },
      {
        "code": "09",
        "key": "catalonia",
        "name": "Catalunya"
      },
      {
        "code": "12",
        "key": "galicia",
        "name": "Galícia"
      },
      {
        "code": "02",
        "key": "aragon",
        "name": "Aragó"
      },
      {
        "code": "17",
        "key": "la rioja",
        "name": "La Rioja"
      },
      {
        "code": "13",
        "key": "community of madrid",
        "name": "Comunitat de Madrid"
      },
      {
        "code": "14",
        "key": "region of murcia",
        "name": "Regió de Múrcia"
      },
      {
        "code": "15",
        "key": "navarre",
        "name": "Comunitat Foral de Navarra"
      },
      {
        "code": "03",
        "key": "asturias",
        "name": "Principat d'Astúries"
      },
      {
        "code": "05",
        "key": "canary islands",
        "name": "Canàries"
      },
      {
        "code": "06",
        "key": "cantabria",
        "name": "Cantàbria"
      },
      {
        "code": "18",
        "key": "ceuta",
        "name": "Ceuta"
      },
      {
        "code": "19",
        "key": "melilla",
        "name": "Melilla"
      },
      {
        "code": "98",
        "name": "No consta"
      },
      {
        "code": "99",
        "name": "Altres/Diversos"
      }
    ],
    "Provinces": [
      {
        "code": "01",
        "name": "Àlaba",
        "stateCode": "16",
        "postalCodePrefixes": [ "01" ]
      },
      {
        "code": "02",
        "name": "Albacete",
        "stateCode": "08",
        "postalCodePrefixes": [ "02" ]
      },
      {
        "code": "03",
        "name": "Alacant",
        "stateCode": "10",
        "postalCodePrefixes": [ "03" ]
      },
      {
        "code": "04",
        "name": "Almeria",
        "stateCode": "01",
        "postalCodePrefixes": [ "04" ]
      },
      {
        "code": "05",
        "name": "Àvila",
        "stateCode": "07",
        "postalCodePrefixes": [ "05" ]
      },
      {
        "code": "06",
        "name": "Badajoz",
        "stateCode": "11",
        "postalCodePrefixes": [ "06" ]
      },
      {
        "code": "07",
        "name": "Illes Balears",
        "stateCode": "04",
        "postalCodePrefixes": [ "07" ]
      },
      {
        "code": "08",
        "name": "Barcelona",
        "stateCode": "09",
        "postalCodePrefixes": [ "08" ]
      },
      {
        "code": "09",
        "name": "Burgos",
        "stateCode": "07",
        "postalCodePrefixes": [ "09" ]
      },
      {
        "code": "10",
        "name": "Càceres",
        "stateCode": "11",
        "postalCodePrefixes": [ "10" ]
      },
      {
        "code": "11",
        "name": "Cadis",
        "stateCode": "01",
        "postalCodePrefixes": [ "11" ]
      },
      {
        "code": "12",
        "name": "Castelló de la Plana",
        "stateCode": "10",
        "postalCodePrefixes": [ "12" ]
      },
      {
        "code": "13",
        "name": "Ciudad Real",
        "stateCode": "08",
        "postalCodePrefixes": [ "13" ]
      },
      {
        "code": "14",
        "name": "Còrdova",
        "stateCode": "01",
        "postalCodePrefixes": [ "14" ]
      },
      {
        "code": "15",
        "name": "La Corunya",
        "stateCode": "12",
        "postalCodePrefixes": [ "15" ]
      },
      {
        "code": "16",
        "name": "Conca",
        "stateCode": "08",
        "postalCodePrefixes": [ "16" ]
      },
      {
        "code": "17",
        "name": "Girona",
        "stateCode": "09",
        "postalCodePrefixes": [ "17" ]
      },
      {
        "code": "18",
        "name": "Granada",
        "stateCode": "01",
        "postalCodePrefixes": [ "18" ]
      },
      {
        "code": "19",
        "name": "Guadalajara",
        "stateCode": "08",
        "postalCodePrefixes": [ "19" ]
      },
      {
        "code": "20",
        "name": "Guipúscoa",
        "stateCode": "16",
        "postalCodePrefixes": [ "20" ]
      },
      {
        "code": "21",
        "name": "Huelva",
        "stateCode": "01",
        "postalCodePrefixes": [ "21" ]
      },
      {
        "code": "22",
        "name": "Osca",
        "stateCode": "02",
        "postalCodePrefixes": [ "22" ]
      },
      {
        "code": "23",
        "name": "Jaén",
        "stateCode": "01",
        "postalCodePrefixes": [ "23" ]
      },
      {
        "code": "24",
        "name": "Lleó",
        "stateCode": "07",
        "postalCodePrefixes": [ "24" ]
      },
      {
        "code": "25",
        "name": "Lleida",
        "stateCode": "09",
        "postalCodePrefixes": [ "25" ]
      },
      {
        "code": "26",
        "name": "La Rioja",
        "stateCode": "17",
        "postalCodePrefixes": [ "26" ]
      },
      {
        "code": "27",
        "name": "Lugo",
        "stateCode": "12",
        "postalCodePrefixes": [ "27" ]
      },
      {
        "code": "28",
        "name": "Madrid",
        "stateCode": "13",
        "postalCodePrefixes": [ "28" ]
      },
      {
        "code": "29",
        "name": "Màlaga",
        "stateCode": "01",
        "postalCodePrefixes": [ "29" ]
      },
      {
        "code": "30",
        "name": "Múrcia",
        "stateCode": "14",
        "postalCodePrefixes": [ "30" ]
      },
      {
        "code": "31",
        "name": "Navarra",
        "stateCode": "15",
        "postalCodePrefixes": [ "31" ]
      },
      {
        "code": "32",
        "name": "Ourense",
        "stateCode": "12",
        "postalCodePrefixes": [ "32" ]
      },
      {
        "code": "33",
        "name": "Astúries",
        "stateCode": "03",
        "postalCodePrefixes": [ "33" ]
      },
      {
        "code": "34",
        "name": "Palència",
        "stateCode": "07",
        "postalCodePrefixes": [ "34" ]
      },
      {
        "code": "35",
        "name": "Las Palmas",
        "stateCode": "05",
        "postalCodePrefixes": [ "35" ]
      },
      {
        "code": "36",
        "name": "Pontevedra",
        "stateCode": "12",
        "postalCodePrefixes": [ "36" ]
      },
      {
        "code": "37",
        "name": "Salamanca",
        "stateCode": "07",
        "postalCodePrefixes": [ "37" ]
      },
      {
        "code": "38",
        "name": "Santa Cruz de Tenerife",
        "stateCode": "05",
        "postalCodePrefixes": [ "38" ]
      },
      {
        "code": "39",
        "name": "Cantàbria",
        "stateCode": "06",
        "postalCodePrefixes": [ "39" ]
      },
      {
        "code": "40",
        "name": "Segòvia",
        "stateCode": "07",
        "postalCodePrefixes": [ "40" ]
      },
      {
        "code": "41",
        "name": "Sevilla",
        "stateCode": "01",
        "postalCodePrefixes": [ "41" ]
      },
      {
        "code": "42",
        "name": "Sòria",
        "stateCode": "07",
        "postalCodePrefixes": [ "42" ]
      },
      {
        "code": "43",
        "name": "Tarragona",
        "stateCode": "09",
        "postalCodePrefixes": [ "43" ]
      },
      {
        "code": "44",
        "name": "Terol",
        "stateCode": "02",
        "postalCodePrefixes": [ "44" ]
      },
      {
        "code": "45",
        "name": "Toledo",
        "stateCode": "08",
        "postalCodePrefixes": [ "45" ]
      },
      {
        "code": "46",
        "name": "València",
        "stateCode": "10",
        "postalCodePrefixes": [ "46" ]
      },
      {
        "code": "47",
        "name": "Valladolid",
        "stateCode": "07",
        "postalCodePrefixes": [ "47" ]
      },
      {
        "code": "48",
        "name": "Biscaia",
        "stateCode": "16",
        "postalCodePrefixes": [ "48" ]
      },
      {
        "code": "49",
        "name": "Zamora",
        "stateCode": "07",
        "postalCodePrefixes": [ "49" ]
      },
      {
        "code": "50",
        "name": "Saragossa",
        "stateCode": "02",
        "postalCodePrefixes": [ "50" ]
      },
      {
        "code": "51",
        "name": "Ceuta",
        "stateCode": "18",
        "postalCodePrefixes": [ "51" ]
      },
      {
        "code": "52",
        "name": "Melilla",
        "stateCode": "19",
        "postalCodePrefixes": [ "52" ]
      },
      {
        "code": "98",
        "name": "No consta",
        "stateCode": "98",
        "postalCodePrefixes": [ "98" ]
      },
      {
        "code": "99",
        "name": "Altres/Diversos",
        "stateCode": "99",
        "postalCodePrefixes": [ "99" ]
      }
    ]

  },
  "OrderReactorSettings": {
    "CountryCode": "IT",
    "PFsGetOrderDocumentOrderUrlFormat": "http://localhost:60750/itmsdocuments/api/v1/GetOrderDoc?orderId={orderIdentifier}&floristId={floristIdentifier}&type={type}",
    "SEUSetExecutingFloristInvoiceUrl": true
  },
  "SequenceGeneratorGetNextEndpoint": {
    //"Authentication": {
    //  "AuthMethod": "BASIC_KEY",
    //  "Token": "aW50ZXJmbG9yYTI6UjNkUjBzMw==", // DEV "interflora2" / "R3dR0s3"
    //  "UseExpirationTime": false
    //},
    "Url": "http://devoctopus.interflora.it:60744/itsequencegenerator/api/v1",
    "HttpTimeoutInSeconds": 700,
    "PolicyTimeoutInSeconds": 250,
    "HandlerLifetime": 5,
    "DefaultConnectionLimit": 10
  },
  "MongoDb": {
    "DatabaseNameSubset": "it-florist",
    "ConnectionString": "mongodb://mongodb:27017"
  },
  "FeatureManagement": {
    "ShutdownOnException": false
  },
  "SlackAlert": {
    "ApiToken": "********************************************************",
    "DefaultChannel": "alerts-ms-fr-debug"
  }
}
