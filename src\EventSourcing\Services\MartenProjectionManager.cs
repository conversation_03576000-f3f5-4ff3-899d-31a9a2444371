﻿using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using Marten.Events;
using Marten.Linq;
using Marten;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using ITF.SharedLibraries.EventSourcing.Marten;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;

namespace ITF.SharedLibraries.EventSourcing.Services
{
    public class MartenProjectionManager : ISubscriptionManager
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IOptionsMonitor<Configuration> _settings;
        private readonly IMartenCheckpointStore _checkpointStore;
        private readonly ILogger<MartenProjectionManager> _logger;
        private readonly IEnumerable<IProjection> _projections;

        public MartenProjectionManager(
            IServiceScopeFactory serviceScopeFactory,
            IOptionsMonitor<Configuration> settings,
            IMartenCheckpointStore checkpointStore,
            ILogger<MartenProjectionManager> logger,
            IEnumerable<IProjection> projections
            )
        {
            _serviceScopeFactory = serviceScopeFactory;
            _settings = settings;
            _checkpointStore = checkpointStore;
            _logger = logger;
            _projections = projections;
        }
        public async Task StartProjections(CancellationToken cancellationToken)
        {
            var pollingInMs = _settings.CurrentValue.Projection.PollingInMs;
            var checkpoint = await _checkpointStore.GetCheckpoint();

            while (!cancellationToken.IsCancellationRequested)
            {
                using var scope = _serviceScopeFactory.CreateScope();
                using var documentSession = scope.ServiceProvider.GetRequiredService<IDocumentSession>();

                var events = documentSession.Events.QueryAllRawEvents();

                if (!string.IsNullOrWhiteSpace(_settings.CurrentValue.Projection.ProjectionStreamPrefix))
                    events = events.Where(x => x.StreamId.ToString().StartsWith(_settings.CurrentValue.Projection.ProjectionStreamPrefix)) as IMartenQueryable<IEvent>;

                events = events.Where(x => x.Sequence > checkpoint) as IMartenQueryable<IEvent>;

                var results = await events
                    .Where(x => x.Sequence > checkpoint)
                    .OrderBy(x => x.Sequence)
                    .Take(_settings.CurrentValue.Projection.NumberOfRetrievedEvents)
                    .ToListAsync(token: cancellationToken);

                if (results.Any())
                {
                    foreach (var @event in results)
                    {
                        var eventData = JsonSerializer.Deserialize(
                            JsonSerializer.Serialize(@event.Data),
                            @event.EventType // Should handle the case of the type is emtpy => remove the Lib version from assembly name
                        );

                        await Task.WhenAll(_projections.Select(x => x.Project(eventData, @event.Id.ToString())));
                        checkpoint = @event.Sequence;
                        await _checkpointStore.StoreCheckpoint(checkpoint);
                    }
                }

                await Task.Delay(pollingInMs, cancellationToken);
            }
        }
    }
}
