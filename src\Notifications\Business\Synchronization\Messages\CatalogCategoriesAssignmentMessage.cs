﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.Synchronization.Payloads;
using System.Linq;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CatalogCategoriesAssignmentMessage : BaseMessage<CatalogCategoriesAssignmentPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.CatalogCategoriesAssignments?.FirstOrDefault()?.ProductNumber;
            }
        }
    }
}
