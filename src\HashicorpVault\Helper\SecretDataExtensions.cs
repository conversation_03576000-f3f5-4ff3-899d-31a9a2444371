﻿using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using VaultSharp;
using VaultSharp.V1.Commons;

namespace ITF.SharedLibraries.HashicorpVault.Helper
{
    public static class SecretDataExtensions
    {
        public const string FeatureManagement = "FeatureManagement";
        public const string Logging = "Logging";
        
        public static string GetSecretValue(this Secret<SecretData> secret)
        {
            if (secret.Data.Data.Values.Count == 1 && !(secret.Data.Data.Values.ElementAt(0).ToString().Contains("[") || secret.Data.Data.Values.ElementAt(0).ToString().Contains("]")))
                return secret.Data.Data.Values.FirstOrDefault() as string;

            return JsonConvert.SerializeObject(secret.Data.Data);
        }

        public static Dictionary<string, string> GetAllSecrets(this Secret<ListInfo> listInfo, IVaultClient vaultClient, Configuration configuration, HostBuilderContext hostBuilderContext)
        {
           //var result = new List<KeyValuePair<string, string>>();
            var result = new Dictionary<string, string>();
            var paths = listInfo.Data;

            // Iterate over all keys
            paths.Keys.ToList().ForEach(k =>
            {
                // Read the coresponding secret
                var res = vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync($"{configuration.Path}/{k}", mountPoint: configuration.MountPoint).Result;

                // FeatureManagement available ?
                if (k.ToLower() == FeatureManagement.ToLower())
                {
                    dynamic features = JsonConvert.DeserializeObject<dynamic>(res.GetSecretValue());
                    foreach (JProperty property in features)
                    {
                        result.Add($"{FeatureManagement}:{property.Name}", property.Value.ToString());
                    }
                }
                // Log Level available ?
                if (k.ToLower() == Logging.ToLower())
                {
                    dynamic features = JsonConvert.DeserializeObject<dynamic>(res.GetSecretValue());
                    foreach (JProperty property in features)
                    {
                        result.Add($"{Logging}:LogLevel:{property.Name}", property.Value.ToString());
                    }
                }
                else
                {
                    var str = res.GetSecretValue();

                    if (str.IsValidJson())
                    {
                        // This object is from Hashicorp Vault
                        dynamic config = JsonConvert.DeserializeObject<dynamic>(str);
                        config.Version = res.Data.Metadata.Version;

                        // This object is from the current Configuration pipeline
                        dynamic currentSection = hostBuilderContext.Configuration.GetSection(k);
                        
                        // The key does not exist yet in the pipeline
                        if (currentSection.Value is null)
                            result.Add(k, JsonConvert.SerializeObject(config));
                        else
                        {
                            // Get the object dynamically
                            dynamic currentConfig = JsonConvert.DeserializeObject<dynamic>(currentSection.Value);

                            // The key already exists, in the case the versions are different we change it
                            if (config?.Version != currentConfig?.Version)
                                result.Add(k, JsonConvert.SerializeObject(config));        
                        }
                    }
                    else
                    {
                        // This object is from Hashicorp Vault
                        dynamic config = new ExpandoObject();
                        AddProperty(config, k, str);
                        AddProperty(config, "Version", res.Data.Metadata.Version);

                        // This object is from the current Configuration pipeline
                        dynamic currentSection = hostBuilderContext.Configuration.GetSection(k);
                        
                        // The key does not exist yet in the pipeline
                        if(currentSection.Value is null)
                            result.Add(k, JsonConvert.SerializeObject(config));                       
                        else
                        {
                            // Get the object dynamically
                            dynamic currentConfig = JsonConvert.DeserializeObject<dynamic>(currentSection.Value);

                            // The key already exists, in the case the versions are different we change it
                            if (config?.Version != currentConfig?.Version.Value)
                                result.Add(k, JsonConvert.SerializeObject(config));                            
                        }
                    }
                }
            });

            return result;
        }


        private static void AddProperty(ExpandoObject expando, string propertyName, object propertyValue)
        {
            // ExpandoObject supports IDictionary so we can extend it like this
            var expandoDict = expando as IDictionary<string, object>;
            if (expandoDict.ContainsKey(propertyName))
                expandoDict[propertyName] = propertyValue;
            else
                expandoDict.Add(propertyName, propertyValue);
        }
    }
}
