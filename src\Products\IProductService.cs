﻿using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ProductSelections;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Products
{
    public interface IProductService
    {
        Task<IProduct> GetBy<PERSON><PERSON>(string key);
        Task<IProductProjection> GetByLegacyProductIdentifier(string identifier, List<string> expansions = null);
        Task<IProduct> GetByBundleVariantsCustomObjectId(string coId, List<string> expansions = null);
        Task<IProduct> GetByKey(string key, List<string> expansions);
        Task<IProduct> GetById(string id, List<string> expansions);
        Task<IProductProjection> GetProjectionById(string identifier, List<string> expansions = null);
        Task<IProductProjection> GetProjectionByKey(string key, List<string> expansions = null);
        Task<IProduct> GetProductWithCategoriesById(string id);
        Task<IProductProjection> GetWithCategoriesById(string id);
        Task<IProduct?> CreateProduct(IProductDraft productDraft);
        Task<IProduct> UpdateProduct(IProduct existingProduct, List<IProductUpdateAction> updateActions);
        Task<IProduct> UpdateProduct(IProductProjection existingProduct, List<IProductUpdateAction> updateActions);
        Task<IProductSelection> AddProductToProductSelection(List<IProduct> products, IProductSelection productSelection);
        Task<IProductSelection> GetProductSelectionByKey(string productSelectionKey);
        IProductSelection GetProductSelectionSyncByKey(string productSelectionKey);
        Task<IProduct> AddProductToCategory(Product product, string categoryId, bool publish);
        Task<IProduct> AddProductToCategory(ProductProjection product, string categoryId, bool publish);
        Task<IProduct> RemoveProductFromCategory(Product product, string categoryId, bool publish);
        Task<IList<IProduct>> GetAllByCategoryId(string categoryId);

        IProductVariant? GetVariantProductByFreePrice(IProduct product, string location, decimal price);
    }
}
