﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist
{
    public class FloristOpeningHoursDefinedPayload : IPayload
    {
        public string FloristId { get; set; }
        public int DayOfWeek { get; set; }
        public bool IsOpen { get; set; }
        public int OpenHour { get; set; }
        public int CloseHour { get; set; }
        public bool IsBreakTimePresent { get; set; }
        public int BreakTimeHour { get; set; }
        public int BreakTimeEndHour { get; set; }

        public string ModifiedBy { get; set; }
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
