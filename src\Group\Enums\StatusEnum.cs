﻿using System;

namespace ITF.SharedModels.Group.Enums
{
    public enum StatusEnum
    {
        NEW_ORDER,
        ASSIGNED,
        ACCEPTED,
        REFUSED,
        ASSIGNATION_NOT_POSSIBLE,
        NOT_PRINTED,
        PRINTED,
        IN_PROGRESS,
        IN_DELIVERY,
        INTERNAL_COURIER,
        EXTERNAL_COURIER,
        DELIVERED,
        ABSENT,
        CANCELLED,
        SENT
    }

    public static class StatusHelper
    {
        public static string GetStringValue(StatusEnum value)
        {
            var stringValue = Enum.GetName(typeof(StatusEnum), value);
            return stringValue;
        }
    }
}
