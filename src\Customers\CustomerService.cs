﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Serialization;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace IT.SharedLibraries.CT.Customers;

public interface ICustomerService
{
    public Task<ICustomer?> GetByEmail(string email);
    public Task<ICustomer?> CreateCustomer(ICustomerDraft cust);
    public Task<ICustomer?> UpdateCustomer(ICustomer customer, List<ICustomerUpdateAction> actions);
}
public class CustomerService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CustomerService> logger , SerializerService serializerService) : ICustomerService
{
    private readonly string? _projectKey = configuration?.GetSection("Client:ProjectKey").Value;
    private readonly string? _storeKey = configuration?.GetSection("Client:StoreProjectionKey").Value;

    public async Task<ICustomer?> GetByEmail(string email)
    {
        try
        {
            var cust = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .Get()
                .WithWhere($"email=\"{email}\"")
                .WithExpand("customerGroupAssignments[*].customerGroup,customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();

            if (cust?.Count == 0)
                return null;

            if (!string.IsNullOrWhiteSpace(_storeKey))
                return cust!.Results?.FirstOrDefault(c => c.Stores.Count == 0 || c.Stores.FirstOrDefault(s => s.Key == _storeKey) != null);

            return cust!.Results?.FirstOrDefault();
        }
        catch (BadRequestException ex)
        {
            logger.LogError(ex, "Error while reading customer with email={email}, because of {ex}",email,ex.ToString());
            return null;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while reading customer with email={email} because of {ex}",email,ex);
            return null;
        }
    }

    public async Task<ICustomer?> CreateCustomer(ICustomerDraft cust)
    {
        try
        {
            var customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .Post(cust)
                .WithExpand("customerGroupAssignments[*].customerGroup,customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();
            return customer?.Customer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while creating customer with email={email} because of {ex}", cust.Email, ex);
            return null;
        }
    }

    public async Task<ICustomer?> UpdateCustomer(ICustomer customer , List<ICustomerUpdateAction> actions)
    {
        CustomerUpdate update = null;
        try
        {
            update = new CustomerUpdate
            {
                Version = customer.Version,
                Actions = actions
            };

            var updatedCustomer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .WithId(customer.Id)
                .Post(update)
                .WithExpand("customerGroupAssignments[*].customerGroup,customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();

            return updatedCustomer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while updating customer with email={email} for payload : {payload} because of {ex}", customer.Email, update?.Serialize(Serializer.SerializerType.CommerceTools,serializerService) , ex);
            return null;
        }
    }
}
