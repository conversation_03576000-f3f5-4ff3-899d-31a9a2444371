﻿using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Florist;
using System;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Domain
{
    public interface IFloristOrderPerDayRepository : IMongoRepository<GlobalFloristOrderPerDayModel>
    {
        Task Update(GlobalFloristOrderPerDayModel globalFloristOrderPerDay);
        Task<int> GetCounter(string floristIdentifier, DateTime date);
    }
}
