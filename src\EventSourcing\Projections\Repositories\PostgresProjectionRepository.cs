﻿using ITF.SharedLibraries.Postgres.Repository.EntityFramework;
using System;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Projections.Repositories
{
    public class PostgresProjectionRepository<T> where T : BaseEntity<string>
    {
        protected IEntityFrameworkRepository<T, string> _entityFrameworkRepository;

        public PostgresProjectionRepository(IEntityFrameworkRepository<T, string> entityFrameworkRepository)
        {
            _entityFrameworkRepository = entityFrameworkRepository;
        }

        public virtual Task Insert(T model)
        {
            return _entityFrameworkRepository.Add(model);
        }

        public virtual async Task UpdateAllFiltered(Expression<Func<T, bool>> where, Action<T> update)
        {
            var results = await _entityFrameworkRepository.GetWhere(where);
            foreach (var item in results)
            {
                update(item);
                await _entityFrameworkRepository.Update(item);
            }
        }

        public virtual async Task UpdateById(string id, Action<T> update)
        {
            var result = await _entityFrameworkRepository.GetById(id);
            update(result);
            await _entityFrameworkRepository.Update(result);
        }

        public virtual async Task DeleteById(string id, Action<T> update)
        {
            var result = await _entityFrameworkRepository.GetById(id);
            await _entityFrameworkRepository.Remove(result);
        }
    }
}
