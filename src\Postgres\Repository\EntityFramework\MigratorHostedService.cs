﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Postgres.Repository.EntityFramework
{
    // https://andrewlock.net/running-async-tasks-on-app-startup-in-asp-net-core-part-1/
    // If several IHostedService are in the pipeline, each StartAsync execution will block
    // the next ones, which is exactly what we expect with code migrations
    // So The MigratorService should be called in first position 
    public class MigratorHostedService<TContext> : IHostedService
        where TContext : DbContext
    {
        private readonly IServiceProvider _serviceProvider;
        public MigratorHostedService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            // Create a new scope to retrieve scoped services
            using (var scope = _serviceProvider.CreateScope())
            {
                try
                {
                    // Get the DbContext instance
                    var dbContext = scope.ServiceProvider.GetRequiredService<TContext>();

                    //Do the migration asynchronously
                    await dbContext.Database.EnsureCreatedAsync();
                    await dbContext.Database.MigrateAsync();
                }
                catch (Exception)
                {
                }

            }
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
