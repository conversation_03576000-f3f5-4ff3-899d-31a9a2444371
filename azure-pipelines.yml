# Build and push a Nuget package to a Private Feed 
# N. Rey 23/06/2020

trigger:
- master
- develop

resources:
- repo: self

variables:
  projectName: 'ITF.SharedModels'
  vstsFeed: '$(vstsRestoredFeed)'
  publishVstsFeed: '$(VstsPublishedFeed)'
  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
- template: build_deploy_lib.yml
  parameters:
    vmImageName: $(vmImageName)
    projectName: $(projectName)
    vstsFeed: $(vstsFeed)
    publishVstsFeed: $(publishVstsFeed)