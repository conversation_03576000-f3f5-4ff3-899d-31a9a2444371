﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.Redis.Extensions
{
    public static class RedisExtensions
    {
        public static IServiceCollection UseRedisClient(this IServiceCollection services, IConfiguration config, string varEnv = "Redis")
        {
            // https://stackexchange.github.io/StackExchange.Redis/
            // https://stackoverflow.com/a/64177201/4734707
            // https://codereview.stackexchange.com/a/240660

            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);

            services.AddSingleton<ICacheClient, CacheClient>();
            return services;
        }
    }
}
