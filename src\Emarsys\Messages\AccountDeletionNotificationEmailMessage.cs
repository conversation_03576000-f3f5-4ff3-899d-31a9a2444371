﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class AccountDeletionNotificationEmailMessage : BaseMessage<AccountDeletionNotificationEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.AccountDeletionEmails?.FirstOrDefault()?.Customer.Email;
        }
    }
}

public class AccountDeletionNotificationEmailPayload : IPayload
{
    public List<AccountDeletionEmail> AccountDeletionEmails { get; set; } = new List<AccountDeletionEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
