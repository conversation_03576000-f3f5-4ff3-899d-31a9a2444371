﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveryDateUpdated
    {
        public string OrderIdentifier { get; set; }
        public DateTime Date { get; set; }

        public static implicit operator GlobalOrderDeliveryDateUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryDateUpdatedMessage v)
        {
            return new GlobalOrderDeliveryDateUpdated
            {
                OrderIdentifier = v?.Payload?.OrderIdentifier,
                Date = v.Payload.Date,
            };
        }
    }
}
