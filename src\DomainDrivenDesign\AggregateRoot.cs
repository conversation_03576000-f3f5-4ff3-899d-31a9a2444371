﻿using ITF.Lib.Common.DomainDrivenDesign.Interfaces;
using System.Collections.Generic;
using System.Linq;

namespace ITF.Lib.Common.DomainDrivenDesign
{
    public abstract class AggregateRoot<TId> : BaseClass<string>, IInternalEventHandler
    {
        private long EventVersion { get; set; } = -1;

        protected abstract void When(object @event);

        private readonly List<object> _changes;

        protected AggregateRoot() => _changes = new List<object>();

        protected void Apply(object @event)
        {
            When(@event);
            EnsureValidState();
            _changes.Add(@event);
        }

        public IEnumerable<object> GetChanges() => _changes.AsReadOnly();

        public void Load(IEnumerable<object> history)
        {
            foreach (var e in history)
            {
                When(e);
                EventVersion++;
            }
        }

        public void ClearChanges() => _changes.Clear();
        public long GetEventVersion() => EventVersion;

        protected abstract void EnsureValidState();

        protected void ApplyToEntity(IInternalEventHandler entity, object @event)
            => entity?.Handle(@event);

        void IInternalEventHandler.Handle(object @event) => When(@event);
    }
}
