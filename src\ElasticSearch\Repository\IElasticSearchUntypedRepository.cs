﻿using ITF.Lib.Common.DomainDrivenDesign;
using Nest;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.ElasticSearch.Repository
{
    public interface IElasticSearchUntypedRepository<Z>
    {
        Task<GetResponse<T>> GetById<T, K>(DocumentPath<T> Id) where T : BaseClass<K>, Z;
        Task<IEnumerable<IMultiGetHit<T>>> GetByIds<T, K>(IEnumerable<string> Ids) where T : BaseClass<K>, Z;
        Task<IReadOnlyCollection<T>> Get<T, K>(Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z;
        Task<IReadOnlyCollection<T>> Get<T, K>(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z;
        Task<IReadOnlyCollection<T>> GetAll<T, K>() where T : BaseClass<K>, Z;
        Task<CountResponse> CountAsync<T, K>(Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z;
        Task<bool> AnyAsync<T, K>() where T : BaseClass<K>, Z;
        Task<BulkResponse> Update<T, K>(DocumentPath<T> Id, T newEntity, int? retriesOnConflict = 10) where T : BaseClass<K>, Z;
        Task<BulkResponse> UpdateMany<T, K>(List<T> newEntities, int? retriesOnConflict = 10) where T : BaseClass<K>, Z;
        string GetIndex();
    }
}
