{
  "exclude": [
    "**/bin",
    "**/bower_components",
    "**/jspm_packages",
    "**/node_modules",
    "**/obj",
    "**/platforms"
  ],
  //"Client": {
  //  "ClientId": "TkAUFDTArNkYqP2GFt7UpiA4",
  //  "ClientSecret": "7Yjv9TlF4xvl0OyGbZA63cWbDC3q-0Yy",
  //  "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
  //  "ProjectKey": "myflower-preprod",
  //  "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
  //  "StoreProjectionKey": "ITI", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
  //  "ChannelKey": "interflora.it" // <PERSON><PERSON><PERSON> used to get prices
  //},
  "Client": {
    "ClientId": "LR1T07lIzIexRQy9ojBZ0Ece",
    "ClientSecret": "CU9UpgBGVrFQUqy6RSbnGqK0iduR-6dV",
    "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
    "ProjectKey": "myflower-dev", // replace with your project key
    "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/",
    "StoreProjectionKey": "ITI", // key of the store used to query products see https://mc.europe-west1.gcp.commercetools.com/myflower-dev/settings/project/stores
    "ChannelKey": "interflora.it" // Channle used to get prices
  },
  "CommerceToolCustomSettings": {
    "LocalCountryCode": "IT",
    "LocalCountryChannelKey": "interflora.it",
    "LocalCountryStoreKey": "ITI",
    "CtMoruningProductTypeKey": "mourning",
    "LocalCountryAccessoriesCategoryKey": "ACC",
    "LocalCountryProductsCategoryKey": "category",
    "OutboundOrderShippingMethodKey": "pfs-international-italy",
    "MourningShippingMethodKey": "mourning",
    "PfsShippingMethodKey": "pfs"
  }
}