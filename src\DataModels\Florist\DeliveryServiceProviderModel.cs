﻿using ITF.SharedModels.Group.Enums;
using MongoDB.Bson.Serialization.Attributes;
using ITF.Lib.Common.DomainDrivenDesign;

namespace ITF.SharedModels.DataModels.Florist;

[BsonIgnoreExtraElements]
public class DeliveryServiceProviderModel : BaseProjectedEvents, IEquatable<DeliveryServiceProviderModel>, IEqualityComparer<DeliveryServiceProviderModel>
{
    public string Name { get; set; }
    public bool DisplayPrice { get; set; }
    public double NoticePeriod { get; set; } = new();
    public List<Slot> SlotList { get; set; } = new();
    public DeliverySlotTypeEnum DeliverySlotType { get; set; }
    public string EndPoint { get; set; }
    public bool ShipmentLabel { get; set; }

    public override void SetId() => Id = Name;

    public bool Equals(DeliveryServiceProviderModel parameter)
    {
        return Name == parameter.Name;
    }

    public bool Equals(DeliveryServiceProviderModel x, DeliveryServiceProviderModel y)
    {
        if (x is null && y is null)
            return true;
        if (x is null || y is null)
            return false;

        return x.Equals(y);
    }

    public int GetHashCode(DeliveryServiceProviderModel obj)
    {
        return obj.Name.GetHashCode();
    }
}
public class Slot
{
    public DateTime Start {  get; set; }
    public DateTime End { get; set; }
}



