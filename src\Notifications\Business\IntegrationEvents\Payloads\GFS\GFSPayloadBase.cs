﻿using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS
{
    public abstract class GFSPayloadBase
    {
        public int MessageStatus { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int? FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int? ToUnitMessageID { get; set; }
        public string Operator { get; set; }
        public int Priority { get; set; }
    }
}
