﻿using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.Lib.Common.Notifications.Messages;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;
using commercetools.Sdk.Api.Serialization;
using System.Text;
using Thrift.Protocol.Entities;
using CSharpFunctionalExtensions;

namespace ITF.SharedLibraries.Kafka.Publisher;

public interface IKafkaPublisher
{
    Task PublishAsync<T>(T message, string topicName, string key = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IDistributedTracing;
    void Publish<T>(T message, string topicName, string key = null, Action<DeliveryReport<string, string>> deliveryHandler = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IDistributedTracing;
    int Flush(int timeInSeconds);
    void PublishBatch<T>(IEnumerable<T> messages, string topicName, int batchSize = 1000, Action<T> onErrorAction = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IMessageKey, IDistributedTracing;
}

public class KafkaPublisher : IKafkaPublisher
{
    private readonly ILogger _logger;
    private readonly IProducer<string, string> _producer;
    private readonly IProducer<string, string> _alertingProducer;
    private readonly SerializerService _serializerService;
    private readonly Configuration _configuration;
    private readonly ProducerConfig _kafkaProducerConfig;
    public KafkaPublisher(ILoggerFactory loggerFactory, Configuration config, SerializerService serializerService = null)
    {
        _logger = loggerFactory.CreateLogger<KafkaPublisher>();
        _configuration = config;

        if (string.IsNullOrWhiteSpace(config.Username))
        {
            _kafkaProducerConfig = new ProducerConfig
            {
                BootstrapServers = config.BootStrapServers,
            };
        }
        else
        {
            SaslMechanism saslMechanism = SaslMechanism.Plain;
            SecurityProtocol securityProtocol = SecurityProtocol.SaslSsl;

            Enum.TryParse(config.SaslMechanism, true, out saslMechanism);
            Enum.TryParse(config.SecurityProtocol, true, out securityProtocol);

            _kafkaProducerConfig = new ProducerConfig
            {
                BootstrapServers = config.BootStrapServers,
                SaslUsername = config.Username,
                SaslPassword = config.Password,
                SaslMechanism = saslMechanism,
                SecurityProtocol = securityProtocol
            };
        }

        // by default we reduce the timeout for producer from 5min to 30sec
        _kafkaProducerConfig.MessageTimeoutMs = 30000;

        if (config.LingerMs.HasValue)
            _kafkaProducerConfig.LingerMs = config.LingerMs;

        if (config.QueueBufferingMaxKbytes.HasValue)
            _kafkaProducerConfig.QueueBufferingMaxKbytes = config.QueueBufferingMaxKbytes;

        if (config.BatchSize.HasValue)
            _kafkaProducerConfig.BatchSize = config.BatchSize;

        if (config.CompressionType.HasValue)
            _kafkaProducerConfig.CompressionType = (CompressionType)config.CompressionType;

        if (config.Acks.HasValue)
            _kafkaProducerConfig.Acks = (Acks)config.Acks;

        if (config.MessageSendMaxRetries.HasValue)
            _kafkaProducerConfig.MessageSendMaxRetries = config.MessageSendMaxRetries;

        if (config.EnableIdempotence.HasValue)
            _kafkaProducerConfig.EnableIdempotence = config.EnableIdempotence;

        if (config.RetryBackoffMs.HasValue)
            _kafkaProducerConfig.RetryBackoffMs = config.RetryBackoffMs;

        if (config.MessageTimeoutMs.HasValue)
            _kafkaProducerConfig.MessageTimeoutMs = config.MessageTimeoutMs;

        if (config.MessageTimeoutMs.HasValue)
            _kafkaProducerConfig.MessageTimeoutMs = config.MessageTimeoutMs;

        if (config.MessageMaxBytes.HasValue)
            _kafkaProducerConfig.MessageMaxBytes = config.MessageMaxBytes;


        _producer = new ProducerBuilder<string, string>(_kafkaProducerConfig).Build();
        _kafkaProducerConfig.MessageMaxBytes = 4194304;
        _alertingProducer = new ProducerBuilder<string, string>(_kafkaProducerConfig).Build();
        _serializerService = serializerService;
    }

    private string Serialize(object obj, Serializer.SerializerType serializerType)
        => obj.Serialize(serializerType, _serializerService);

    private static void FeedCorrelationIds<T>(T message) where T : IClrType, IDistributedTracing
    {
        if (string.IsNullOrEmpty(message.DistributedTracingData))
            message.SetDistributedTracingData(GetCorrelationLogsId());

        if (string.IsNullOrEmpty(message.CausationId))
            message.SetCausationId(GetCausationTransactionId());
    }

    public int Flush(int timeInSeconds)
        => _producer.Flush(TimeSpan.FromSeconds(timeInSeconds));

    public async Task PublishAlertMessageAsync(ErrorMessage errorMessage)
    {
        try
        {
            if (errorMessage is null)
            {
                _logger.LogError("The message : {error} to publish is null", nameof(errorMessage));
                return;
            }

            if (_configuration?.UseAlerting == false) // no alterting system if explicitely set to false in the config kafka
                return;

            FeedCorrelationIds(errorMessage);

            // Set the fully assembly path to let the object beeing deserialized as "typed" object
            if (string.IsNullOrWhiteSpace(errorMessage.ClrType))
                (errorMessage as IClrType)?.SetClrType(errorMessage.GetType().AssemblyQualifiedName);

            var result = await _alertingProducer.ProduceAsync("interflora-errors", new Message<string, string>()
            {
                Key = errorMessage?.Key,
                Value = errorMessage?.Serialize(),
                Headers = new Headers
                    {
                        new Header("ClrType", Encoding.ASCII.GetBytes(errorMessage.GetType().AssemblyQualifiedName))
                    }
            });

            if (result.Status == PersistenceStatus.NotPersisted)
                throw new KafkaException(new Error(ErrorCode.BrokerNotAvailable, "Failed to publish the message"));
        }
        catch (KafkaException e)
        {
            _logger.LogError(e, "Error while publishing the message {message} to interflora-errors with Kafka with reason {error}", errorMessage.Serialize(), e.Error.Reason);
            // TODO add here an fallback implementation of slack alerting channel to sent the ErrorMessage when kafka is down

            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while publishing the message {message} to interflora-errors with Kafka", errorMessage.Serialize());
            // TODO add here an fallback implementation of slack alerting channel to sent the ErrorMessage when kafka is down

            throw;
        }
    }

    public void Publish<T>(T message, string topicName, string key = null, Action<DeliveryReport<string, string>> deliveryHandler = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IDistributedTracing
    {
        try
        {
            if (message is null)
            {
                _logger.LogError("The message to publish is null");
                return;
            }

            FeedCorrelationIds(message);

            // Set the fully assembly path to let the object beeing deserialized as "typed" object
            if (string.IsNullOrWhiteSpace(message.ClrType))
                message.SetClrType(message.GetType().AssemblyQualifiedName);

            _producer.Produce(topicName, new Message<string, string>()
            {
                Key = key ??= null,
                Value = Serialize(message, serializerType),
                Headers = new Headers
                    {
                        new Header("ClrType", Encoding.ASCII.GetBytes(message.GetType().AssemblyQualifiedName))
                    }
            },
            deliveryHandler);
        }
        catch (KafkaException e)
        {
            _logger.LogError(e, "Error while publishing the message {message} to {topic} with Kafka with reason {error}", Serialize(message, serializerType), topicName, e.Error.Reason);
            Result.Try(() => PublishAlertMessageAsync(new ErrorMessage
            {
                BoundedContext = "Kafka",
                RelatedKafkaMessage = Serialize(message, serializerType),
                MessageId = Guid.NewGuid().ToString(),
                Severity = "High",
                Message = "KafkaException raised on the Publish Process",
                ErrorType = "KafkaProducerException",
                ExceptionCaptured = e.ToString(),
                RelatedMethod = nameof(Publish),
                RelatedMs = "ITF.SharedLibraries",
                RelateProcess = "KafkaPublisher"
            }).Wait());
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while publishing the message {message} to {topic} with Kafka", Serialize(message, serializerType), topicName);
            Result.Try(() => PublishAlertMessageAsync(new ErrorMessage
            {
                BoundedContext = "Kafka",
                RelatedKafkaMessage = Serialize(message, serializerType),
                MessageId = Guid.NewGuid().ToString(),
                Severity = "High",
                Message = "Exception raised on the Publish Process",
                ErrorType = "KafkaProducerException",
                ExceptionCaptured = ex.ToString(),
                RelatedMethod = nameof(Publish),
                RelatedMs = "ITF.SharedLibraries",
                RelateProcess = "KafkaPublisher"
            }).Wait());
            throw;
        }
    }

    public async Task PublishAsync<T>(T message, string topicName, string key = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IDistributedTracing
    {
        try
        {
            if (message is null)
            {
                _logger.LogError("The message to publish is null");
                return;
            }

            FeedCorrelationIds(message);

            // Set the fully assembly path to let the object beeing deserialized as "typed" object
            if (string.IsNullOrWhiteSpace(message.ClrType))
                message.SetClrType(message.GetType().AssemblyQualifiedName);

            var result = await _producer.ProduceAsync(topicName, new Message<string, string>()
            {
                Key = key ??= null,
                Value = Serialize(message, serializerType),
                Headers = new Headers
                    {
                        new Header("ClrType", Encoding.ASCII.GetBytes(message.GetType().AssemblyQualifiedName))
                    }
            });

            if (result.Status == PersistenceStatus.NotPersisted)
                throw new KafkaException(new Error(ErrorCode.BrokerNotAvailable, "Failed to publish the message"));
        }
        catch (KafkaException e)
        {
            _logger.LogError(e, "Error while publishing the message {message} to {topic} with Kafka with reason {error}", Serialize(message, serializerType), topicName, e.Error.Reason);
            await Result.Try(async () => await PublishAlertMessageAsync(new ErrorMessage
            {
                BoundedContext = "Kafka",
                RelatedKafkaMessage = Serialize(message, serializerType),
                MessageId = Guid.NewGuid().ToString(),
                Severity = "High",
                Message = "KafkaException raised on the Publish Process",
                ErrorType = "KafkaProducerException",
                ExceptionCaptured = e.ToString(),
                RelatedMethod = nameof(Publish),
                RelatedMs = "ITF.SharedLibraries",
                RelateProcess = "KafkaPublisher"
            }));
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while publishing the message {message} to {topic} with Kafka", Serialize(message, serializerType), topicName);
            await Result.Try(async () => await PublishAlertMessageAsync(new ErrorMessage
            {
                BoundedContext = "Kafka",
                RelatedKafkaMessage = Serialize(message, serializerType),
                MessageId = Guid.NewGuid().ToString(),
                Severity = "High",
                Message = "Exception raised on the Publish Process",
                ErrorType = "KafkaProducerException",
                ExceptionCaptured = ex.ToString(),
                RelatedMethod = nameof(Publish),
                RelatedMs = "ITF.SharedLibraries",
                RelateProcess = "KafkaPublisher"
            }));
            throw;
        }
    }

    public void PublishBatch<T>(IEnumerable<T> messages, string topicName, int batchSize = 1000, Action<T> onErrorAction = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IMessageKey, IDistributedTracing
    {
        var tasks = new List<Task>();
        foreach (var batch in messages?.GetBatch(batchSize) ?? new List<List<T>>())
        {
            tasks.Add(Task.Run(() =>
            {
                PublishSubBatch(batch, topicName, onErrorAction, serializerType);
            }));
        }
        Task.WaitAll(tasks.ToArray());
    }

    private void PublishSubBatch<T>(IEnumerable<T> messages, string topicName, Action<T> onErrorAction = null, Serializer.SerializerType serializerType = Serializer.SerializerType.TextJson) where T : IClrType, IMessageKey, IDistributedTracing
    {
        // https://github.com/confluentinc/confluent-kafka-dotnet/issues/843#issuecomment-477362831
        try
        {
            var drCount = new CountdownEvent(messages?.ToList()?.Count ?? 0);
            foreach (var message in messages ?? new List<T>())
            {
                if (message is null)
                {
                    _logger.LogError("The message to publish is null");
                    return;
                }

                FeedCorrelationIds(message);

                // Set the fully assembly path to let the object beeing deserialized as "typed" object
                if (string.IsNullOrWhiteSpace(message.ClrType))
                    message.SetClrType(message.GetType().AssemblyQualifiedName);

                _producer.Produce(topicName, new Message<string, string>()
                {
                    Key = message.GetMessageKey(),
                    Value = Serialize(message, serializerType),
                    Headers = new Headers
                        {
                            new Header("ClrType", Encoding.ASCII.GetBytes(message.GetType().AssemblyQualifiedName))
                        }
                },
                dr =>
                {
                    if (dr.Error.IsError)
                        onErrorAction?.Invoke(message);

                    drCount.Signal();
                });
            }

            drCount.Wait();
        }
        catch (KafkaException e)
        {
            _logger.LogError(e, "Error while publishing the current batch of messages to {topic} with Kafka with reason {error}", topicName, e.Error.Reason);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while publishing the current batch of messages to {topic} with Kafka", topicName);
            throw;
        }
    }
}
