﻿using Newtonsoft.Json;

namespace ITF.SharedModels.Messages.GFS.Product
{
    public class GFSSavedPublishedProductMessage
    {
        [JsonProperty("Saved Published Product")]
        public GFSProductPayload SavedPublishedProduct { get; set; } = new();
    }

    public class GFSProductPayload
    {
        public GFSProductPayload()
        {
            MinPriceImages = [];
            MidPriceImages = [];
            MaxPriceImages = [];
            Availabilities = [];
            PeakPeriods = [];
            ProductCategories = [];

        }

        public int? Id { get; set; }
        public string? CountryCode { get; set; }
        public string? IntercatCode { get; set; }
        public string? InternalName { get; set; }
        public string? Name { get; set; }
        public string? InternalDescription { get; set; }
        public string? ShortDescription { get; set; }
        public string? LongDescription { get; set; }
        public List<GFSMedia> MinPriceImages { get; set; }
        public List<GFSMedia> MidPriceImages { get; set; }
        public List<GFSMedia> MaxPriceImages { get; set; }
        public List<GFSAvailability> Availabilities { get; set; }
        public List<GFSAvailability> PeakPeriods { get; set; }
        public List<int> ProductCategories { get; set; }
        public int? ProductType { get; set; }
        public bool? IsAddOn { get; set; }
        public bool? IsGeneric { get; set; }
        public int? DeliveryDelay { get; set; }
        public DateTime? LastUpdate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? RemovedDate { get; set; }
    }

    public class GFSMedia
    {
        public int? Id { get; set; }
        public string URL { get; set; }
        public int? FileSize { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public DateTime? LastUpdate { get; set; }
    }

    public class GFSAvailability
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsTemplate { get; set; }
        public string? SKUMin { get; set; }
        public string? SKUMid { get; set; }
        public string? SKUMax { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MidPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTime? LastUpdate { get; set; }
    }
}
