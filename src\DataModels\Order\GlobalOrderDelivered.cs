﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDelivered
    {
        public string FloristIdentier { get; set; }
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderDelivered(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredMessage v)
        {
            return new GlobalOrderDelivered
            {
                FloristIdentier = v?.Payload?.FloristIdentifier,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
