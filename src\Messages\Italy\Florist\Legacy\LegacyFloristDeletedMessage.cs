﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Runtime.CompilerServices;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristDeletedMessage : BaseMessage<LegacyFloristDeletedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

               
            }
        }
    }

    public class LegacyFloristDeletedPayload : LegacyPayload, IEquatable<LegacyFloristDeletedPayload>
    {
        public string FloristIdentifier { get; set; }
        public bool Deleted { get; set; }

        public bool Equals(LegacyFloristDeletedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                Deleted == parameter.Deleted
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristDeletedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Deleted
        }.GetHashCode();

        //public static implicit operator LegacyFloristDeletedPayload(Messages.V1.LegacyFloristDeletedMessage message)
        //{
        //    var entity = new LegacyFloristDeletedPayload
        //    {
        //        FloristIdentifier = message.Payload.FloristIdentifier,
        //        Deleted = message.Payload.Deleted,
        //    };
        //    return entity;
        //}
    }
}
