﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class CatalogPayload : IPayload
    {
        public string ProductNumber { get; set; }
        public DateTime LastSynchronized { get; set; }
        public string DataArea { get; set; }
        public string ProductSearchName { get; set; }
        public string DefaultLedgerDimensionDisplayValue { get; set; }
        public string SearchName { get; set; }
        public string ProductType { get; set; }
        public string SalesUnitSymbol { get; set; }
        public string InventoryUnitSymbol { get; set; }
        public string PurchaseUnitSymbol { get; set; }
        public string PurchaseLineDiscountProductGroupCode { get; set; }
        public string ItemNumber { get; set; }
        public string SalesSalesTaxItemGroupCode { get; set; }
        public string FlowersAndFoliage { get; set; }
        public string ProductGroupId { get; set; }
        public string ProductDimensionGroupName { get; set; }
        public string PurchaseSalesTaxItemGroupCode { get; set; }
        public string IncludedContainer { get; set; }
        public string CountryCode { get; set; }
        public string OptionalContainer { get; set; }
        public string Description { get; set; }
        public string MandatoryFlowers { get; set; }
        public string PreparationTime { get; set; }
        public bool IsBundle { get; set; }
        public bool IsAccessory { get; set; }
        public bool IsOfferedAccessory { get; set; }
        public bool IsFrenchReference { get; set; }
        public bool IsForeignReference { get; set; }
        public string SubstitutionFlower { get; set; }
        public string MandatoryColors { get; set; }
        public string ProductDimensions { get; set; }
        public string FeasibilityPeriod { get; set; }
        public bool IsAPCodeNeeded { get; set; }
        public bool ShouldHaveVariants { get; set; }
        public double ExtraConsideration { get; set; }
        public DateTime LastModified { get; set; }
        public List<CatalogTaxValue> CatalogTaxValues { get; set; } = new List<CatalogTaxValue>();
        public List<ProductVariant> Variants { get; set; } = new List<ProductVariant> { };
        public List<CatalogCategoriesAssignment> Categories { get; set; } = new List<CatalogCategoriesAssignment> { };
        public List<CatalogTranslation> Translations { get; set; } = new List<CatalogTranslation> { };
        public List<CatalogBundledItem> BundledItems { get; set; } = new List<CatalogBundledItem> { };
        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
        public List<string> ChangeOrigin { get; set; } = new();
    }

    public class ProductVariant
    {
        public ProductVariant()
        {
            CommercialAgreements = new List<CatalogCommercialAgreement>();
            Translations = new List<CatalogTranslation>();
        }
        public string ProductNumber { get; set; }
        public string Size { get; set; }
        public string Style { get; set; }
        public string Description { get; set; }
        public string Name { get; set; }
        public string VariantNumber { get; set; }
        public string SearchName { get; set; }
        public string Dimensions { get; set; }
        public string FlowersAndFoliage { get; set; }
        public string IncludedContainer { get; set; }
        public DateTime LastModified { get; set; }
        public string OptionalContainer { get; set; }
        public string FullDescription { get; set; }
        public string PreparationTime { get; set; }
        public bool IsFake { get; set; }
        public List<CatalogCommercialAgreement> CommercialAgreements { get; set; }
        public List<CatalogTranslation> Translations { get; set; }

    }
}
