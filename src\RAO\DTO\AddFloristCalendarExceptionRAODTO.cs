﻿namespace ITF.SharedLibraries.RAO.DTO
{
    public class AddFloristCalendarExceptionRAODTO : CalendarException
    {
    }

    public class CalendarException
    {
        public string? CalendarExceptionId { get; set; } = string.Empty;
        public string? FloristId { get; set; } = string.Empty;
        public DateTime? Date { get; set; } = DateTime.MinValue;
        public string? Reason { get; set; } = string.Empty;
        public string? Moment { get; set; } = string.Empty;
        public string? StartTime { get; set; } = string.Empty;
        public string? EndTime { get; set; } = string.Empty;
        public string? Type { get; set; } = string.Empty;
        public bool? GuaranteedDelivery { get; set; } = null;
        public int? MaxDistance { get; set; } = 0;
        public DateTime? ModifiedDate { get; set; } = DateTime.MinValue;
        public string? ModifiedBy { get; set; } = string.Empty;
        public string? LunchBreakStartTime { get; set; } = string.Empty;
        public string? LunchBreakEndTime { get; set; } = string.Empty;
        public List<CalendarExceptionHistory>? CalendarExceptionHistories { get; set; } = new();
    }

    public class CalendarExceptionHistory
    {
        public string? CalendarExceptionHistoryId { get; set; } = string.Empty;
        public string? CalendarExceptionId { get; set; } = string.Empty;
        public string? FloristId { get; set; } = string.Empty;
        public DateTime? Date { get; set; } = DateTime.MinValue;
        public string? Reason { get; set; } = string.Empty;
        public string? Moment { get; set; } = string.Empty;
        public string? StartTime { get; set; } = string.Empty;
        public string? EndTime { get; set; } = string.Empty;
        public string? Type { get; set; } = string.Empty;
        public bool? GuaranteedDelivery { get; set; } = null;
        public int? MaxDistance { get; set; } = 0;
        public DateTime? ModifiedDate { get; set; } = DateTime.MinValue;
        public string? ModifiedBy { get; set; } = string.Empty;
        public string? ModificationType { get; set; } = string.Empty;
        public DateTime? ModificationTypeDate { get; set; } = DateTime.MinValue;
        public string? ModificationTypeBy { get; set; } = string.Empty;
    }
}
