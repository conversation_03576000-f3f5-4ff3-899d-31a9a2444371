﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.Group.Florist.Pfs
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristSpecialDayRemovedMessage : BaseMessage<PfsFloristSpecialDayRemovedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier + "_" + Payload?.From.ToString("yyyyMMdd") + "_" + Payload?.To.ToString("yyyyMMdd") + "_" + Enum.GetName<SpecialDayTypeEnum>(Payload.Type);

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristSpecialDayRemovedMessage() { }


                public PfsFloristSpecialDayRemovedMessage(string floristIdentifier, SpecialDayTypeEnum type, DateTime from, DateTime to)
                {
                    this.Payload = new PfsFloristSpecialDayRemovedPayload { FloristIdentifier = floristIdentifier, Type = type, From = from, To = to };
                }
            }


        }
    }

    public class PfsFloristSpecialDayRemovedPayload : LegacyPayload, IEquatable<PfsFloristSpecialDayRemovedPayload>
    {
        public string FloristIdentifier { get; set; }
        public SpecialDayTypeEnum Type { get; set; }
        public DateTime From { get; set; }
        public DateTime To { get; set; }

        public bool Equals(PfsFloristSpecialDayRemovedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                Type == parameter.Type &&
                From == parameter.From &&
                To == parameter.To
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristSpecialDayRemovedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Type,
            From,
            To
        }.GetHashCode();
    }
}
