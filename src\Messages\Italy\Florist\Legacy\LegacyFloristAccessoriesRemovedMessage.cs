﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Florist;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristAccessoriesRemovedMessage : BaseMessage<LegacyFloristAccessoriesRemovedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => string.Join(',', Payload?.Accessories.Select(c => c.ProductCode));

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyFloristAccessoriesRemovedPayload : LegacyPayload, IEquatable<LegacyFloristAccessoriesRemovedPayload>
    {
        public List<Accessory> Accessories { get; set; }

        public bool Equals(LegacyFloristAccessoriesRemovedPayload parameter)
        {
            return (Accessories == parameter.Accessories);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristAccessoriesRemovedPayload);
        }

        public override int GetHashCode() => new
        {
            Accessories
        }.GetHashCode();
    }

}
