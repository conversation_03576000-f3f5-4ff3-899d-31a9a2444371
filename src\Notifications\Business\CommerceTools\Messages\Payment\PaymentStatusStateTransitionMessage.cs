﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Payment;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Payment
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class PaymentStatusStateTransitionMessage : BaseMessage<PaymentStatusStateTransitionPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.PaymentStatusStateTransitionMessage?.Resource?.Id;
            }
        }
    }
}
