﻿using ITF.Lib.Common.DomainDrivenDesign;
using Nest;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.ElasticSearch.Repository
{
    public interface IElasticSearchRepository<T, K> where T : BaseClass<K>
    {
        Task<GetResponse<T>> GetById(DocumentPath<T> Id);
        Task<IEnumerable<IMultiGetHit<T>>> GetByIds(IEnumerable<string> Ids);
        Task<IReadOnlyCollection<T>> Get(Func<QueryContainerDescriptor<T>, QueryContainer> query);
        Task<IReadOnlyCollection<T>> Get(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query);
        Task<IReadOnlyCollection<T>> Get(int size, Func<SourceFilterDescriptor<T>, ISourceFilter> source, Func<QueryContainerDescriptor<T>, QueryContainer> query);
        Task<IReadOnlyCollection<T>> Get(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query, Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort);
        Task<IReadOnlyCollection<T>> GetAll();
        Task<CountResponse> CountAsync(Func<QueryContainerDescriptor<T>, QueryContainer> query);
        Task<bool> AnyAsync();
        Task<IReadOnlyCollection<T>> Get(int size,
            Func<SourceFilterDescriptor<T>, ISourceFilter> source,
            Func<ScriptFieldsDescriptor, IPromise<IScriptFields>> scripts,
            Func<QueryContainerDescriptor<T>, QueryContainer> query,
            Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort,
            string index = null);

        Task<IReadOnlyCollection<T>> Get(int size, int from, Func<QueryContainerDescriptor<T>, QueryContainer> query);


        Task<(IReadOnlyCollection<H> results, long totalCount)> Get<H>(
            int size,
            int from,
            Func<SourceFilterDescriptor<T>, ISourceFilter> source,
            Func<ScriptFieldsDescriptor, IPromise<IScriptFields>> scripts,
            Func<QueryContainerDescriptor<T>, QueryContainer> query,
            Func<SortDescriptor<T>, IPromise<IList<ISort>>> sort,
            string scriptField,
            string index = null)
            where H : BaseClass<K>, IBaseScriptFields;

        Task<IndexResponse> Add(T entity);
        Task<BulkResponse> AddMany(List<T> entities);
        Task<UpdateResponse<T>> Update(DocumentPath<T> Id, T newEntity);
        Task<BulkResponse> UpdateMany(List<T> newEntities, int? retriesOnConflict = 10);
        Task<DeleteResponse> Remove(DocumentPath<T> Id);
        string GetIndex();
    }
}
