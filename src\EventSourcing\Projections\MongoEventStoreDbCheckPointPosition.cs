﻿using ITF.Lib.Common.DomainDrivenDesign;
using System;

namespace ITF.SharedLibraries.EventSourcing.Projections
{
    public class MongoEventStoreDbCheckPointPosition : BaseClass<string>
    {
        public ulong PreparePosition { get; set; }
        public ulong CommitPosition { get; set; }

        public MongoEventStoreDbCheckPointPosition(string checkPointName, ulong commitPosition, ulong preparePosition)
        {
            Id = checkPointName;
            CommitPosition = commitPosition;
            PreparePosition = preparePosition;
        }
        public MongoEventStoreDbCheckPointPosition()
        {
        }
        public override void SetId()
        {
            throw new NotImplementedException();
        }
    }
}
