﻿using System;

namespace ITF.SharedModels.Events.Domain.Florist
{
    public static class DomainEvents
    {
        public static class V1
        {
            public record FloristEnrolled(string FloristId, string Email, string CreatedBy, DateTime AgreementStartOn, string CountryCode, DateTime CreatedAt);
            public record FloristContactDefined(string FloristId, string Type, string Entry, string ModifiedBy, DateTime LastModified);
            public record FloristContactDeleted(string FloristId, string Type, string ModifiedBy, DateTime LastModified);
            public record FloristIdentifierDefined(string FloristId, string Identifier, string ModifiedBy, DateTime LastModified);
            public record FloristShopLocated(string FloristId, string Name, string Street, string ZipCode, string City, string CountryCode, double Latitude, double Longitude, string ModifiedBy, DateTime LastModified);
            public record FloristSuspended(string FloristId, string Reason, DateTime SuspensionOn, string ModifiedBy, DateTime LastModified);
            public record FloristReinstated(string FloristId, DateTime ReinstateOn, string ModifiedBy, DateTime LastModified);
            public record FloristPersonalCodeDefined(string FloristId, string PersonalCode, string ModifiedBy, DateTime LastModified);
            public record FloristStockAdded(string FloristId, string ProductCode, string ProductName, bool IsInStock, string ModifiedBy, DateTime LastModified);
            public record FloristStockUpdated(string FloristId, string ProductCode, string ProductName, bool IsInStock, string ModifiedBy, DateTime LastModified);
            public record FloristStockDeleted(string FloristId, string ProductCode, string ModifiedBy, DateTime LastModified);
            public record FloristOpeningHoursDefined(string FloristId, int DayOfWeek, bool IsOpen, int OpenHour, int CloseHour, bool IsBreakTimePresent, int BreakTimeHour, int BreakTimeEndHour, string ModifiedBy, DateTime LastModified);
        }
    }
}