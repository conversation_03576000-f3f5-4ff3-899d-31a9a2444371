﻿using System;

namespace ITF.Lib.Common.Availability.Exceptions
{
    public class Exceptions
    {
        public class MandatoryFieldsMissingException : Exception
        {
            public MandatoryFieldsMissingException()
            {
            }
            public MandatoryFieldsMissingException(string message)
                : base(message)
            {
            }

            public MandatoryFieldsMissingException(string message, Exception inner)
                : base(message, inner)
            {
            }
        }
    }
}
