﻿using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Linq;
using System.Reflection;

namespace ITF.SharedLibraries.Swagger
{
    public class ConfigureSwaggerOptions : IConfigureNamedOptions<SwaggerGenOptions>
    {
        private readonly IApiVersionDescriptionProvider provider;
        private readonly ApplicationName _applicationName;

        public ConfigureSwaggerOptions(IApiVersionDescriptionProvider provider, ApplicationName applicationName)
        {
            this.provider = provider;
            _applicationName = applicationName;
        }

        public void Configure(SwaggerGenOptions options)
        {
            // add swagger document for every API version discovered
            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerDoc(description.GroupName, CreateVersionInfo(description));
            }
        }

        public void Configure(string name, SwaggerGenOptions options)
        {
            Configure(options);
        }

        private OpenApiInfo CreateVersionInfo(ApiVersionDescription description)
        {
            var info = new OpenApiInfo()
            {
                Title = $"{_applicationName.Name} - {GetEnvironment()}v{description.ApiVersion}",
                Version = description.ApiVersion.ToString()
            };
            var referencedAssemblies = Assembly.GetEntryAssembly().GetReferencedAssemblies();
            var itfLibs = referencedAssemblies.Where(x => x.Name.StartsWith("ITF.") || x.Name.StartsWith("IT.") || x.Name.StartsWith("ITI.") || 
                                                        x.Name.StartsWith("ITE.") || x.Name.StartsWith("ITP.") || x.Name.StartsWith("ITD.") ||
                                                        x.Name.StartsWith("ITS.") || x.Name.StartsWith("ITI2.") || x.Name.StartsWith("SE.")).Select(x => $"{x.Name} v{x.Version.Major}.{x.Version.Minor}.{x.Version.Build}");
            if (itfLibs.Any())
                info.Description = $"Interflora nuget dependencies:</br>{string.Join("</br>", itfLibs)}";

            if (description.IsDeprecated)
                info.Description = $"{info.Description} *** [WARNING] This API version has been deprecated ***";

            return info;
        }

        private static string GetEnvironment()
        {
            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return env != null ? $"[{env}] " : " ";
        }
    }
}
