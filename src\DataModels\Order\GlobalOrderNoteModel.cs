﻿using ITF.Lib.Common.DomainDrivenDesign;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace ITF.SharedModels.DataModels.Order
{
    [BsonIgnoreExtraElements]
    public class GlobalOrderNoteModel : BaseClass<string>
    {
        public string OrderId { get; set; }
        public string Content { get; set; }
        public string Owner { get; set; }
        public string Category { get; set; }
        public override void SetId()
        {
            throw new NotImplementedException();
        }
    }
}
