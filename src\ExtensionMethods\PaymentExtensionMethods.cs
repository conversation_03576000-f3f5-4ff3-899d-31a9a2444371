﻿using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Payments;
using IT.SharedLibraries.CT.CustomAttributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class PaymentExtensionMethods
    {
        public static string GetProvider(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.PROVIDER))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.PROVIDER].ToString();
            }
            return null;
        }
        public static string GetReservationId(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.RESERVATION_ID))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.RESERVATION_ID].ToString();
            }
            return null;
        }
        public static string GetLabel(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.LABEL))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.LABEL].ToString();
            }
            return null;
        }
        public static string GetTransactionId(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.TRANSACTION_ID))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.TRANSACTION_ID].ToString();
            }
            return null;
        }
        public static string GetProviderResponse(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.PROVIDER_RESPONSE))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.PROVIDER_RESPONSE].ToString();
            }
            return null;
        }
        public static string GetRewardType(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.REWARD_TYPE))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.REWARD_TYPE].ToString();
            }
            return null;
        }
        public static string GetId(this IPayment cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Payment.ID))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.Payment.ID].ToString();
            }
            return null;
        }
    }
}
