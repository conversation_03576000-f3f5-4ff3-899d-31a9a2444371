﻿using EventStore.Client;
using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Elastic.Apm;
using Elastic.Apm.Api;
using System.Threading;
using System.Collections.Generic;
using System.Text.Json;
using ITF.SharedLibraries.EventSourcing.EventStore;

namespace ITF.SharedLibraries.EventSourcing.Projections.Services
{
    public class EventStoreProjectionManager : ISubscriptionManager
    {
        private readonly IEventStoreCheckpointStore _checkpointStore;
        private readonly ILogger<EventStoreProjectionManager> _logger;
        private readonly IHostApplicationLifetime _hostApplicationLifetime;
        private readonly IEnumerable<IProjection> _projections;
        private readonly EventStoreClient _connection;
        private readonly Configuration _configuration;

        public EventStoreProjectionManager(
            EventStoreClient connection,
            Configuration configuration,
            IEventStoreCheckpointStore checkpointStore,
            ILogger<EventStoreProjectionManager> logger,
            IHostApplicationLifetime hostApplicationLifetime,
            IEnumerable<IProjection> projections)
        {
            _connection = connection;
            _configuration = configuration;
            _checkpointStore = checkpointStore;
            _logger = logger;
            _hostApplicationLifetime = hostApplicationLifetime;
            _projections = projections;
        }

        private object TryDeserialize(ResolvedEvent evnt)
        {
            try
            {
                var eventData = JsonSerializer.Deserialize(
                    evnt.Event.Data.Span,
                    Type.GetType(evnt.Event.EventType)!
                );
                return eventData;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Fail to deserialize event with type {type}", evnt.OriginalEvent.EventType);
                return null;
            }
        }

        public async Task StartProjections(CancellationToken cancellationToken)
        {
            var checkpoint = await _checkpointStore.GetCheckpoint();

            SubscriptionFilterOptions filter = null;
            if (!string.IsNullOrWhiteSpace(_configuration.ProjectionStreamPrefix))
                filter = new SubscriptionFilterOptions(StreamFilter.Prefix(_configuration.ProjectionStreamPrefix));

            await _connection.SubscribeToAllAsync(
                start: GetPosition(),
                eventAppeared: async (subscription, evnt, cancellationToken) => {
                    try
                    {
                        var eventData = TryDeserialize(evnt);
                        if (eventData != null)
                        {
                            await Task.WhenAll(_projections.Select(x => x.Project(eventData, evnt.Event.EventId.ToString())));
                            checkpoint = evnt.OriginalPosition.Value;
                            await _checkpointStore.StoreCheckpoint(checkpoint);
                        }
                    }
                    catch (Exception e)
                    {
                        await Agent.Tracer
                            .CaptureTransaction($"ERR_EventstoreDBExceptionHandlingTransaction", ApiConstants.SubtypeHttp, async (t) =>
                            {
                                _logger.LogCritical(e, "Handler stopped ! Failed to synchronize messages from EventstoreDB with exception message : {error}", e.Message);
                                await Task.Delay(5000); // Let log join Elk
                                _hostApplicationLifetime.StopApplication();
                            });
                    }
                },
                subscriptionDropped: (async (subscription, reason, exception) => {

                    await Agent.Tracer
                        .CaptureTransaction($"ERR_EventstoreDBSubscriptionDroppedHandlingTransaction", ApiConstants.SubtypeHttp, async (t) =>
                        {
                            _logger.LogCritical(exception, "Handler stopped ! Subscription {subscription} dropped from EventstoreDB with reason {reason} and with exception message : {error}", subscription.ToString(), reason, exception.Message);
                            await Task.Delay(5000); // Let log join Elk
                            _hostApplicationLifetime.StopApplication();
                        });
                }),
                filterOptions: filter,
                cancellationToken: cancellationToken
            );

            FromAll GetPosition()
            => checkpoint.PreparePosition != 0 || checkpoint.CommitPosition != 0
                ? FromAll.After(checkpoint)
                : FromAll.Start;
        }
    }
}
