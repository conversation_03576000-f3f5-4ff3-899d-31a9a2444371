﻿using commercetools.Sdk.Api.Models.Payments;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Payment.Resource
{
    public class PaymentResourceCreatedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public PaymentReference Payment { get; set; }
        public ResourceCreatedDeliveryPayload ResourceCreated { get; set; }

    }
}
