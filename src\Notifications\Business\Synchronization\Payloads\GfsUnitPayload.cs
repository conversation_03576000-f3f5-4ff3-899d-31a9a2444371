﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class GfsUnitPayload : IPayload
    {
        public int? Id { get; set; }
        public string CountryCode { get; set; }
        public string ClearingCode { get; set; }
        public string Name { get; set; }
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public GfsContact Contact { get; set; }
        public DateTime? LastConnected { get; set; }
        public List<GfsUnitTime> UnitTimes { get; set; }
        public List<GfsUnitTimesSpecial> UnitTimesSpecial { get; set; }
        public DateTime LastUpdate { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class GfsContact
    {
        public int? Id { get; set; }
        public string CountryCode { get; set; }
        public string NameFirst { get; set; }
        public string NameLast { get; set; }
        public string Address { get; set; }
        public string PostCode { get; set; }
        public string City { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
    }

    public class GfsUnitTime
    {
        public int? Id { get; set; }
        public int? UnitId { get; set; }
        public int? DayOfWeek { get; set; }
        public DateTime? OpenTime { get; set; }
        public DateTime? CloseTime { get; set; }
    }

    public class GfsUnitTimesSpecial
    {
        public int? Id { get; set; }
        public int? UnitId { get; set; }
        public bool? Closed { get; set; }
        public string Name { get; set; }
        public DateTime? BeginDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? Static { get; set; }
    }
}
