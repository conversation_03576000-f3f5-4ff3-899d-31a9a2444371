﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.Group.Enums;
using MongoDB.Driver.GeoJsonObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristAddressUpdatedMessage : BaseMessage<LegacyFloristAddressUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                     => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

               
            }
        }
    }
    public class LegacyFloristAddressUpdatedPayload : LegacyPayload, IEquatable<LegacyFloristAddressUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string Street { get; set; }
        public string ZipCode { get; set; }
        public string City { get; set; }
        public string Province { get; set; }
        public string CountryCode { get; set; }
        public LegacyCoordinates Gps { get; set; }

        public bool Equals(LegacyFloristAddressUpdatedPayload parameter)
        {
            return (
                FloristIdentifier  == parameter.FloristIdentifier &&
                Street  == parameter.Street &&
                ZipCode == parameter.ZipCode &&
                City == parameter.City &&
                Province == parameter.Province &&
                CountryCode == parameter.CountryCode &&
                Gps == parameter.Gps
            );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristAddressUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Street,
            ZipCode,
            City,
            Province,
            CountryCode,
            Gps
        }.GetHashCode();
    }

}
