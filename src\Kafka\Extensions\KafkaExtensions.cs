﻿using Confluent.Kafka;
using Confluent.Kafka.Admin;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.Kafka.Publisher;
using ITF.SharedLibraries.Kafka.Subscriber;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using static ITF.SharedLibraries.Kafka.DelegateHandler;
using static ITF.SharedLibraries.ExtensionMethods.Operations;
using System.Collections.Generic;
using ITF.SharedLibraries.Kafka.Health;
using Microsoft.Extensions.Hosting;
using ITF.SharedLibraries.ExtensionMethods;
using Amazon.Runtime.Internal.Transform;

namespace ITF.SharedLibraries.Kafka.Extensions
{
    public static class KafkaExtensions
    {

        public static IServiceCollection AddAlertingKafka(this IServiceCollection services, IConfiguration config, string varEnv = "Kafka")
        {
            var logger = services.BuildServiceProvider().GetRequiredService<ILoggerFactory>().CreateLogger(nameof(AddAlertingKafka));

            var configuration = config.Get<Configuration>(varEnv);

            CheckConfiguration(services, configuration,logger);

            if (configuration?.UseAlerting == false)
                logger.LogWarning("AddAlertingKafka warning : UseAlerting is set to false in the configuration {c}", configuration);

            // TODO : for now i comment this we will need to create the topic interflora-errors manually with the right configuration with the ops team 
            //CreateAlertingTopicIfneeded(services, configuration).Wait();

            services.AddSingleton(configuration);
            services.AddSingleton<IKafkaAlertingPublisher, KafkaAlertingPublisher>();

            return services;
        }

        public static IServiceCollection UseKafkaPublisher(this IServiceCollection services, IConfiguration config, string varEnv = "Kafka")
        {
            var configuration = config.Get<Configuration>(varEnv);

            CheckConfiguration(services, configuration);

            CreateTopicsAsync(services, configuration).Wait();

            services.AddSingleton(configuration);
            services.AddSingleton<IKafkaPublisher, KafkaPublisher>();

            if((configuration?.UseAlerting ?? true) == true)
                AddAlertingKafka(services,config,varEnv);

            return services;
        }

        public static IServiceCollection AddKafkaSubscribersHostedService<TKey, TValue>(this IServiceCollection services, IConfiguration config, string varEnv = "Kafka", params KafkaDelegateHandler[] kafkaActionHandlers)
        {
            var configuration = config.Get<Configuration>(varEnv);

            CheckConfiguration(services, configuration);

            kafkaActionHandlers.ToList().ForEach(h => h.Invoke(services));

            CheckIntegrity(services, configuration);

            if ((configuration?.UseAlerting ?? true) == true)
                AddAlertingKafka(services, config, varEnv);

            return services.AddSingleton(configuration)
                .AddSingleton<IKafkaSubscriber<TKey, TValue>, KafkaSubscriber<TKey, TValue>>()
                .AddSingleton<KafkaMultipleSubscribers<TKey, TValue>>()// Allows to access the background service as singleton and stop it
                .AddHostedService<KafkaMultipleSubscribers<TKey, TValue>>();
        }
           

        public static IServiceCollection AddKafkaSubscribersBackgroundWorker<TKey, TValue>(this IServiceCollection services)
            => services.AddHostedService(provider => provider.GetService<KafkaMultipleSubscribers<TKey, TValue>>());

        public static IServiceCollection UseKafkaHealthChecker(this IServiceCollection services, IConfiguration config, string varEnv = "Kafka")
        {
            var configuration = config.Get<Configuration>(varEnv);

            CheckConfiguration(services, configuration);

            services.AddSingleton(configuration);
            services.AddSingleton<IKafkaHealthChecker, KafkaHealthChecker>();

            return services;
        }

        public static IServiceCollection UseKafkaSubscribers<TKey, TValue>(this IServiceCollection services, IConfiguration config, string varEnv = "Kafka", params KafkaDelegateHandler[] kafkaActionHandlers)
        {
            var configuration = config.Get<Configuration>(varEnv);

            CheckConfiguration(services, configuration);

            kafkaActionHandlers.ToList().ForEach(h => h.Invoke(services));

            CheckIntegrity(services, configuration);

            if ((configuration?.UseAlerting ?? true) == true)
                AddAlertingKafka(services, config, varEnv);

            return services.AddSingleton(configuration)
                .AddSingleton<IKafkaSubscriber<TKey, TValue>, KafkaSubscriber<TKey, TValue>>()
                .AddSingleton<KafkaMultipleSubscribers<TKey, TValue>>();// Allows to access the background service as singleton and stop it
        }

        public static IApplicationBuilder AddKakfaConsumerManagementRoutes(this IApplicationBuilder applicationBuilder, string cancelRouteName = "/admin/CancelBackgroundService") =>
            applicationBuilder
                .Map($"{cancelRouteName}", versionApp =>
                    versionApp.UseMiddleware<CancelMiddleware>());

        public static async Task CheckKafkaHealth(this IApplicationBuilder app)
        {

            var kafkaChecker = app.ApplicationServices.GetService<IKafkaHealthChecker>();
            var appLife = app.ApplicationServices.GetService<IHostApplicationLifetime>();
            var logger = app.ApplicationServices.GetService<ILoggerFactory>()?.CreateLogger(nameof(CheckKafkaHealth));

            if (kafkaChecker != null && appLife != null)
            {
                if(!await kafkaChecker.CanProduceMessages())
                {
                    logger?.LogCritical("Kafka health check failed we cant produce messages, we stop the app now because kafka is required");
                    await Task.Delay(5000); // Let log join Elk
                    appLife.StopApplication();
                }
            }
        }

        private static void CheckConfiguration(IServiceCollection services, Configuration configuration , ILogger log = null)
        {

            var logger = log is null ? services.BuildServiceProvider().GetRequiredService<ILoggerFactory>().CreateLogger(nameof(UseKafkaSubscribers)) : log;

            if(configuration == null)
                throw new Exception("No Configuration have been supplied for Kafka !");

            if (string.IsNullOrWhiteSpace(configuration.BootStrapServers))
                throw new Exception($"No valid Configuration have been supplied for Kafka : field BootStrapServers = {configuration.BootStrapServers}");
        }

        private static void CheckIntegrity(IServiceCollection services, Configuration configuration)
        {
            var logger = services.BuildServiceProvider().GetRequiredService<ILoggerFactory>().CreateLogger(nameof(UseKafkaSubscribers));

            // Get service descriptors
            var allMessageHandlers = services.Where(s => s.ServiceType.FullName == typeof(IMessageHandler).FullName);

            if (!allMessageHandlers.Any())
                throw new Exception("No handlers from Dependency Injection have been supplied for Kafka !");

            if (configuration.SubscriberConfigurations.Count == 0)
                throw new Exception("No subscribers from configuration have been supplied for Kafka !");

            var subscribersNames = configuration.SubscriberConfigurations.Select(s => s.ClassName.ToLower()).OrderBy(s => s).ToList();
            var handlersNames = allMessageHandlers.Select(m => m.ImplementationType.Name.ToLower()).OrderBy(s => s).ToList();

            if (subscribersNames.Distinct().Count() != handlersNames.Count)
                logger.LogWarning("There is not exactly one handler for one subscriber for Kafka ! Handlers list: {handlers} | Subscribers List: {subscribers}", handlersNames.Serialize(), subscribersNames.Serialize());

            if (!subscribersNames.Distinct().SequenceEqual(handlersNames))
                logger.LogWarning("There is at least one classname that doesn't fit between subscribers and handlers for Kafka ! Handlers list: {handlers} | Subscribers List: {subscribers}", handlersNames.Serialize(), subscribersNames.Serialize());
        }

        private static async Task CreateAlertingTopicIfneeded(IServiceCollection services, Configuration configuration, ILogger log = null)
        {
            var logger = log is null ? services.BuildServiceProvider().GetRequiredService<ILoggerFactory>().CreateLogger(nameof(UseKafkaSubscribers)) : log;

            IAdminClient adminClient = null;

            if (string.IsNullOrWhiteSpace(configuration.Username))
            {
                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers }).Build();
            }
            else
            {
                SaslMechanism saslMechanism = SaslMechanism.Plain;
                SecurityProtocol securityProtocol = SecurityProtocol.SaslSsl;

                Enum.TryParse(configuration.SaslMechanism, true, out saslMechanism);
                Enum.TryParse(configuration.SecurityProtocol, true, out securityProtocol);

                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers, SaslUsername = configuration.Username, SaslPassword = configuration.Password, SaslMechanism = saslMechanism, SecurityProtocol = securityProtocol }).Build();
            }

            using (adminClient)
            {

                try
                {
                    try
                    {
                        if (adminClient.GetMetadata(TimeSpan.FromSeconds(30))?.Topics?.Select(tp => tp.Topic)?.ToList()?.Contains("interflora-errors") == true)
                            return;
                    }
                    catch (Exception e)
                    {
                        logger.LogWarning(e, "Error on getting metadata from Kafka");
                    }

                    var topic = new TopicSpecification
                    {
                        Name = "interflora-errors",
                        NumPartitions = 20,
                        Configs = new Dictionary<string, string> {
                            {"retention.ms", "-1" },
                            {"retention.bytes", "-1" },
                            {"max.message.bytes","4194304" }
                        },
                        ReplicationFactor = 1
                    };

                    await adminClient.CreateTopicsAsync(new TopicSpecification[] { topic }, new() { RequestTimeout = TimeSpan.FromSeconds(30) });

                    logger.LogInformation("The topic interflora-errors with 1 replicas and 20 partitions has been successfully created on brokers {brokers}", configuration.BootStrapServers);
                }
                catch (CreateTopicsException e)
                {
                    logger.LogWarning(e, "Unable to create topic {topic} : {error}", e.Results[0].Topic, e.Results[0].Error.Reason);
                }
                catch (KafkaException e)
                {
                    logger.LogCritical(e, "Unable to create topic interflora-errors, global kafka error : {error}", e.Error.Reason);
                    throw;
                }
            };

        }

        private static async Task CreateTopicsAsync(IServiceCollection services, Configuration configuration)
        {
            if (!configuration.TopicsToCreateConfigurations.Any())
                return;

            var logger = services.BuildServiceProvider().GetRequiredService<ILoggerFactory>().CreateLogger(nameof(UseKafkaSubscribers));

            IAdminClient adminClient = null;

            if (string.IsNullOrWhiteSpace(configuration.Username))
            {
                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers }).Build();
            }
            else
            {
                SaslMechanism saslMechanism = SaslMechanism.Plain;
                SecurityProtocol securityProtocol = SecurityProtocol.SaslSsl;

                Enum.TryParse(configuration.SaslMechanism, true, out saslMechanism);
                Enum.TryParse(configuration.SecurityProtocol, true, out securityProtocol);

                adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = configuration.BootStrapServers , SaslUsername = configuration.Username , SaslPassword = configuration.Password , SaslMechanism = saslMechanism , SecurityProtocol = securityProtocol}).Build();
            }
            using (adminClient)
            {
                foreach (var t in configuration.TopicsToCreateConfigurations)
                {
                    try
                    {
                        try
                        {
                            if (adminClient.GetMetadata(TimeSpan.FromSeconds(60))?.Topics?.Select(tp => tp.Topic)?.ToList()?.Contains(t.TopicName) == true)
                                continue;
                        }
                        catch (Exception e)
                        {
                            logger.LogWarning(e, "Error on getting metadata from Kafka");
                        }

                        var topic = new TopicSpecification
                        {
                            Name = t.TopicName,
                            NumPartitions = t.NumberOfPartitions,
                            Configs = new Dictionary<string, string> {
                                {"retention.ms", t.RetentionMs.HasValue ? t.RetentionMs.Value.ToString() : "604800000" }
                            }
                        };

                        if (t.RetentionBytes.HasValue)
                            topic.Configs.Add("retention.bytes", t.RetentionBytes.Value.ToString());

                        if (t.ReplicationFactor.HasValue)
                            topic.ReplicationFactor = t.ReplicationFactor.Value;

                        if (t.MaxMessageBytes.HasValue)
                            topic.Configs.Add("max.message.bytes", t.MaxMessageBytes.Value.ToString());

                        await adminClient.CreateTopicsAsync(new TopicSpecification[] { topic });

                        logger.LogInformation("The topic {topic} with {nbReplicas} replicas and {NbPartitions} partitions has been successfully created on brokers {brokers}", t.TopicName, t.ReplicationFactor, t.NumberOfPartitions, configuration.BootStrapServers);
                    }
                    catch (CreateTopicsException e)
                    {
                        logger.LogWarning(e, "Unable to create topic {topic} : {error}", e.Results[0].Topic, e.Results[0].Error.Reason);
                    }
                    catch (KafkaException e)
                    {
                        logger.LogCritical(e, "Unable to create topic global kafka error : {error}", e.Error.Reason);
                        throw;
                    }
                };
            }

        }

        public static async Task StopSubscribeHandlersAsync(this IServiceProvider serviceProvider)
        {
            var backgroundServices = serviceProvider.GetAllHostedService<KafkaMultipleSubscribers<string, string>>();
            backgroundServices?.ToList().ForEach(async s =>
            {
                await s?.StopAsync(new System.Threading.CancellationToken());
            });

            await Task.CompletedTask;
        }
    }
}
