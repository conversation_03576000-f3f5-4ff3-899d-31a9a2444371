﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.IO;
using System.Linq;

namespace ITF.SharedLibraries.Swagger
{
    public static class SwaggerExtensions
    {
        // https://www.telerik.com/blogs/your-guide-rest-api-versioning-aspnet-core
        // https://referbruv.com/blog/posts/integrating-aspnet-core-api-versions-with-swagger-ui
        // https://salihcantekin.medium.com/what-is-api-versioning-how-to-use-it-and-integrate-with-swagger-4c82e7d62b53
        public static IServiceCollection AddSwagger(this IServiceCollection services, string applicationName)
        {
            services.AddSingleton(new ApplicationName { Name = applicationName });
            // https://www.telerik.com/blogs/your-guide-rest-api-versioning-aspnet-core
            services.AddApiVersioning(options =>
            {
                options.DefaultApiVersion = new ApiVersion(1, 0);
                options.AssumeDefaultVersionWhenUnspecified = true;
                options.ReportApiVersions = true;
                options.ApiVersionReader =
                ApiVersionReader.Combine(
                    new UrlSegmentApiVersionReader());
            });

            services.AddVersionedApiExplorer(setup =>
            {
                setup.GroupNameFormat = "'v'VVV";
                setup.SubstituteApiVersionInUrl = true;
            });

            services = services.AddSwaggerGen(c =>
            {
                c.EnableAnnotations();

                // Set the comments path for the Swagger JSON and UI.
                var xmlFile = $"{applicationName}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);

                if (File.Exists(xmlPath))
                    c.IncludeXmlComments(xmlPath);

                c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());

                c.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme (Example: 'Bearer 12345abcdef')",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = JwtBearerDefaults.AuthenticationScheme
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = JwtBearerDefaults.AuthenticationScheme
                            }
                        },
                        Array.Empty<string>()
                    }
                });

                c.OperationFilter<AuthorizationOperationFilter>();

                c.DocumentFilter<DisabledOperationFilter>();
            });
            //services.AddSwaggerGenNewtonsoftSupport(); // Was used with Newtonsoft as serializer
            services.ConfigureOptions<ConfigureSwaggerOptions>();
            return services;
        }

        public static IApplicationBuilder UseSwaggerEndpoint(this IApplicationBuilder app, IApiVersionDescriptionProvider provider, string prefix = "")
        {
            app.UseSwagger(c =>
            {
                c.RouteTemplate = prefix + "/swagger/{documentName}/swagger.json";
            });
            app.UseSwaggerUI(c =>
            {
                foreach (var description in provider.ApiVersionDescriptions)
                    c.SwaggerEndpoint($"/{prefix}/swagger/{description.GroupName}/swagger.json", $"{description.GroupName.ToUpperInvariant()}");

                c.RoutePrefix = $"{prefix}/swagger";
                c.DisplayOperationId();
                c.DefaultModelRendering(ModelRendering.Model);
                c.DefaultModelsExpandDepth(-1);
                c.DisplayRequestDuration();
                c.DocExpansion(DocExpansion.List);
                c.EnableDeepLinking();
                c.ShowExtensions();
                c.ShowCommonExtensions();
                c.ConfigObject.AdditionalItems.Add("syntaxHighlight", false); // https://stackoverflow.com/a/65700162/4734707
            });
            return app;
        }
    }
}
