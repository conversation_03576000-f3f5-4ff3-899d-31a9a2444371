﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Product;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Product
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class ProductRemovedFromCategoryMessage : BaseMessage<ProductRemovedFromCategoryPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.ProductRemovedFromCategoryMessage?.Resource?.Id;
            }
        }
    }
}
