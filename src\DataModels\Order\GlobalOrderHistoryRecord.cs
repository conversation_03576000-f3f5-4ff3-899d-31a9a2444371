﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.DomainDrivenDesign;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderHistoryRecordBuilder
    {
        private GlobalOrderHistoryRecord _record = new GlobalOrderHistoryRecord();
        public GlobalOrderHistoryRecordBuilder AddLogDate(DateTime datetime)
        {
            _record.LogDate = datetime;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddCommerceToolsID(string commerceToolsID)
        {
            _record.CommerceToolsID = commerceToolsID;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddOrderNumber(string orderNumber)
        {
            _record.OrderNumber = orderNumber;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddLagacyOrderNumber(string legacyOrderNumber)
        {
            _record.LegacyOrderNumber = legacyOrderNumber;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddInitialOrderStatus(string initialOrderStatus)
        {
            _record.InitialOrderStatus = initialOrderStatus;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddOrderAction(string orderAction)
        {
            _record.OrderAction = orderAction;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddExecutingFloristId(string executingFloristId)
        {
            _record.ExecutingFloristId = executingFloristId;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddLog(string log)
        {
            _record.Log = log;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddOrderAmount(decimal amount)
        {
            _record.OrderAmount = amount;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddIsOnTime(bool isOnTime)
        {
            _record.IsOnTime = isOnTime;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddCtOrderPreUpdate(string ctOrderPreUpdate)
        {
            _record.CtOrderPreUpdate = ctOrderPreUpdate;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddRequest(string request)
        {
            _record.Request = request;
            return this;
        }
        public GlobalOrderHistoryRecordBuilder AddMessage(string message)
        {
            _record.Message = message;
            return this;
        }


        public GlobalOrderHistoryRecord Build()
        {
            if (_record.LogDate == DateTime.MinValue)
            {
                _record.LogDate = DateTime.UtcNow;
            }
            _record.SetId();
            return _record;
        }
    }

    [BsonIgnoreExtraElements]
    public class GlobalOrderHistoryRecord : BaseClass<string>
    {
        public override void SetId() => Id = Guid.NewGuid().ToString();

        public DateTime LogDate { get; set; }
        public string CommerceToolsID { get; set; }
        public string OrderNumber { get; set; }
        public string LegacyOrderNumber { get; set; }
        public string InitialOrderStatus { get; set; }
        public string OrderAction { get; set; }
        public string ExecutingFloristId { get; set; }
        public string Log { get; set; }
        public decimal OrderAmount { get; set; }
        public bool? IsOnTime { get; set; }
        public string CtOrderPreUpdate { get; set; }
        public string Request { get; set; }
        public string Message { get; set; }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Group.Order.Messages.V1.OrderNewHistoryRecordMessage v)
        {
            var item = new GlobalOrderHistoryRecord
            {
                LogDate = v?.Payload?.LogDate ?? DateTime.Now,
                CommerceToolsID = v?.Payload?.CommerceToolsID,
                OrderNumber = v?.Payload?.OrderNumber,
                LegacyOrderNumber = v?.Payload?.LegacyOrderNumber,
                InitialOrderStatus = v?.Payload?.InitialOrderStatus,
                OrderAction = v?.Payload?.OrderAction,
                ExecutingFloristId = v?.Payload?.ExecutingFloristId,
                Log = v?.Payload?.Log,
                OrderAmount = v?.Payload?.OrderAmount ?? -1,
                IsOnTime = v?.Payload?.IsOnTime ?? false,
                CtOrderPreUpdate = v?.Payload?.CtOrderPreUpdate,
                Request = v?.Payload?.Request,
                Message = v?.Payload?.Message,
            };
            item.SetId();
            return item;
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ASSIGNED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to ASSIGNED to florist {v?.Payload?.FloristIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCancelledMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("CANCELLED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to CANCELLED");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryTimeUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERY TIME UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated delivery time for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryStatusUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERY STATUS UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated delivery status for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryCostUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERY COST UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated delivery cost for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryDateUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERY DATE UPDATED")
                .AddMessage($"Legacy has updated delivery date for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCardMessageUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("CARD MESSAGE UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated card message for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderNotesUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("NOTE UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated notes for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryAddressUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERY ADDRESS UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated delivery address for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredOnBehalfMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERED ON BEHALF")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to DELIVERED on behalf");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ACCEPTED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to ACCEPTED by florist {v?.Payload?.FloristIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRejectedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("REFUSED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to REFUSED by florist {v?.Payload?.FloristIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveredMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("DELIVERED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to DELIVERED by florist {v?.Payload?.FloristIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignationRemovedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ASSIGNATION REMOVED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has removed the assignation for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedOnBehalfMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ACCEPTED ON BEHALF")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to ACCEPTED on behalf");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRejectedOnBehalfMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("REFUSED ON BEHALF")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to REFUSED on behalf");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderSentMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("SENT")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated order {v?.Payload?.OrderIdentifier} to SENT");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCreatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddLagacyOrderNumber(v?.Payload?.LegacyOrderNumber)
                .AddOrderAction("CREATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has created the order {v?.Payload?.LegacyOrderNumber}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientNameUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("RECIPIENT NAME UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated the recipient name for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientPhoneNumberUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("RECIPIENT PHONE NUMBER UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated the recipient phone number for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientCoordinatesUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("COORDINATES UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated the coordinates for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ITEM UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated the recipient phone number for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemExecutorAmountUpdatedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddCommerceToolsID(v?.Payload?.OrderIdentifier)
                .AddOrderAction("ITEM FLORIST EXECUTOR AMOUNT UPDATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Legacy has updated the florist executor amount for order {v?.Payload?.OrderIdentifier}");
            return builder.Build();
        }

        public static implicit operator GlobalOrderHistoryRecord(Messages.Group.Order.Messages.V1.OrderNumberGeneratedMessage v)
        {
            GlobalOrderHistoryRecordBuilder builder = new GlobalOrderHistoryRecordBuilder();
            builder.AddLogDate(v?.Payload?.EventDate ?? DateTime.Now)
                .AddOrderNumber(v?.Payload?.OrderNumber)
                .AddCommerceToolsID(v?.Payload?.CommerceToolsID)
                .AddOrderAction("ORDER NUMBER GENERATED")
                .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(v))
                .AddMessage($"Order number {v?.Payload?.OrderNumber} generated for order {v?.Payload?.CommerceToolsID}");
            return builder.Build();
        }
    }
}