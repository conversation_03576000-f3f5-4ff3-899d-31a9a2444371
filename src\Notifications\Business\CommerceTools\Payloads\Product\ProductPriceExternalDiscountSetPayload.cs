﻿using commercetools.Sdk.Api.Models.Messages;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Product
{
    public class ProductPriceExternalDiscountSetPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public ProductPriceExternalDiscountSetMessage ProductPriceExternalDiscountSetMessage { get; set; }
    }
}
