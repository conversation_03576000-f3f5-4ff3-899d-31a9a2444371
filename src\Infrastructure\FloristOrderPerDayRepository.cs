﻿using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.Messages.Repo;
using MongoDB.Driver;
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public class FloristOrderPerDayRepository : MongoRepository<GlobalFloristOrderPerDayModel>, IFloristOrderPerDayRepository
    {
        public FloristOrderPerDayRepository(ITF.SharedLibraries.MongoDB.Configuration configuration, IMongoClient mongoClient) : base(mongoClient, configuration.DatabaseNameSubset, RepositoryNames.OrderPerDay)
        {
        }

        public async Task<int> GetCounter(string floristIdentifier, DateTime date)
        {
            Expression<Func<GlobalFloristOrderPerDayModel, bool>> where = c => c.FloristIdentifier == floristIdentifier && c.DeliveryDate.Date.Equals(date.Date);
            var filter = Builders<GlobalFloristOrderPerDayModel>.Filter.Where(where);
            var result = await FilterByAsync(filter);
            return result.Any() ? result.FirstOrDefault().Counter : 0;
        }

        public async Task Update(GlobalFloristOrderPerDayModel globalFloristOrderPerDay)
        {
            var updateDef = Builders<GlobalFloristOrderPerDayModel>.Update
                .Set(o => o.Counter, globalFloristOrderPerDay.Counter)
                .Set(o => o.FloristIdentifier, globalFloristOrderPerDay.FloristIdentifier)
                .Set(o => o.DeliveryDate, globalFloristOrderPerDay.DeliveryDate);

            var filter = Builders<GlobalFloristOrderPerDayModel>.Filter.Eq(x => x.Id, globalFloristOrderPerDay.FloristIdentifier + "_" + globalFloristOrderPerDay.DeliveryDate.ToString("yyyyMMdd"));
            await Collection.UpdateOneAsync(filter, updateDef);
        }

        //public async Task Save(string floristIdentifier, DateTime date, int counter)
        //{
        //    var updateDef = Builders<GlobalFloristOrderPerDayModel>.Update
        //        .Set(o => o.Counter, counter)
        //        .Set(o => o.FloristIdentifier, floristIdentifier)
        //        .Set(o => o.DeliveryDate, date);

        //    var filter = Builders<GlobalFloristOrderPerDayModel>.Filter.Eq(x => x.Id, floristIdentifier + "_" + date.ToString("yyyyMMdd"));
        //    await Collection.UpdateOneAsync(filter, updateDef);
        //}
    }
}
