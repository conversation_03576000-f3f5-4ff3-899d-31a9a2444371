﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy; // used for LegacyPayload common payload class should be moved into a group namespace instead of Italy
using System;

namespace ITF.SharedModels.Messages.France.Order.Legacy;

public static partial class Messages
{
    public static partial class V1
    {
        public class LegacyOrderDocumentGeneratedMessage : BaseMessage<LegacyOrderDocumentGeneratedPayload>, IMessageKey, IDistributedTracing
        {
            public string GetMessageKey()
                => Payload?.Version == 0 ? Payload.OrderId : Payload?.OrderId + "-" + Payload?.Version ;

            public void SetDistributedTracingData(string distributedTracingData)
            {
                DistributedTracingData = distributedTracingData;
            }
        }
    }
}

public sealed class LegacyOrderDocumentGeneratedPayload : LegacyPayload
{
    public string FloristId { get; set; } // the florist id referencing this order and document
    public string OrderId { get; set; } // root order id here
    public int Version { get; set; }
    public string DocumentEntityType { get; set; } // the type of the document generated can be bdc for florist invoice or fac for customer billing invoice for example see all values in DB RAO FileReference
    public string Url { get; set; }
    public string AssignmentState { get; set; } // OrderAssignmentType => ASSIGNED / UPDATED / REJECTED / CANCELED
    public DateTime DeliveryDate { get; set; }
    public DateTime DocumentCreationDate { get; set; }
}