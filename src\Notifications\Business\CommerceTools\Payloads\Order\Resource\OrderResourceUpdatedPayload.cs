﻿using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Order.Resource
{
    public class OrderResourceUpdatedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public IOrder Order { get; set; }
        public ResourceUpdatedDeliveryPayload ResourceUpdated { get; set; }
    }
}
