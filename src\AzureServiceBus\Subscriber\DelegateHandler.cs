﻿using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public class DelegateHandler
    {
        public delegate void AzureServiceBusDelegateHandler(IServiceCollection services);

        public static void AzureServiceBusSupplier<TService, TImplementation>(IServiceCollection services)
            where TService : class, IMessageHandler
            where TImplementation : class, TService
        {
            services.AddSingleton<TService, TImplementation>();
        }
    }
}
