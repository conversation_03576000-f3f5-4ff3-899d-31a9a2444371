﻿namespace ITF.SharedLibraries.EventSourcing.Marten
{
    public class Configuration
    {
        public string ConnectionString { get; set; } = default!;
        public Projection Projection { get; set; }
    }

    public class Projection
    {
        public int PollingInMs { get; set; }
        public int NumberOfRetrievedEvents { get; set; }
        public string ProjectionStreamPrefix { get; set; }
    }
}
