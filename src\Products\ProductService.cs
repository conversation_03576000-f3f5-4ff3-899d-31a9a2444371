﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Client.RequestBuilders.Products;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Categories;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ProductSelections;
using commercetools.Sdk.Api.Serialization;
using ITF.Lib.Common.Availability;
using ITF.SharedModels.Notifications.Business.Synchronization.Payloads;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace IT.SharedLibraries.CT.Products
{
    public class ProductService : IProductService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ProductService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public ProductService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ProductService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration?.GetSection("Client:ProjectKey")?.Value??"";
        }
        public async Task<IProduct> GetByKey(string key)
        {
            IProduct product = null;
            try
            {
                product = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithKey(key)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }


        public async Task<IProductProjection> GetByLegacyProductIdentifier(string identifier, List<string> expansions = null)
        {
            IProductProjection product = null;
            try
            {
                var builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductProjections()
                    .Search()
                    .Get()
                    .WithFilter("variants.attributes.legacy_product_identifier:\"" + identifier + "\"")
                    .WithStaged(false);

                if (expansions != null && expansions.Count > 0)
                    foreach (string expansion in expansions)
                    {
                        builder.WithExpand(expansion);
                    }
                var response = await builder
                    .ExecuteAsync();
                if (response != null && response.Count > 0)
                {
                    product = response.Results.FirstOrDefault();
                }
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product identifier {identifier}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with legacy product identifier {identifier} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product identifier {identifier} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProduct> GetByBundleVariantsCustomObjectId(string coId, List<string> expansions = null)
        {
            IProduct product = null;
            try
            {
                var builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()

                    .Get()
                    .WithWhere("masterData(current(masterVariant(attributes(name=\"bundle_variants\" and value(id=\"" + coId + "\")))))");
                    //.WithFilter("variants.attributes.bundle_variants.id:\"" + coId + "\"")
                    //.WithStaged(false);

                if(expansions != null && expansions.Count > 0)
                    foreach (string expansion in expansions)
                    {
                        builder.WithExpand(expansion);
                    }

                var response = await builder.ExecuteAsync();

                //.ExecuteAsync();
                if (response != null && response.Count > 0)
                {
                    product = response.Results.FirstOrDefault();
                }
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with bundle variants custom object id = {coId}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with bundle variants custom object id = {coId} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with bundle variants custom object id = {coId} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProduct> GetByKey(string key, List<string> expansions)
        {
            IProduct product = null;
            try
            {
                ByProjectKeyProductsKeyByKeyGet builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithKey(key)
                    .Get();

                foreach(string expansion in expansions)
                {
                    builder.WithExpand(expansion);
                }

                product = await builder.ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }
        public async Task<IProduct> GetById(string id, List<string> expansions)
        {
            IProduct product = null;
            try
            {
                var builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithId(id)
                    .Get();

                foreach (string expansion in expansions)
                {
                    builder.WithExpand(expansion);
                }

                product = await builder.ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProductProjection> GetProjectionById(string identifier, List<string> expansions = null)
        {
            IProductProjection product = null;
            try
            {
                var builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductProjections()
                    .WithId(identifier)
                    .Get()
                    .WithStaged(false);

                if (expansions != null && expansions.Count > 0)
                    foreach (string expansion in expansions)
                    {
                        builder.WithExpand(expansion);
                    }
                product = await builder
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product identifier {identifier}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with legacy product identifier {identifier} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product identifier {identifier} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProductProjection> GetProjectionByKey(string key, List<string> expansions = null)
        {
            IProductProjection product = null;
            try
            {
                var builder = _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductProjections()
                    .WithKey(key)
                    .Get()
                    ;//.WithStaged(false);

                if (expansions != null && expansions.Count > 0)
                    foreach (string expansion in expansions)
                    {
                        builder.WithExpand(expansion);
                    }
                product = await builder
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with legacy product key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with legacy product key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProductProjection> GetWithCategoriesById(string id)
        {
            IProductProjection product = null;
            try
            {
                product = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductProjections()
                    .WithId(id)
                    .Get()
                    .WithExpand("categories[*]")
                    .WithStaged(false)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProduct> GetProductWithCategoriesById(string id)
        {
            IProduct product = null;
            try
            {
                product = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithId(id)
                    .Get()
                    .WithExpand("masterData.current.categories[*]")
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with id {id} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public async Task<IProduct?> CreateProduct(IProductDraft productDraft)
        {
            IProduct? product = null;
            try
            {
                product = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .Post(productDraft)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while creating product with key {productDraft.Key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while creating product with key {productDraft.Key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while creating product with key {productDraft.Key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return product;
        }

        public IProductSelection GetProductSelectionSyncByKey(string productSelectionKey)
        {
            var task = Task.Run(async () =>
            {
                var prodSel = await GetProductSelectionByKey(productSelectionKey);

                return prodSel;
            });
            task.Wait();
            return task.Result;
        }

        public async Task<IProductSelection> GetProductSelectionByKey(string productSelectionKey)
        {
            IProductSelection productSelection = null;
            try
            {
                productSelection = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ProductSelections()
                    .WithKey(productSelectionKey)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while getting productSelection  with key {productSelectionKey} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error while getting productSelection  with key {productSelectionKey} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while getting productSelection  with key {productSelectionKey} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return productSelection;
        }

        public async Task<IProductSelection> AddProductToProductSelection(List<IProduct> products, IProductSelection productSelection)
        {
            var actions = new List<IProductSelectionUpdateAction>();
            foreach (var product in products)
            {
                actions.Add(new ProductSelectionAddProductAction
                {
                    Product = new ProductResourceIdentifier
                    {
                        Id = product.Id
                    }
                });
            }
            IProductSelection updatedProductSelection = productSelection;
            if (actions.Count > 0)
            {

                var iterations = SplitList<commercetools.Sdk.Api.Models.ProductSelections.IProductSelectionUpdateAction>(actions, 500);
                foreach (var iteration in iterations)
                {
                    commercetools.Sdk.Api.Models.ProductSelections.ProductSelectionUpdate update = new commercetools.Sdk.Api.Models.ProductSelections.ProductSelectionUpdate()
                    {
                        Version = updatedProductSelection.Version,
                        Actions = iteration
                    };
                    try
                    {
                        updatedProductSelection = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .ProductSelections()
                        .WithKey(productSelection.Key)
                        .Post(update)
                        .ExecuteAsync();
                    }
                    catch (BadRequestException ex)
                    {
                        _logger.LogError(ex, $"Error while retrieving adding products to selection {productSelection.Name} because of {ex.Message} - {ex.StackTrace}");
                        throw;
                    }
                    catch (NotFoundException nfex)
                    {
                        _logger.LogError(nfex, $"Error while retrieving adding products to selection {productSelection.Name} because of {nfex.Message} - {nfex.StackTrace}");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error while retrieving adding products to selection {productSelection.Name} because of {ex.Message} - {ex.StackTrace}");
                        throw;
                    }
                }


            }

            return updatedProductSelection;
        }

        public async Task<IProduct> UpdateProduct(IProduct existingProduct, List<IProductUpdateAction> updateActions)
        {

            IProduct updatedProduct = null;
            if (updateActions.Count > 0)
            {
                ProductUpdate update = new ProductUpdate()
                {
                    Version = existingProduct.Version,
                    Actions = updateActions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .Products()
                        .WithId(existingProduct.Id)
                        .Post(update)
                        .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while updating product {existingProduct.Key} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;

        }
        public async Task<IProduct> UpdateProduct(IProductProjection existingProduct, List<IProductUpdateAction> updateActions)
        {

            IProduct updatedProduct = null;
            if (updateActions.Count > 0)
            {

                ProductUpdate update = new ProductUpdate()
                {
                    Version = existingProduct.Version,
                    Actions = updateActions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .Products()
                        .WithId(existingProduct.Id)
                        .Post(update)
                        .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while updating product {existingProduct.Key} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;

        }

        public async Task<IProduct> AddProductToCategory(Product product, string categoryId, bool publish)
        {
            var actions = new List<IProductUpdateAction>();
            actions.Add(new ProductAddToCategoryAction
            {
                Category = new CategoryResourceIdentifier { Id = categoryId },

            });
            if (publish)
            {
                actions.Add(new ProductPublishAction());
            }

            IProduct updatedProduct = null;
            if (actions.Count > 0)
            {

                ProductUpdate update = new ProductUpdate()
                {
                    Version = product.Version,
                    Actions = actions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithId(product.Id)
                    .Post(update)
                    .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while adding product {product.Key} to category {categoryId} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;
        }

        public async Task<IProduct> AddProductToCategory(ProductProjection product, string categoryId, bool publish)
        {
            var actions = new List<IProductUpdateAction>();
            actions.Add(new ProductAddToCategoryAction
            {
                Category = new CategoryResourceIdentifier { Id = categoryId },

            });
            if (publish)
            {
                actions.Add(new ProductPublishAction());
            }

            IProduct updatedProduct = null;
            if (actions.Count > 0)
            {

                ProductUpdate update = new ProductUpdate()
                {
                    Version = product.Version,
                    Actions = actions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .Products()
                        .WithId(product.Id)
                        .Post(update)
                        .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while adding product {product.Key} to category {categoryId} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;
        }

        public async Task<IProduct> RemoveProductFromCategory(Product product, string categoryId, bool publish)
        {
            var actions = new List<IProductUpdateAction>();
            actions.Add(new ProductRemoveFromCategoryAction
            {
                Category = new CategoryResourceIdentifier { Id = categoryId },

            });
            if (publish)
            {
                actions.Add(new ProductPublishAction());
            }

            IProduct updatedProduct = null;
            if (actions.Count > 0)
            {

                ProductUpdate update = new ProductUpdate()
                {
                    Version = product.Version,
                    Actions = actions
                };
                try
                {
                    updatedProduct = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .WithId(product.Id)
                    .Post(update)
                    .ExecuteAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while adding product {product.Key} to category {categoryId} because of {ex.Message} - {ex.StackTrace}");
                    updatedProduct = null;
                }
            }
            return updatedProduct;
        }

        public static IEnumerable<List<T>> SplitList<T>(List<T> locations, int nSize = 30)
        {
            for (int i = 0; i < locations.Count; i += nSize)
            {
                yield return locations.GetRange(i, Math.Min(nSize, locations.Count - i));
            }
        }

        public async Task<IList<IProduct>> GetAllByCategoryId(string categoryId)
        {
            IList<IProduct> products = null;

            try
            {
                products = (await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Products()
                    .Get()
                    .WithWhere($"masterData(current(categories(id=\"{categoryId}\")))")
                    .ExecuteAsync()).Results;
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {categoryId}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving product with key {categoryId} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving product with key {categoryId} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return products;
        }

        public IProductVariant GetVariantProductByFreePrice(IProduct product, string location, decimal price)
        {
            var result = product.MasterData.Current.Variants.Concat([product.MasterData.Current.MasterVariant])
            .Where(v => v.Key.EndsWith(location)).FirstOrDefault();  // Filter by key and location

            if(result == null)
            {
                result = product.MasterData.Current.MasterVariant;
                _logger.LogError("Unable to find matching variant according to the location {location} for product {product}, select masterVariant instead : {sku}", location, product.Id, result.Key);
            }

            return result;

        }



    }
}
