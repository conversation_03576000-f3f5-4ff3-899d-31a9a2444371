﻿using Azure.Messaging.ServiceBus;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public interface IMessageHandler
    {
        //Task HandleMessage(object data, string topic = null, string subscription = null);
        Task HandleMessage(ProcessMessageEventArgs message, string topic = null, string subscription = null);
    }
}
