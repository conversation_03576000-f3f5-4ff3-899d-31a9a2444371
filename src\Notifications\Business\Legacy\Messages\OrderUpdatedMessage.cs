﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;

namespace ITF.SharedModels.Notifications.Business.Legacy.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class OrderUpdatedMessage : BaseOrderMessage<OrderUpdatedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.OrderId;
            }
        }
    }
}

