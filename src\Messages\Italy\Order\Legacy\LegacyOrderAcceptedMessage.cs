﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderAcceptedMessage : BaseMessage<LegacyOrderAcceptedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier + "-" + Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyOrderAcceptedPayload : LegacyPayload, IEquatable<LegacyOrderAcceptedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrderAcceptedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderAcceptedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            OrderIdentifier
        }.GetHashCode();
    }
}
