﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedLibraries.MongoDB.Repository;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure;

public class OrderReactorIdempotenceAlreadyExistingException(string message) : Exception(message)
{
}

public interface IOrderReactorIdempotenceRepository : IMongoRepository<OrderReactorIdempotence>
{
}

// TODO : see with Cyril/ops to add an TTL index based on the CreatedAt property to autodelete the documents after some days/months see this : https://www.mongodb.com/docs/manual/tutorial/expire-data/#:~:text=%7D%20)-,MongoDB%20will%20automatically%20delete%20documents%20from%20the%20log_events%20collection%20when,at%20the%20specified%20expireAt%20value.
public class OrderReactorIdempotenceRepository(IMongoClient mongoClient) : MongoRepository<OrderReactorIdempotence>(mongoClient, "order_reactor", "idempotence"), IOrderReactorIdempotenceRepository
{
}

public class OrderReactorIdempotence : BaseClass<string>
{
    public OrderReactorIdempotence(string messageId) => Id = messageId; // the Id here from the BaseClass represent the MessageId from the Kafka message

    public override void SetId() => Id = Guid.NewGuid().ToString(); 
}
