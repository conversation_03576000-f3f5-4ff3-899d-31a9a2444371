# azure-pipelines.yml/build_deploy_lib.yml
# <PERSON> 12/11/2020

parameters:
  vmImageName: ''
  projectName: ''
  vstsFeed: ''
  publishVstsFeed: ''
    
stages:
- stage: Build
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/develop')}}:
    displayName: DEV -> Build and push stage
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/develop')
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/master')}}:
    displayName: PROD -> Build and push stage
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
  jobs:         
  - job: Build
    ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/develop')}}:   
        displayName: DEV -> Build and push job
    ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/master')}}:   
        displayName: PROD -> Build and push job
    pool:
      vmImage: ${{ parameters.vmImageName }}
    steps:

    - task: UseDotNet@2
      displayName: Install .Net 8 SDK
      inputs:
        packageType: 'sdk'
        includePreviewVersions: true
        version: '8.0.404'

    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: restore
        projects: '**/*.csproj'
        vstsFeed: '${{ parameters.vstsFeed }}'
            
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        arguments: '--configuration Release'
        projects: '**/*.csproj'

    - script: dotnet test tests/**/*.csproj --logger trx --configuration Release
      displayName: Run tests

    - task: PublishTestResults@2
      condition: succeededOrFailed()
      displayName: Publish tests
      inputs:
        testRunner: VSTest
        testResultsFiles: '**/*.trx' 
        
    - task: DotNetCoreCLI@2
      displayName: Nuget pack
      inputs:
       command: 'custom'
       custom: 'pack'
       arguments: 'src/${{ parameters.projectName }}.csproj --no-build --include-source -c=Release -o $(build.artifactstagingdirectory)'

    - task: DotNetCoreCLI@2
      displayName: Nuget push
      inputs:
       command: 'push'
       packagesToPush: '$(Build.ArtifactStagingDirectory)/**/*.nupkg;!$(Build.ArtifactStagingDirectory)/**/*.symbols.nupkg'
       nuGetFeedType: 'internal'
       publishVstsFeed: '${{ parameters.publishVstsFeed }}'