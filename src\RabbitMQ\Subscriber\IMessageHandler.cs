﻿using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.RabbitMQ.Subscriber
{
    public interface IMessageHandler
    {
        public Subscribers _sub { get; set; }

        public void InitSubscriber(Subscribers sub) { 
            _sub = sub; 
        }

        Task HandleMessageAsync(object sender, BasicDeliverEventArgs e);
        //Task OnConsumerConsumerCancelled(object sender, ConsumerEventArgs e) { return Task.CompletedTask; }
        Task OnConsumerUnregistered(object sender, ConsumerEventArgs e) { return Task.CompletedTask; }
        Task OnConsumerRegistered(object sender, ConsumerEventArgs e) { return Task.CompletedTask; }
        Task OnConsumerShutdown(object sender, ShutdownEventArgs e) { return Task.CompletedTask; }
        Task RabbitMQ_ConnectionShutdown(object sender, ShutdownEventArgs e) { return Task.CompletedTask; }
    }
}
