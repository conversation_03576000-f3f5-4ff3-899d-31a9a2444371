﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderRecipientPhoneNumberUpdatedMessage : BaseMessage<LegacyOrderRecipientPhoneNumberUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                => Payload?.OrderIdentifier + "-" + Payload?.RecipientPhoneNumber + "-" + Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderRecipientPhoneNumberUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderRecipientPhoneNumberUpdatedMessage = new LegacyOrderRecipientPhoneNumberUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderRecipientPhoneNumberUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            RecipientPhoneNumber = payload.Recipient.MainPhone
                        }
                    };


                    return legacyOrderRecipientPhoneNumberUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderRecipientPhoneNumberUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderRecipientPhoneNumberUpdatedPayload>
    {
        public string RecipientPhoneNumber { get; set; }
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrderRecipientPhoneNumberUpdatedPayload parameter)
        {
            return (RecipientPhoneNumber == parameter.RecipientPhoneNumber &&
                OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderRecipientPhoneNumberUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            RecipientPhoneNumber,
            OrderIdentifier
        }.GetHashCode();
    }
}
