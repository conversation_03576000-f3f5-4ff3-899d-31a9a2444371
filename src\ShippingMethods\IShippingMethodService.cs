﻿using commercetools.Sdk.Api.Models.ShippingMethods;
using commercetools.Sdk.Api.Models.Zones;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.ShippingMethods
{
    public interface IShippingMethodService
    {
        Task<IShippingMethodPagedQueryResponse> GetByCartId(string cartId);
        Task<IShippingMethod> GetByKey(string key);
        Task<decimal> GetDeliveryFeeForPfs(bool isMourning);
        Task<decimal> GetInternationalDeliveryFeeForPfs();
        Task<IShippingMethod> UpdateSetFixedShippingRate(string key, Zone zone, decimal newFixedShippingRate, string currencyCode);
        Task<decimal> GetDeliveryFeeForPfs(IShippingMethod shippingMethod, bool isMourning);
    }
}
