﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist
{
    public class FloristEnrolledPayload : IPayload
    {
        public string FloristId { get; set; }
        public string Email { get; set; }
        public DateTime AgreementOn { get; set; }
        public string CountryCode { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
