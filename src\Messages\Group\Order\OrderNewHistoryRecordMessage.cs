﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;
using System;
using static ITF.SharedModels.Messages.Group.Order.Messages.V1;

namespace ITF.SharedModels.Messages.Group.Order
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class OrderNewHistoryRecordMessage : BaseMessage<OrderNewHistoryRecordPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.CommerceToolsID + "_" + Payload?.OrderAction + "_" + Payload?.LogDate.ToString("yyyyMMddhhmmssfff");

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public OrderNewHistoryRecordMessage() { }
            }
        }
    }

    public class OrderNewHistoryRecordPayload : LegacyPayload, IEquatable<OrderNewHistoryRecordPayload>
    {
        public string CommerceToolsID { get; set; }
        public string OrderNumber { get; set; }
        public string LegacyOrderNumber { get; set; }
        public string ExecutingFloristId { get; set; }
        public string OrderAction { get; set; }
        public DateTime LogDate { get; set; }
        public string InitialOrderStatus { get; set; }
        public string Log { get; set; }
        public decimal OrderAmount { get; set; }
        public bool IsOnTime { get; set; }
        public string CtOrderPreUpdate { get; set; }
        public string Request { get; set; }
        public string Message { get; set; }

        public bool Equals(OrderNewHistoryRecordPayload parameter)
        {
            if (parameter == null) return false;
            return (CommerceToolsID == parameter.CommerceToolsID &&
                    OrderNumber == parameter.OrderNumber &&
                    LegacyOrderNumber == parameter.LegacyOrderNumber &&
                    ExecutingFloristId == parameter.ExecutingFloristId &&
                    OrderAction == parameter.OrderAction &&
                    LogDate == parameter.LogDate &&
                    InitialOrderStatus == parameter.InitialOrderStatus &&
                    Log == parameter.Log &&
                    OrderAmount == parameter.OrderAmount &&
                    IsOnTime == parameter.IsOnTime &&
                    CtOrderPreUpdate == parameter.CtOrderPreUpdate &&
                    Request == parameter.Request &&
                    Message == parameter.Message
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as OrderNewHistoryRecordPayload);
        }

        public override int GetHashCode() => new
        {
            CommerceToolsID,
            OrderNumber,
            LegacyOrderNumber,
            ExecutingFloristId,
            OrderAction,
            LogDate,
            InitialOrderStatus,
            Log,
            OrderAmount,
            IsOnTime,
            CtOrderPreUpdate,
            Request,
            Message
        }.GetHashCode();
    }

    public class OrderNewHistoryRecordMessageBuilder
    {
        private OrderNewHistoryRecordMessage _record = new OrderNewHistoryRecordMessage { Payload = new OrderNewHistoryRecordPayload() };
        public OrderNewHistoryRecordMessageBuilder AddLogDate(DateTime datetime)
        {
            _record.Payload.LogDate = datetime;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddCommerceToolsID(string commerceToolsID)
        {
            _record.Payload.CommerceToolsID = commerceToolsID;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddOrderNumber(string orderNumber)
        {
            _record.Payload.OrderNumber = orderNumber;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddLegacyOrderNumber(string legacyOrderNumber)
        {
            _record.Payload.LegacyOrderNumber = legacyOrderNumber;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddInitialOrderStatus(string initialOrderStatus)
        {
            _record.Payload.InitialOrderStatus = initialOrderStatus;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddOrderAction(string orderAction)
        {
            _record.Payload.OrderAction = orderAction;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddExecutingFloristId(string executingFloristId)
        {
            _record.Payload.ExecutingFloristId = executingFloristId;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddLog(string log)
        {
            _record.Payload.Log = log;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddOrderAmount(decimal amount)
        {
            _record.Payload.OrderAmount = amount;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddIsOnTime(bool isOnTime)
        {
            _record.Payload.IsOnTime = isOnTime;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddCtOrderPreUpdate(string ctOrderPreUpdate)
        {
            _record.Payload.CtOrderPreUpdate = ctOrderPreUpdate;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddRequest(string request)
        {
            _record.Payload.Request = request;
            return this;
        }
        public OrderNewHistoryRecordMessageBuilder AddMessage(string message)
        {
            _record.Payload.Message = message;
            return this;
        }


        public OrderNewHistoryRecordMessage Build()
        {
            if (_record.Payload.LogDate == DateTime.MinValue)
            {
                _record.Payload.LogDate = DateTime.UtcNow;
            }
            return _record;
        }
    }
}
