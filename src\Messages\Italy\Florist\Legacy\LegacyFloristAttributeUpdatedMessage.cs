﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristAttributeUpdatedMessage : BaseMessage<LegacyFloristAttributeUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                     => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }
    public class LegacyFloristAttributeUpdatedPayload : LegacyPayload, IEquatable<LegacyFloristAttributeUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }

        public bool Equals(LegacyFloristAttributeUpdatedPayload parameter)
        {
            return (
                FloristIdentifier == parameter.FloristIdentifier &&
                Name == parameter.Name &&
                Value == parameter.Value
            );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristAttributeUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Name,
            Value
        }.GetHashCode();
    }
}
