﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.Synchronization.Payloads;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            
            public class FloristFreshPortalMessage : BaseMessage<FloristFreshPortalPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.FloristId;

                public static implicit operator FloristFreshPortalMessage((FloristHeaderMessage Header, FloristContactsMessage Contacts) src)
                {
                    var entity = new FloristFreshPortalMessage();
                    entity.Payload = new FloristFreshPortalPayload
                    {
                        FloristId = src.Header?.Payload?.FloristId,
                        EventDate = src.Header?.Payload?.EventDate ?? DateTime.UtcNow,
                        EventID = src.Header?.Payload?.EventID,
                        Country = src.Header?.Payload?.Location?.CountryCode,
                        Name = src.Header?.Payload?.Name,
                        Group = src.Header?.Payload?.Group,
                        Address = src.Header?.Payload?.Location?.Street,
                        City = src.Header?.Payload?.Location?.City,
                        PostalCode = src.Header?.Payload?.Location?.ZipCode,
                        AgreementStopDate = src.Header?.Payload?.AgreementStopDate ?? DateTime.UtcNow,
                        Vat_number = src.Header?.Payload?.CompanySiret,
                        Comment = src.Header?.Payload?.Comments,
                        Currency_code = "EUR",
                        FloristContacts = src.Contacts?.Payload?.FloristContacts ?? new()
                    };
                    return entity;

                }
            }
        }
    }
}
