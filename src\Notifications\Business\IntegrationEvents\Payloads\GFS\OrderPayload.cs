﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS
{
    public class OrderPayload : GFSPayloadBase, IPayload
    {
        public DateTime DeliveryDate { get; set; }
        public string SendingUnitOrderNumber { get; set; }
        public int OrderType { get; set; }
        public int OrderState { get; set; }
        public int Occasion { get; set; }
        public string SenderCompany { get; set; }
        public string SenderName { get; set; }
        public string SenderAddress { get; set; }
        public string SenderCity { get; set; }
        public string SenderRegion { get; set; }
        public string SenderPostalCode { get; set; }
        public string SenderCountryCode { get; set; }
        public string SenderPhoneHome { get; set; }
        public string SenderPhoneWork { get; set; }
        public string SenderPhoneMobile { get; set; }
        public string SenderFax { get; set; }
        public string SenderEmail1 { get; set; }
        public string SenderEmail2 { get; set; }
        public string RecipientTitle { get; set; }
        public string RecipientName { get; set; }
        public string RecipientAddress { get; set; }
        public string RecipientCity { get; set; }
        public string RecipientRegion { get; set; }
        public string RecipientPostalCode { get; set; }
        public string RecipientCountryCode { get; set; }
        public string RecipientPhoneHome { get; set; }
        public string RecipientPhoneWork { get; set; }
        public string RecipientPhoneMobile { get; set; }
        public string RecipientFax { get; set; }
        public string RecipientEmail { get; set; }
        public decimal TotalOrderValue { get; set; }
        public string OrderInstructions { get; set; }
        public string SendingMemberCode { get; set; }
        public string SendingLocation { get; set; }
        public string SendingName { get; set; }
        public string SendingMemberOrderNumber { get; set; }
        public string FillingMemberCode { get; set; }
        public string FillingName { get; set; }
        public string FillingLocation { get; set; }
        public string ClearingPeriod { get; set; }
        public List<OrderItem> OrderItems { get; set; }
        public int FloristgateNumber { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class OrderItem
    {
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public string IntercatCode { get; set; }
        public string SecondChoice { get; set; }
        public string CardMessage { get; set; }
        public string Description { get; set; }
    }
}
