﻿using System.Collections.Generic;

namespace ITF.Lib.Common.Availability
{
    public class FloristOutputFlorist
    {
        public string? Id { get; set; }
        public string? ShopName { get; set; }
        public decimal? Distance { get; set; }
        public int? KPI { get; set; }
        public string? PhoneNumber { get; set; } = string.Empty;
    }

    public interface IFloristsOutput
    {
        public List<FloristOutputFlorist> Main { get; set; }
        public List<FloristOutputFlorist> Reserve { get; set; }
        public List<FloristOutputFlorist> BigZone { get; set; }
    }
}
