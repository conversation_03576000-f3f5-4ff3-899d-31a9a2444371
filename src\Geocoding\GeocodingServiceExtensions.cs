﻿using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.HealthCheck;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;

namespace ITF.SharedLibraries.Geocoding
{
    public static class GeocodingServiceExtensions
    {
        public static IServiceCollection AddGoogleMapsGeocodingService(this IServiceCollection services , IConfiguration conf , string varEnv = "GoogleMaps") =>
            services.Configure<GoogleMapsSettings>(conf.GetSection(varEnv))
                    .AddScoped<IGeocodingService, GoogleMapsGeocodingService>();
    }

}
