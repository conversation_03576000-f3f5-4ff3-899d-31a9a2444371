﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Hosting;
using ITF.SharedLibraries.HashicorpVault.ConfigurationProvider;
using Microsoft.AspNetCore.Builder;
using ITF.SharedLibraries.HashicorpVault.Middleware;
using Serilog;
using Microsoft.AspNetCore.Hosting;

namespace ITF.SharedLibraries.HashicorpVault.Extensions
{
    public static class HashicorpVaultExtensions
    {
        public static IServiceCollection AddHashicorpVault(this IServiceCollection services, IConfiguration config)
        {
            var hashicorpConfig = config.Get<Configuration>(HashicorpVaultConfigurationProvider.HashicorpVaultConfig);

            services.AddSingleton(hashicorpConfig);

            // Auto polling
            if (hashicorpConfig.EnablePolling)
                services.AddHostedService<HashicorpVaultBackgroundService>();

            services.AddSingleton<IHashicorpVaultClient, HashicorpVaultClient>();

            return services;
        }

        public static IApplicationBuilder AddHashicorpVaultSynchronizationRoute(this IApplicationBuilder applicationBuilder, string routeName = "/HashicorpVaultSync")
        {
            return applicationBuilder.Map($"{routeName}", versionApp =>
                    versionApp.UseMiddleware<HashicorpVaultMiddleware>());          
        }

        public static IConfigurationBuilder AddHashicorpVaultSecretsManager(          
            this IConfigurationBuilder configurationBuilder,
            HostBuilderContext hostBuilderContext,
            Configuration configuration, 
            bool ReloadOnChange = true, 
            int ReloadDelay = 500)
        {
            try
            {
                return configurationBuilder.Add(new HashicorpVaultConfigurationSource
                {
                    Configuration = configuration,
                    ReloadOnChange = ReloadOnChange,
                    HostBuilderContext = hostBuilderContext,
                    ReloadDelay = ReloadDelay
                });
            }
            catch (Exception ex)
            {
                throw new Exception("Hashicorp Vault configuration failed: " + ex.Message);
            }
        }

        public static IHostBuilder CreateHashicorpHostBuilder(string[] args,
            string roleIdVarEnv = "HASHICORP_VAULT_ROLEID",
            string secretIdVarEnv = "HASHICORP_VAULT_SECRETID",
            string urlVarEnv = "HASHICORP_VAULT_URL",
            string pathVarEnv = "HASHICORP_VAULT_PATH",
            string mountPointVarEnv = "HASHICORP_VAULT_MOUNTPOINT",
            string enablePollingVarEnv = "HASHICORP_VAULT_ENABLE_POLLING",
            string pollingFrequencyVarEnv = "HASHICORP_VAULT_POLLING_FREQUENCY",
            bool reloadOnChange = true,
            int reloadDelay = 500)
        {
            // Get access to environment variables (Key Vault credentials)
            var ConfigEnvVar = new ConfigurationBuilder()
                    .AddEnvironmentVariables()
                    .Build();

            // Return the config builder in the pipeline
            return Host.CreateDefaultBuilder(args)
                .UseDefaultServiceProvider((context, options) => { options.ValidateScopes = true; })
                .UseSerilog()
                .ConfigureAppConfiguration((hostingContext, config) => {
                    config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                    config.AddHashicorpVaultSecretsManager(
                        hostBuilderContext: hostingContext,
                        configuration:
                            new Configuration()
                            {
                                Role_id = ConfigEnvVar[roleIdVarEnv],
                                Secret_id = ConfigEnvVar[secretIdVarEnv],
                                Url = ConfigEnvVar[urlVarEnv],
                                Path = ConfigEnvVar[pathVarEnv],
                                MountPoint = ConfigEnvVar[mountPointVarEnv],
                                EnablePolling = ConfigEnvVar[enablePollingVarEnv].To<bool>(),
                                PollingFrequency = ConfigEnvVar[pollingFrequencyVarEnv].To<int>()
                            },
                        ReloadOnChange: reloadOnChange,
                        ReloadDelay: reloadDelay
                    );
                });
        }

    }
}
