﻿using Confluent.Kafka;
using System;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;

namespace ITF.SharedLibraries.Kafka.Subscriber
{
    public abstract class KafkaBaseMessageHandler : IMessageHandler
    {
        public virtual Task HandleMessage(object data, string topic = null, int? partition = null, long? offset = null)
        {
            throw new NotImplementedException();
        }

        public virtual Task HandleError(Error e)
        {
            var transaction = Elastic.Apm.Agent
                .Tracer.StartTransaction("ERR_KafkaErrorHandling", "Kafka");
            try
            {
                CaptureErrorTransaction(transaction, e.Reason, $"Error in handling Kafka message");
            }
            catch (Exception ex)
            {
                CaptureExceptionTransaction(transaction, ex, $"Exception in error handling Kafka message");
            }
            finally
            {
                transaction.End();
            }
            return Task.CompletedTask;
        }

        public virtual Task HandleExceptions(Exception e)
        {
            var transaction = Elastic.Apm.Agent
                .Tracer.StartTransaction("ERR_KafkaExceptionHandling", "Kafka");
            try
            {
                CaptureExceptionTransaction(transaction, e, $"Exception in handling Kafka message");
            }
            finally
            {
                transaction.End();
            }
            return Task.CompletedTask;
        }
    }
}
