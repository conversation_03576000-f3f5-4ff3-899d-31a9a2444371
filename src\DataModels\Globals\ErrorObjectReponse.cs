﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.DataModels.Globals
{
    public class ErrorObjectReponse
    {
        public string Error { get; set; }
        public string Message { get; set; }
        public ErrorObjectReponse(string error, string message, ILogger logger) 
        { 
            Error = error;
            Message = message;
            logger.LogError("Error {error} - Message: {message}", error, message);
        }
    }
}
