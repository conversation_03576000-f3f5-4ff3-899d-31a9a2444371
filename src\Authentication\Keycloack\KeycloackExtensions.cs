﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Authentication.Keycloack
{
    public static class KeycloackExtensions
    {
        public static IServiceCollection UseKeycloackAuthentication(this IServiceCollection services, IConfiguration config, string varEnv = "Keycloak")
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.AddSingleton(configuration);
            
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = configuration.RequireHttpsMetadata;
                options.Authority = configuration.Authority;
                options.IncludeErrorDetails = configuration.IncludeErrorDetails;
                options.TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateAudience = configuration.ValidateAudience,
                    ValidAudience = configuration.Audience,
                    ValidateIssuerSigningKey = configuration.ValidateIssuerSigningKey,
                    ValidateIssuer = configuration.ValidateIssuer,
                    ValidIssuer = configuration.Issuer,
                    ValidateLifetime = configuration.ValidateLifetime,
                };

                options.Events = new JwtBearerEvents()
                {
                    OnAuthenticationFailed = context =>
                    {
                        context.NoResult();
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        Log.Error(context.Exception, "Failed to authenticate with error {error}", context.Exception?.Message);
                        return Task.CompletedTask;
                    }
                };

                options.Validate();
            });

            return services;
        }

        public static IServiceCollection UseKeycloakBasicAuthentication(this IServiceCollection services, IConfiguration config, string varEnv = "KeycloakBasic")
        {
            var configuration = config.Get<BasicConfiguration>(varEnv);
            services.AddSingleton(configuration);

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(o =>
            {
                o.Authority = configuration.Authority;
                o.MetadataAddress = configuration.MetadataAddress;
                o.RequireHttpsMetadata = configuration.RequireHttpsMetadata;
                o.IncludeErrorDetails = configuration.IncludeErrorDetails;
                o.TokenValidationParameters = new TokenValidationParameters()
                {
                    NameClaimType = ClaimTypes.Name,
                    RoleClaimType = ClaimTypes.Role,
                    ValidateIssuer = configuration.ValidateIssuer,
                    ValidIssuers = new[] { configuration.Issuer },
                    ValidateAudience = configuration.ValidateAudience,
                    ValidAudiences = configuration.Audiences,
                };
            });

            return services;
        }
    }
}
