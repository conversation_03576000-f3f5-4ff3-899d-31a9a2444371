﻿using commercetools.Sdk.Api.Models.Messages;
using commercetools.Sdk.Api.Models.ProductSelections;
using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.ProductSelection;
public static partial class Messages
{
    public static partial class V1
    {
        public class ProductSelectionProductRemovedMessage : BaseMessage<ProductSelectionProductRemovedPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.ProductSelection?.Key ?? "";
        }
    }
}

public class ProductSelectionProductRemovedPayload : IPayload
{
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
    public ProductSelectionProductRemovedMessage ProductSelectionProductRemovedMessage { get; set; }
    public IProductSelection ProductSelection { get; set; }
}
