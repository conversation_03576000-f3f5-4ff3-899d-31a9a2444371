﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace ITF.SharedLibraries.Swagger
{
    public class DisabledOperationFilter : IDocumentFilter
    {
        private bool IsDisabledApi(OpenApiOperation operation)
        {
            return operation.Tags.Any(tag => tag.Name == "DisabledApi");
        }

        private string GetControllerName(string actionPath)
        {
            var lastSlashIdx = actionPath.LastIndexOf('/');
            var controllerPath = actionPath[0..lastSlashIdx];

            var controllerPathLastSlashIdx = controllerPath.LastIndexOf('/');
            var controllerName = controllerPath[(controllerPathLastSlashIdx + 1)..];

            return controllerName;
        }

        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var pathsToRemove = new List<string>();

            foreach (var (key, pathItem) in swaggerDoc.Paths)
            {
                var operationTypesToRemove = new List<OperationType>();
                foreach (var (operationType, operation) in pathItem.Operations)
                {
                    if (IsDisabledApi(operation))
                        operationTypesToRemove.Add(operationType);
                }

                foreach (var operationType in operationTypesToRemove)
                {
                    pathItem.Operations.Remove(operationType);
                }

                // if everything removed from "controller"
                if (operationTypesToRemove.Count != 0 && pathItem.Operations.Count == 0)
                {
                    pathsToRemove.Add(key);
                }
            }

            // remove those "controllers"
            foreach (var pathToRemove in pathsToRemove)
            {
                swaggerDoc.Paths.Remove(pathToRemove);

                var controllerName = GetControllerName(pathToRemove);

                if (swaggerDoc.Paths.All(path => GetControllerName(path.Key) != controllerName))
                {
                    var tag = swaggerDoc.Tags.FirstOrDefault(tag => tag.Name == controllerName);
                    if (tag is not null)
                        swaggerDoc.Tags.Remove(tag);
                }
            }
        }
    }
}
