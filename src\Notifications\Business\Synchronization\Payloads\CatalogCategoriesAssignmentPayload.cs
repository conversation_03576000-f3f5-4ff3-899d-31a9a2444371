﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class CatalogCategoriesAssignmentPayload : IPayload
    {
        public List<CatalogCategoriesAssignment> CatalogCategoriesAssignments { get; set; } = new List<CatalogCategoriesAssignment>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class CatalogCategoriesAssignment
    {
        public string ProductNumber { get; set; }
        public string Name { get; set; }
        public List<string> Categories { get; set; } = new List<string>();
    }
}
