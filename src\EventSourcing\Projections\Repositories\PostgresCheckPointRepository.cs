﻿using EventStore.Client;
using ITF.SharedLibraries.Postgres.Repository.EntityFramework;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Projections.Repositories
{
    public class PostgresCheckPointRepository<T> where T : PostgresCheckPointPosition
    {
        protected readonly string _checkPointName;
        protected readonly IEntityFrameworkRepository<T, string> _checkPointRepository;
        public PostgresCheckPointRepository(IEntityFrameworkRepository<T, string> checkPointRepository, string checkPointName)
        {
            _checkPointRepository = checkPointRepository;
            _checkPointName = checkPointName;
        }

        public async virtual Task<Position> GetCheckpoint()
        {
            var result = await _checkPointRepository.GetById(_checkPointName);

            if (result is null)
            {
                var position = new Position(0, 0);
                await InsertCheckpoint(position);
                return position;
            }

            return new Position(result.CommitPosition, result.PreparePosition);
        }

        protected async virtual Task InsertCheckpoint(Position checkpoint)
        {
            var data = new PostgresCheckPointPosition(_checkPointName, checkpoint.PreparePosition, checkpoint.CommitPosition);
            await _checkPointRepository.Add(data as T);
        }

        public async virtual Task StoreCheckpoint(Position checkpoint)
        {
            var data = new PostgresCheckPointPosition(_checkPointName, checkpoint.PreparePosition, checkpoint.CommitPosition);
            await _checkPointRepository.Update(data as T);
        }
    }
}
