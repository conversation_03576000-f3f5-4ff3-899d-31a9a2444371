﻿using System.Collections.Generic;

namespace ITF.SharedLibraries.Emarsys.Models.Api;

public abstract class TriggerAnExternalEventBaseEmail
{
public int key_id { get; set; } = 3;
public string external_id { get; set; }
public object data { get; set; }
public List<Attachment> attachment { get; set; } = new List<Attachment>();
}

public class Attachment
{
public string filename { get; set; }
public string data { get; set; }
}
