﻿using commercetools.Base.Client;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Payments;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace IT.SharedLibraries.CT.Payments
{
    public class PaymentService : IPaymentService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<PaymentService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public PaymentService(IClient commerceToolsClient, IConfiguration configuration, ILogger<PaymentService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }
        public async Task<IPayment> GetPaymentById(string paymentId)
        {
            IPayment payment = null;
            try
            {
                payment = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Payments()
                    .WithId(paymentId)
                    .Get()
                    .ExecuteAsync();
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Payment with id {paymentId} not found");
                payment = null;
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, $"Failed to get payment with id {paymentId}");
                payment = null;
            }
            return payment;
        }
    }
}
