﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Customer.Resource
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CustomerResourceDeletedMessage : BaseMessage<CustomerResourceDeletedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.ResourceDeleted.Resource.Id;
            }
        }
    }
}
