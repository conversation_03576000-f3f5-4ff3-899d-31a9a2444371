﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderRecipientCoordinatesUpdatedMessage : BaseMessage<LegacyOrderRecipientCoordinatesUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderRecipientCoordinatesUpdatedMessage((string ctOrderId, OrderUpdatedMessage message)src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderRecipientCoordinatesUpdatedMessage = new LegacyOrderRecipientCoordinatesUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderRecipientCoordinatesUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Latitude = payload.Recipient.Latitude,
                            Longitude = payload.Recipient.Longitude
                        }
                    };


                    return legacyOrderRecipientCoordinatesUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderRecipientCoordinatesUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderRecipientCoordinatesUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public bool Equals(LegacyOrderRecipientCoordinatesUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Latitude == parameter.Latitude &&
                Longitude == parameter.Longitude
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderRecipientCoordinatesUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier
        }.GetHashCode();
    }
}
