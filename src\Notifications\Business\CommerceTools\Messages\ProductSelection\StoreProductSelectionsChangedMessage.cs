﻿using commercetools.Sdk.Api.Models.Messages;
using commercetools.Sdk.Api.Models.ProductSelections;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Product;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.ProductSelection;
public static partial class Messages
{
    public static partial class V1
    {
        public class StoreProductSelectionsChangedMessage : BaseMessage<StoreProductSelectionsChangedPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.ProductSelection?.Key ?? "";
        }
    }
}

public class StoreProductSelectionsChangedPayload : IPayload
{
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
    public StoreProductSelectionsChangedMessage StoreProductSelectionsChangedMessage { get; set; }
    public IProductSelection ProductSelection { get; set; }
}
