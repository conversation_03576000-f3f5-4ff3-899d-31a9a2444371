﻿using commercetools.Sdk.Api;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Serialization;
using IT.SharedLibraries.CT.ExtensionMethods;
using ITF.SharedModels.Group.Enums;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace IT.SharedLibraries.CT.UnitTests.Extensions.LineItemsTests
{
    public class LineItemsTests
    {

        private SerializerService GetSerializerService()
        {
            var services = new ServiceCollection();
            services.UseCommercetoolsApiSerialization();
            var serviceProvider = services.BuildServiceProvider();
            var serializerService = serviceProvider.GetService<IApiSerializerService>();
            return (SerializerService)serializerService;
        }
        [Fact]
        public void MapLineItemToRAOLineItem_ReturnsExpectedResult()//Uncomment if you want test with json resourcefile, but recomment if you want avoir pipeline building about lack of embedded resource file
        {
            //string orderSerialized = File.ReadAllText("GetRaoProductTest.json");
            //SerializerService serializerService = GetSerializerService();

            //var li = serializerService.Deserialize<ILineItem>(orderSerialized);
            //var product = li.MapLineItemToRAOLineItem();
            //Assert.Equal("C0172", product.ProductId);
            //Assert.Equal(1, product.Quantity);
            //Assert.Equal(18.00, product.Margin); // 1800 / 100
            //Assert.Equal(67.25, product.Privex); // 6725 / 100
            //Assert.Equal("", product.RibbonText);
            //Assert.Equal(8.5, product.Price); // discounted one
            //Assert.Equal("", product.Label); // No "fr" in Name
            //Assert.Equal("C0172-L", product.IntercatCode);

            Assert.True(true);
        }

        [Fact]
        public void GetLineItem_ReturnsExpectedResult()//Uncomment if you want test with json resourcefile, but recomment if you want avoir pipeline building about lack of embedded resource file
        {
            //string orderSerialized = File.ReadAllText("GetRaoProductTest.json");
            //SerializerService serializerService = GetSerializerService();

            //var li = serializerService.Deserialize<ILineItem>(orderSerialized);
            //var product = li.MapLineItemToRAOLineItem();

            //var executorPrice = li.GetPrice(FloristTypeEnum.Executor);
            //Assert.Equal(67.25m, executorPrice); // 6725 / 100

            //var otherPrice = li.GetPrice();
            //Assert.Equal(8.50m, otherPrice); // discounted = 850 / 100

            Assert.True(true);
        }
       

    }
}
