﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Messages.Florist
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class FloristStockAddedMessage : BaseMessage<FloristStockAddedPayload>, IMessageKey
            {
                public string GetMessageKey()
                => Payload.FloristId;
            }
        }
    }
}
