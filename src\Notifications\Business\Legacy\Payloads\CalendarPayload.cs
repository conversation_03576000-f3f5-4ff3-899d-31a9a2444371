﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class CalendarPayload : IPayload
    {
        public string Id { get; set; }
        public string FloristId { get; set; }
        public int DayOfWeek { get; set; }
        public string ClosedWindow { get; set; }
        public DateTime? WorkHourOpening { get; set; }
        public DateTime? WorkHourClosing { get; set; }
        public bool DeliveryAvailable { get; set; }
        public int? MaxDistanceOnDeliveryAvailable { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
