﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Yggdrasil;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class IfloraCustomerEmailMessage : BaseMessage<IfloraCustomerEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.IfloraCustomerEmails?.FirstOrDefault()?.Email;
        }
        public class IfloraRecipientEmailMessage : BaseMessage<IfloraRecipientEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.IfloraRecipientEmails?.FirstOrDefault()?.Email;
        }

    }
}

public class IfloraCustomerEmailPayload : IPayload
{
    public List<IfloraCustomerEmail> IfloraCustomerEmails { get; set; } = [];
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
    public string EmailType { get; set; } = "WAITING"; // Email Type to help routing the good email to emarsys with the same payload

}

public class IfloraRecipientEmailPayload : IPayload
{
    public List<IfloraRecipientEmail> IfloraRecipientEmails { get; set; } = [];
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
