﻿using Marten;
using Marten.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IProjection = Marten.Events.Projections.IProjection;
using Microsoft.Extensions.DependencyInjection;
using static ITF.SharedLibraries.ElasticSearch.APM.CorrelationLogsHelper;
using Microsoft.Extensions.Logging;
using ITF.SharedLibraries.ExtensionMethods;

namespace ITF.SharedLibraries.EventSourcing.Services
{
    public class MartenAutoProjectionManager : IProjection
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MartenAutoProjectionManager> _logger;

        public MartenAutoProjectionManager(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = _serviceProvider.GetService<ILogger<MartenAutoProjectionManager>>();
        }

        public void Apply(IDocumentOperations operations, IReadOnlyList<StreamAction> streams)
        {
            throw new NotImplementedException();
        }

        public async Task ApplyAsync(IDocumentOperations operations, IReadOnlyList<StreamAction> streams, CancellationToken cancellation)
        {
            var events = streams.SelectMany(x => x.Events).OrderBy(s => s.Sequence).Select(s => s);
            var projections = _serviceProvider.GetServices<Projections.Interfaces.IProjection>();
            foreach (var @event in events)
            {
                try
                {
                    await Task.WhenAll(projections.Select(x => x.Project(@event.Data, @event.Id.ToString())));
                }
                catch (Exception e)
                {
                    await Elastic.Apm.Agent.Tracer
                          .CaptureTransaction($"ERR_{nameof(MartenAutoProjectionManager)}_ExceptionHandlingTransaction", "Marten", (t) => 
                          {
                              CaptureExceptionTransaction(t, e, $"Exception in handling message from Marten");
                              _logger.LogError(e, "Error in handling message {message} from Marten", @event.Serialize());

                              return Task.CompletedTask;
                          });
                }
            }
        }
    }
}
