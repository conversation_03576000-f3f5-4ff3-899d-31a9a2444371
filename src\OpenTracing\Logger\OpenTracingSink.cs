﻿using OpenTracing;
using Serilog.Core;
using Serilog.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ITF.SharedLibraries.OpenTracing.Logger
{
    public class OpenTracingSink : ILogEventSink
	{
		private readonly ITracer _tracer;
		private readonly IFormatProvider _formatProvider;

		public OpenTracingSink(ITracer tracer, IFormatProvider formatProvider)
		{
			_tracer = tracer;
			_formatProvider = formatProvider;
		}

		public void Emit(LogEvent logEvent)
		{

			//// Do not report some Http health check
			//var ignoredHc = logEvent.Properties.FirstOrDefault(x => x.Key == "HealthCheckName").Value;
			//if (ignoredHc != null && ignoredHc.ToString().Contains("ElasticSearchHC"))
			//	return;

			ISpan span = _tracer.ActiveSpan;

			// Creating a new span for a log message seems brutal so we ignore messages if we can't attach it to an active span.
			if (span == null)
				return;
	
			var fields = new Dictionary<string, object>
			{
				{ "component", logEvent.Properties["SourceContext"] },
				{ "level", logEvent.Level.ToString() }
			};

			fields[LogFields.Event] = "log";

			try
			{
				fields[LogFields.Message] = logEvent.RenderMessage(_formatProvider);
				fields["message.template"] = logEvent.MessageTemplate.Text;

				if (logEvent.Exception != null)
				{
					fields[LogFields.ErrorKind] = logEvent.Exception.GetType().FullName;
					fields[LogFields.ErrorObject] = logEvent.Exception;
				}

				if (logEvent.Properties != null)
				{
					foreach (var property in logEvent.Properties)
					{
						fields[property.Key] = property.Value;
					}
				}
			}
			catch (Exception logException)
			{
				fields["mbv.common.logging.error"] = logException.ToString();
			}

			span.Log(fields);
		}
	}
}
