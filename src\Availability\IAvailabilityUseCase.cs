﻿using System.Threading.Tasks;

namespace ITF.Lib.Common.Availability
{
    public interface IAvailabilityUseCase
    {
        Task<IAvailabilityOutput> CheckAvailability(IAvailabilityInput availability);
        Task<IMomentsOutput> GetMoments(IMomentsInput moments);
        Task<IAutoCompleteOutput> PlaceAutocomplete(IAutoCompleteInput autoComplete);
        Task<IUnavailableDaysOutput> GetUnvailableDays(IUnavailableDaysInput unavailableDays);
        Task<IUndeliverableDaysOutput> GetUndeliverableDays(IUndeliverableDaysInput undeliverableDaysInput);
        Task<IFloristsOutput> GetFlorists(IFloristsInput input);
        Task<IValidateRegionOutput> ValidateRegionCart(IValidateRegionInput validateRegionInput);
    }
}
