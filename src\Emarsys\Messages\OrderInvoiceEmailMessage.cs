﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class OrderInvoiceEmailMessage : BaseMessage<OrderInvoiceEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.InvoiceEmails?.FirstOrDefault()?.Customer?.Email;
        }
    }
}

public class OrderInvoiceEmailPayload : IPayload
{
    public List<InvoiceEmail> InvoiceEmails { get; set; } = new List<InvoiceEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
