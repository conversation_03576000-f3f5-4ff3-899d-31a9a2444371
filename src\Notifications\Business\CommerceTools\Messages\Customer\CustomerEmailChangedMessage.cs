﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Customer;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Customer
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CustomerEmailChangedMessage : BaseMessage<CustomerEmailChangedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.CustomerEmailChangedMessage.Resource.Id;
            }
        }
    }
}
