﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacySpecialDayRemovedMessage : BaseMessage<LegacySpecialDayRemovedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                => Payload?.Start.ToString("yyyyMMdd") + Payload?.End.ToString("yyyyMMdd") + Payload?.Type.ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacySpecialDayRemovedPayload : LegacyPayload, IEquatable<LegacySpecialDayRemovedPayload>
    {
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public SpecialDayTypeEnum Type { get; set; }
        public List<string> Florists { get; set; } = new();

        public bool Equals(LegacySpecialDayRemovedPayload parameter)
        {
            return (Start == parameter.Start &&
                End == parameter.End &&
                Type == parameter.Type &&
                Florists.Equals(parameter.Florists)
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacySpecialDayRemovedPayload);
        }

        public override int GetHashCode() => new
        {
            Start,
            End,
            Type,
            Florists
        }.GetHashCode();
    }
}
