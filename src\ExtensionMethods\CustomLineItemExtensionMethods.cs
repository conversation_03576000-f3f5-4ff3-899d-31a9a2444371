﻿using commercetools.Sdk.Api.Models.Carts;
using IT.SharedLibraries.CT.CustomAttributes;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class CustomLineItemExtensionMethods
    {
        public static string GetRibbonText(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT].ToString();
            }
            return null;
        }
        public static string GetSku(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.SKU))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.SKU].ToString();
            }
            return null;
        }
        public static string GetDescription(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.DESCRIPTION))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.DESCRIPTION].ToString();
            }
            return null;
        }
        public static string GetProvider(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.PROVIDER))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.PROVIDER].ToString();
            }
            return null;
        }
        public static string GetId(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.ID))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.ID].ToString();
            }
            return null;
        }
        public static string GetRewardType(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.REWARD_TYPE))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.REWARD_TYPE].ToString();
            }
            return null;
        }
        public static string GetTransactionId(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.TRANSACTION_ID))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.TRANSACTION_ID].ToString();
            }
            return null;
        }
        public static string GetProviderResponse(this ICustomLineItem cli)
        {
            if (cli.Custom != null && cli.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.CustomLinetItem.PROVIDER_RESPONSE))
            {
                return cli.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.PROVIDER_RESPONSE].ToString();
            }
            return null;
        }
    }
}
