﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class MessagePayload : IPayload
    {
        public string FloristId { get; set; }
        public string Title { get; set; }
        public string Body { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public Level level { get; set; }
        public string Type { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public enum Level
    {
        information,
        warning,
        important,
        critical
    }
}
