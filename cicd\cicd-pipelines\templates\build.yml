steps:
  - bash: |
      # Define variables : 
      #   - docker image tag : 8 first characters of git commit id
      # -----------   
      
      commitID=$(echo $(imageTag) | cut -c1-8)
      helmChartVersion=$(echo $(helmChartVersion))
      
      echo "helmChartVersion: $helmChartVersion"

      if [ ! -z $commitID ] && [ ! -z $helmChartVersion ]
      then
        #Import variable in current pipeline  
        echo "##vso[task.setvariable variable=commitID;isOutput=true]$commitID" 
      else
        echo "Err: can't find commitID or helmChartVersion "
        echo "helmChartVersion  = $helmChartVersion"
        echo "commitID          = $commitID"
        exit -1
      fi
    displayName: 'Docker: Find commit id'
    name: versioningTask    

  - bash: |
      dotnet restore "src/$(dotnetProjectName)/$(dotnetProjectName).csproj"
      dotnet build "src/$(dotnetProjectName).csproj" -c Release
      dotnet test "tests/$(dotnetProjectName).UnitTests/$(dotnetProjectName).UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx" --collect "XPlat Code coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Exclude="[*]$(dotnetProjectName).Infrastructure.*,[*]$(dotnetProjectName).Presentation.*,[*]$(dotnetProjectName).Program,[*]$(dotnetProjectName).Startup"
      dotnet publish "src/$(dotnetProjectName).csproj" -c Release -o out
    displayName: 'Dotnet: Build and Test'

  - bash: |
      dotnet tool install -g dotnet-reportgenerator-globaltool
      reportgenerator -reports:$(Build.SourcesDirectory)/tests/**/coverage.cobertura.xml -targetdir:$(Build.SourcesDirectory)/CodeCoverage -reporttypes:"HtmlInline_AzurePipelines;Cobertura"
    displayName: Create Code coverage report

  - task: PublishTestResults@2
    condition: succeededOrFailed()
    inputs:
      testRunner: VSTest
      testResultsFiles: '**/*.trx'
      failTaskOnFailedTests: true
    displayName: 'Publish results'

  - task: PublishCodeCoverageResults@1
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: '$(Build.SourcesDirectory)/CodeCoverage/Cobertura.xml'
    displayName: 'Publish code coverage'    
    
  - bash: |
      docker build -t $(containerFullPath):$(versioningTask.commitID) -t $(containerFullPath):latest -f cicd/dockerfile.publish .
      docker login -u $(containerRegistryLogin) -p '$(containerRegistryPwd)' $(containerRegistry)
      echo "Pushing $(containerFullPath):$(versioningTask.commitID) "
      docker push $(containerFullPath):$(versioningTask.commitID)
      docker push $(containerFullPath):latest
    displayName: 'Docker: Build and Push image'