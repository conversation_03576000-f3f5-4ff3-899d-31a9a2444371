﻿using commercetools.Sdk.Api.Models.Products;
using ITF.SharedModels.Group.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using commercetools.Sdk.Api.Models.Common;
using System.Reflection.Metadata;
using System.Xml.Linq;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderItemUpdated
    {
        public string OrderIdentifier { get; set; }
        public OrderItemTypeEnum Type { get; set; }
        public GlobalOrderProduct Product { get; set; }
        public bool Equals(GlobalOrderItemUpdated parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier 
                && Type == parameter.Type
                && Product.Equals(parameter.Product)
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GlobalOrderItemUpdated);
        }
        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Type,
            Product
        }.GetHashCode();

        public static implicit operator GlobalOrderItemUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderItemUpdatedMessage v)
        {

            if (v?.Payload?.Type == null)
            {
                return new GlobalOrderItemUpdated();
            }

            return new GlobalOrderItemUpdated
            {
                OrderIdentifier = v?.Payload?.OrderIdentifier,
                Type = v.Payload.Type,
                Product = new GlobalOrderProduct()
                {
                    Name = v?.Payload?.Product.Name,
                    ProductKey = v?.Payload?.Product.ProductKey,
                    VariantKey = v?.Payload?.Product.VariantKey,
                    Quantity = v?.Payload?.Product.Quantity ?? 0,
                    Price = v?.Payload?.Product.Price ?? 0,
                    Description = v?.Payload?.Product.Description,
                    IsAccessoryFor = v?.Payload?.Product.IsAccessoryFor,
                    RibbonText = v?.Payload?.Product.RibbonText,
                    MarketingFee = v?.Payload?.Product.MarketingFee,
                    ExecutingFloristAmount = v?.Payload?.Product.ExecutorAmount, 
                } 
            };
        }
    }
}
