﻿using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity
{
    public class OrderCancelledEmail
    {
        [SwaggerSchema(Nullable = false)]
        public string OrderId { get; set; }

        [SwaggerSchema(Format = "yyyy-MM-dd")]
        public string NewDeliveryDate { get; set; }

        public decimal RefundAmount { get; set; }

        public CustomerEmail Customer { get; set; } = new();
    }
}
