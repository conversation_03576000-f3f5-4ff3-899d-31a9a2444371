﻿namespace ITF.SharedLibraries.Kafka.Publisher
{
    public class TopicsToCreateConfiguration
    {
        public string TopicName { get; set; }
        public short? ReplicationFactor { get; set; }
        public int NumberOfPartitions { get; set; }
        public long? RetentionMs { get; set; }

        public long? RetentionBytes { get; set; }
        public long? MaxMessageBytes { get; set; }
    }
}
