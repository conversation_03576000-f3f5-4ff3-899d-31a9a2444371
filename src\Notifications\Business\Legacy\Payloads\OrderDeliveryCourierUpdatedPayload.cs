﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class OrderDeliveryCourierUpdatedPayload : IPayload
    {
        public string OrderId { get; set; }
        public int Version { get; set; }
        public string DeliveryCourierStatus { get; set; }
        public DateTime LastModified { get; set; }
        public decimal? Price { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
