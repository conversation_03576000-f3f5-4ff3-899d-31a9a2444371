﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;

namespace ITF.SharedModels.Messages.Italy.Document.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyDocumentGeneratedMessage : BaseMessage<LegacyDocumentGeneratedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyDocumentGeneratedPayload : LegacyPayload, IEquatable<LegacyDocumentGeneratedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string OrderCode { get; set; }
        public int DocumentYear { get; set; }
        public int DocumentMonth { get; set; }
        public string DocumentUrl { get; set; }
        public string Filename { get; set; }
        public string FileExtension { get; set; }
        public DocTypeEnum DocType { get; set; }
        public string OctopusOrderId { get; set; }
        public string CTOrderId { get; set; }

        public bool Equals(LegacyDocumentGeneratedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                OrderCode == parameter.OrderCode &&
                DocumentYear == parameter.DocumentYear &&
                DocumentMonth == parameter.DocumentMonth &&
                DocumentUrl == parameter.DocumentUrl &&
                DocType == parameter.DocType &&
                Filename == parameter.Filename &&
                FileExtension == parameter.FileExtension &&
                OctopusOrderId == parameter.OctopusOrderId &&
                CTOrderId == parameter.CTOrderId
            );
        }
    }
}
