﻿using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.Messages.Repo;
using MongoDB.Driver;
using System.Linq.Expressions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure
{
    public class FloristsRepository : MongoRepository<GlobalFloristModel>, IFloristsRepository
    {
        public FloristsRepository(ITF.SharedLibraries.MongoDB.Configuration configuration, IMongoClient mongoClient) : base(mongoClient, configuration.DatabaseNameSubset, RepositoryNames.Florist)
        {
        }

        public async Task<GlobalFloristModel> GetByIdentifier(string floristIdentifier)
        {
            Expression<Func<GlobalFloristModel, bool>> where = c => c.FloristId == floristIdentifier;
            var filter = Builders<GlobalFloristModel>.Filter.Where(where);
            var result = await FilterByAsync(filter);
            return result.FirstOrDefault();
        }

        public async Task SaveDocuments(GlobalFloristModel florist)
        {
            var updateDef = Builders<GlobalFloristModel>.Update
                          .Set(o => o.Documents, florist.Documents)
                          .Set(o => o.LastUpdate, florist.LastUpdate);

            var filter = Builders<GlobalFloristModel>.Filter.Eq(x => x.Id, florist.FloristId);
            await Collection.UpdateOneAsync(filter, updateDef);
        }
    }
}
