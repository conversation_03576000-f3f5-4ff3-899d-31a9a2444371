﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class FloristCourierDeliveryEnabledPayload : IPayload
    {
        public string FloristId { get; set; }
        public bool CourierEnabled { get; set; }
        public string User { get; set; }
        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
