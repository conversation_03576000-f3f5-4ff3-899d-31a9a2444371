﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class FloristHeaderPayload : IPayload
    {
        public DateTime LastSynchronized { get; set; }
        public string DataArea { get; set; }
        public string FloristId { get; set; }
        public string PreviousOwner { get; set; }
        public string InvoiceAccount { get; set; }
        public string Group { get; set; }
        public bool IsCompensationExcluded { get; set; }
        public bool IsInterfloraExclusive { get; set; }
        public string PartnerCode { get; set; }
        public bool IsConferenceMeetingAttendance { get; set; }
        public bool IsClubMeetingAttendance { get; set; }
        public string InformationNote { get; set; }
        public DateTime RoyaltyFeeFirstDate { get; set; }
        public DateTime LegalEntityCreationDate { get; set; }
        public bool IsMainShop { get; set; }
        public DateTime InterfloraWebsiteAuthorizationEndDate { get; set; }
        public string FloralCelebrationComment { get; set; }
        public bool AsReceivedContract { get; set; }
        public string Comments { get; set; }
        public string AdvertisingComment { get; set; }
        public DateTime InitialisationExecutionDate { get; set; }
        public string CompanySiret { get; set; }
        public DateTime SuspensionStartDate { get; set; }
        public DateTime SuspensionEndDate { get; set; }
        public DateTime AgreementStopDate { get; set; }
        public string NextOwner { get; set; }
        public string ClubOfficeMember { get; set; }
        public DateTime LastModified { get; set; }
        public bool IsPayingContribution { get; set; }
        public bool IsLocatedInAMall { get; set; }
        public string TaxGroupCode { get; set; }
        public bool IsSuspended { get; set; }
        public string AgreementStopReason { get; set; }
        public string ShopSignComment { get; set; }
        public string ContactPartyNumber { get; set; }
        public string Name { get; set; }
        public bool IsClubMember { get; set; }
        public DateTime ContractSendingDate { get; set; }
        public bool AsAcceptedcommittee { get; set; }
        public string OwnerContactName { get; set; }
        public string Awards { get; set; }
        public List<string> Segmentations { get; set; } = new();
        public Bonus Bonus { get; set; } = new();
        public Performance Performance { get; set; } = new();
        public President President { get; set; } = new();
        public Equipment Equipment { get; set; } = new();
        public Location Location { get; set; } = new();
        public Promoter Promoter { get; set; } = new();
        public TelePromoter TelePromoter { get; set; } = new();
        public MysteryVisit MysteryVisit { get; set; } = new();
        public bool IsFrench { get; set; }
        public bool IsForeign { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class President
    {
        public string Name { get; set; }
        public string Address { get; set; }
    }

    public class Equipment
    {
        public string EquipmentsAvailables { get; set; }
        public bool IsEquippedWithAComputer { get; set; }
        public string EquipmentType { get; set; }
        public string ComputerEquipments { get; set; }
    }

    public class Promoter
    {
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class TelePromoter
    {
        public string Name { get; set; }
    }

    public class MysteryVisit
    {
        public DateTime TransmissionVisitLastDate { get; set; }
        public DateTime ExecutionVisitLastDate { get; set; }
    }

    public class Bonus
    {
        public double Quality { get; set; }
        public double Litigation { get; set; }
        public double Reassignment { get; set; }
        public double VisitCard { get; set; }
        public DateTime VisitCardEndDate { get; set; }
        public double OpenForFloralCelebrations { get; set; }
        public DateTime OpenForFloralCelebrationsEndDate { get; set; }
        public double ShopAccessoryArea { get; set; }
        public DateTime ShopAccessoryEndDate { get; set; }
        public double HighlightingBrand { get; set; }
        public DateTime HighlightingBrandEndDate { get; set; }
        public double InvolvementIndex { get; set; }
        public double InterfloraWebsiteLink { get; set; }
        public double MondayDayOpening { get; set; }
        public double NPS { get; set; }
        public double CarAdvertising { get; set; }
        public DateTime CarAdvertisingEndDate { get; set; }
        public double InterfloraWebsiteUsage { get; set; }
        public DateTime InterfloraWebsiteUsageEndDate { get; set; }
        public double DayOpening { get; set; }
    }

    public class Performance
    {
        public double ExecutionTransmissionRatio { get; set; }
        public double ManualPerformanceAmount { get; set; }
        public double ManualPerformanceVolume { get; set; }
        public DateTime ManualPerformanceEndDate { get; set; }
        public string ManualPerformanceMotivation { get; set; }
        public double QualityRating { get; set; }
        public double TransmissionVolume { get; set; }
        public double TransmissionAmount { get; set; }
        public double ExecutionVolume { get; set; }
        public double ExecutionAmount { get; set; }
    }

    public class Location
    {
        public string Street { get; set; }
        public string ZipCode { get; set; }
        public string City { get; set; }
        public string CountryCode { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }
}
