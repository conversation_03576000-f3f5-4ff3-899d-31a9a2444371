﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist
{
    public class FloristStockDeletedPayload : IPayload
    {
        public string FloristId { get; set; }
        public string ProductCode { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
