﻿using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using Xunit;

namespace IT.SharedLibraries.CT.UnitTests.Extensions
{
    public class Address
    {
        [Fact]
        public async Task Test_Address_GetTime_1()
        {
            IAddress address = new commercetools.Sdk.Api.Models.Common.Address();
            address.Custom = new CustomFields {
                Fields = new FieldContainer()
            };
            address.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.TIME, "15:30:00.000");
            TimeSpan? time = address.GetTime();

            Assert.NotNull(time);
            Assert.Equal(15, time.Value.Hours);
            Assert.Equal(30, time.Value.Minutes);
        }

        [Fact]
        public async Task Test_Address_GetTime_2()
        {
            IAddress address = new commercetools.Sdk.Api.Models.Common.Address();
            address.Custom = new CustomFields
            {
                Fields = new FieldContainer()
            };
            address.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.TIME, "16:25");
            TimeSpan? time = address.GetTime();

            Assert.NotNull(time);
            Assert.Equal(16, time.Value.Hours);
            Assert.Equal(25, time.Value.Minutes);
        }
    }
}
