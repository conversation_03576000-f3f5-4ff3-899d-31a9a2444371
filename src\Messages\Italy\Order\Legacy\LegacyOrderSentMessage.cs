﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderSentMessage : BaseMessage<LegacyOrderSentPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderSentMessage((string ctOrderId, OrderUpdatedMessage message) src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderSentMessage = new LegacyOrderSentMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderSentPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,                         
                        }
                    };


                    return legacyOrderSentMessage;
                }
            }
        }
    }

    public class LegacyOrderSentPayload : LegacyPayload, IEquatable<LegacyOrderSentPayload>
    {
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrderSentPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderSentPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier
        }.GetHashCode();
    }
}
