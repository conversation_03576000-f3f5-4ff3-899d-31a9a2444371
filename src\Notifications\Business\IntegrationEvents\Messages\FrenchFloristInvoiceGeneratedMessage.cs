﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Messages
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class FrenchFloristInvoiceGeneratedMessage : BaseMessage<FrenchFloristInvoiceGeneratedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.OrderId;
            }
        }
    }
}
