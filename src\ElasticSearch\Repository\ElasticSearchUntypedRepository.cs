﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedLibraries.ExtensionMethods;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ElasticSearch.Constants;

namespace ITF.SharedLibraries.ElasticSearch.Repository
{
    public class ElasticSearchUntypedRepository<Z> : IElasticSearchUntypedRepository<Z>
    {
        private readonly IElasticClient _elasticClient;
        private readonly string _index;
        private readonly bool _readOnlyMode;

        public ElasticSearchUntypedRepository(IElasticClient elasticClient, string index, bool readOnly = false)
        {
            _elasticClient = elasticClient;
            _readOnlyMode = readOnly;
            _index = index.ToLower();

            if (_readOnlyMode)
                return;

            var exist = _elasticClient.Indices.Exists(_index);
            if (!exist.Exists)
            {
                var res = _elasticClient.Indices.Create(_index, 
                    c => c
                        .Settings(s => s.Analysis(a => a
                            .Normalizers(n => n.Custom(NORMALIZER, c => c.Filters(LOWERCASE)))
                        ))
                        //.Map(m => m.DynamicTemplates(dt => dt.DynamicTemplate("string_to_keyword", t => t
                        //        .MatchMappingType("string")
                        //        .Mapping(map => map.Keyword(k => k.Normalizer(LOWERCASE))))))
                           )
                    ;

                if (!res.IsValid && res.OriginalException is not null)
                    throw res.OriginalException;
            }

            PutFilteredMapping();
        }

        // Should stay public to be referenced throught reflection
        public void MapFields<R, K, J>() where R : BaseClass<K>
                                                 where J : IMappings<R>, new()
        {
            var instance = Activator.CreateInstance<J>();
            var mappingSettings = instance.GetMappingProperties();
            var res = _elasticClient.Map<R>(x => x.Index(_index).AutoMap().Properties(mappingSettings));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;
        }

        private void PutFilteredMapping()
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            var types = Types.GetAllTypesImplementingOpenGenericType(typeof(IMappings<>));
            var methodInfo = typeof(ElasticSearchUntypedRepository<Z>).GetMethod("MapFields");
            types?.ToList().ForEach(t =>
            {
                if (typeof(Z).IsAssignableFrom(t))
                {
                    var refMethod = methodInfo?.MakeGenericMethod(t, typeof(string), t);
                    refMethod.Invoke(this, null);
                }
            });
        }

        public virtual async Task<CountResponse> CountAsync<T, K>(Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z
        {
            return await _elasticClient.CountAsync<T>(c => c.Index(_index).Query(query));
        }

        public virtual async Task<bool> AnyAsync<T, K>() where T : BaseClass<K>, Z
        {
            var count = await _elasticClient.CountAsync<T>(c => c.Index(_index));
            if (!count.IsValid && count.OriginalException is not null)
                throw count.OriginalException;

            return count.Count > 0;
        }

        public virtual async Task<IReadOnlyCollection<T>> GetAll<T, K>() where T : BaseClass<K>, Z
        {
            var finalResults = new List<T>();
            var scanResults = await _elasticClient.SearchAsync<T>(s => s.Index(_index)
                            .From(0)
                            .Size(100)
                            .MatchAll()
                            .SearchType(Elasticsearch.Net.SearchType.QueryThenFetch)
                            .Scroll("5M")
                        );

            if (!scanResults.IsValid && scanResults.OriginalException is not null)
                throw scanResults.OriginalException;

            finalResults.AddRange(scanResults.Documents);
            var results = await _elasticClient.ScrollAsync<T>("10m", scanResults.ScrollId);

            if (!results.IsValid && results.OriginalException is not null)
                throw results.OriginalException;

            do
            {
                if (results.Documents.Count > 0)
                    finalResults.AddRange(results.Documents);

                var nbFinalResults = finalResults.Count;
                var nbFresults = results.Documents.Count;

                results = await _elasticClient.ScrollAsync<T>("10m", results.ScrollId);
                if (!results.IsValid && results.OriginalException is not null)
                    throw results.OriginalException;

                nbFresults = results.Documents.Count;


            } while (results.Documents.Any());

            return finalResults;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get<T, K>(Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Query(query));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }

        public virtual async Task<IReadOnlyCollection<T>> Get<T, K>(int size, Func<QueryContainerDescriptor<T>, QueryContainer> query) where T : BaseClass<K>, Z
        {
            var searchResponse = await _elasticClient.SearchAsync<T>(s => s.Index(_index).Size(size).Query(query));
            if (!searchResponse.IsValid && searchResponse.OriginalException is not null)
                throw searchResponse.OriginalException;

            return searchResponse.Documents;
        }
        public virtual async Task<GetResponse<T>> GetById<T, K>(DocumentPath<T> Id) where T : BaseClass<K>, Z
        {
            var res = await _elasticClient.GetAsync(Id, idx => idx.Index(_index));
            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public virtual async Task<IEnumerable<IMultiGetHit<T>>> GetByIds<T, K>(IEnumerable<string> Ids) where T : BaseClass<K>, Z
        {
            var res = await _elasticClient.GetManyAsync<T>(Ids, _index);
            return res;
        }

        public async Task<BulkResponse> Update<T, K>(DocumentPath<T> Id, T newEntity, int? retriesOnConflict = 10) where T : BaseClass<K>, Z
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            //CheckTypeValidity<T, K>(newEntity);

            var res = await _elasticClient.BulkAsync(b => b
                .Index(_index)
                .Update<T>( d => d
                    .RetriesOnConflict(retriesOnConflict)
                    .Doc(newEntity)
                    .Upsert(newEntity)));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public async Task<BulkResponse> UpdateMany<T, K>(List<T> newEntities, int? retriesOnConflict = 10) where T : BaseClass<K>, Z
        {
            if (_readOnlyMode)
                throw new UnauthorizedAccessException("The read only mode is activated");

            //newEntities.ForEach(e => CheckTypeValidity<T, K>(e));

            var res = await _elasticClient.BulkAsync(b => b
            .Index(_index)
            .UpdateMany(newEntities, (d, doc) => d
                .RetriesOnConflict(retriesOnConflict)
                .Doc(doc)
                .Upsert(doc)));

            if (!res.IsValid && res.OriginalException is not null)
                throw res.OriginalException;

            return res;
        }

        public string GetIndex() => _index;


        private static void CheckTypeValidity<T, K>(T entity) where T : BaseClass<K>
        {
            if (!typeof(Z).IsAssignableFrom(entity.GetType()))
                throw new UnauthorizedAccessException($"The entity must implement {typeof(Z)}");
        }
    }
}
