# ITF.SharedModels

This library aims to share models between Microservices. The models shared should be static enough, like Kafka messages, otherwise even if it introduces duplication, the models should be hold by each MS.

## Install

We can install the Nuget package from private feed using the bash

```bash
Install-Package ITF.SharedModels
```
or
editing the .csproj file of any projet, example :
```csharp
  <ItemGroup>
    <PackageReference Include="ITF.SharedModels" Version="1.1.0" />
  </ItemGroup>
```


## Usage / examples

Example of a Kafka message with its payload

```csharp
public class StockReferencePayload : IPayload
{
    public string FloristId { get; set; }
    public string ProductId { get; set; }
    public int Quantity { get; set; }

    public string EventID { get; set; }
    public DateTime EventDate { get; set; }
    public DateTime ExpiryTimeGlobal { get; set; }

    public string GetId() => $"{FloristId}_{ProductId}";
}

public class StockReferenceMessage : BaseMessage<StockReferencePayload>
{
}
```

