# IT.Microservices.OrderReactor

Write a description here ...

## Docker compose

Describe the docker compose 

```dockerfile
  itorderreactor:
    image: ${DOCKER_REGISTRY-}itorderreactor
    container_name: itorderreactor
    build:
      context: .
      dockerfile: src/IT.Microservices.OrderReactor/src/Dockerfile
```

```dockerfile
  itorderreactor:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - UNLEASH__KEY=*:default.0f1cc0c83ac07695d565dbc4317af531d1d9dd3e864f52137da6112a
    ports:
      - "99999:80"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
```
## Settings

Indicate here the list of specific settings here, and their purpose

```json
"CommerceToolCustomSettings": {
      "LocalCountryCode": "IT", // local country code to be used as local country code in orders created within CT
      "LocalCountryChannelKey": "interflora.it", // local channel key to be used as channel in orders created within CT
      "LocalCountryStoreKey": "ITI", // local store key to be used as channel in orders created within CT
      "CtMoruningProductTypeKey": "mourning", // mourning product type key to be used as channel in orders created within CT
      "LocalCountryAccessoriesCategoryKey": "ACC",
      "LocalCountryProductsCategoryKey": "category",
      "OutboundOrderShippingAmount": 15.99, // amount to be used for shipping in international oubound orders created within CT
      "OutboundOrderShippingTaxRate": 0 // tax rate to be used for shipping in international oubound orders created within CT
    }
```

## Usage / examples

Indicate here some examples of how to use it ...

```bash
# Perform a call and return something ...
curl --location --request GET 'http://localhost:99999/api/Any/Something?id=1234' \
--header 'Content-Type: application/json' 
```