﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using ITF.SharedLibraries.Emarsys.Models.Api;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.HttpClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace ITF.SharedLibraries.Emarsys;

public class EmarsysHttpService : HttpClient.HttpClient, IEmarsysHttpService
{
  private readonly IOptionsMonitor<EmailEmarsysSettings> _settings;
  private readonly IConfiguration _configuration;
  private readonly IOptionsMonitor<EmarsysFieldsIds> _emarsysFieldsIds;
  private readonly JsonSerializerSettings _jsonSerializerContactSettings;
  private readonly JsonSerializerSettings _jsonSerializerCreateContactSettings;


  public EmarsysHttpService(System.Net.Http.HttpClient httpClient, IConfiguration configuration, IOptionsMonitor<EmailEmarsysSettings> settings, IOptionsMonitor<EmarsysFieldsIds> emarsysFieldsIds, ILogger<EmarsysHttpService> logger) : base(httpClient, configuration, "EmarsysHttpEndpoint", logger)
  {
    _settings = settings;
    _configuration = configuration;
    _emarsysFieldsIds = emarsysFieldsIds;
    _jsonSerializerContactSettings = new JsonSerializerSettings();
    _jsonSerializerContactSettings.Converters.Add(new EmarsysJsonConverter(_emarsysFieldsIds));
    _jsonSerializerCreateContactSettings = new JsonSerializerSettings();
    _jsonSerializerCreateContactSettings.Converters.Add(new EmarsysCreateOrUpdateContactJsonConverter(logger));
    _jsonSerializerCreateContactSettings.Converters.Add(new EmarsysCreateOrUpdateContactBatchJsonConverter(logger));
  }

  public List<string> ListAllContactFieldsId()
  {
    var fields = new List<string>()
        {
          _emarsysFieldsIds.CurrentValue.ContactId,
          _emarsysFieldsIds.CurrentValue.Email,
          _emarsysFieldsIds.CurrentValue.Salutation,
          _emarsysFieldsIds.CurrentValue.LastName,
          _emarsysFieldsIds.CurrentValue.FirstName,
          _emarsysFieldsIds.CurrentValue.CompanyName,
          _emarsysFieldsIds.CurrentValue.B2C,
          _emarsysFieldsIds.CurrentValue.B2B,
          _emarsysFieldsIds.CurrentValue.B2F,
          _emarsysFieldsIds.CurrentValue.DateOfBirth,
          _emarsysFieldsIds.CurrentValue.Mobile,
          _emarsysFieldsIds.CurrentValue.PostalCode,
          _emarsysFieldsIds.CurrentValue.City,
          _emarsysFieldsIds.CurrentValue.Country,
          _emarsysFieldsIds.CurrentValue.OptInOriginFr,
          _emarsysFieldsIds.CurrentValue.OptInOriginEs,
          _emarsysFieldsIds.CurrentValue.OptInOriginIt,
          _emarsysFieldsIds.CurrentValue.OptInOriginPt,
          _emarsysFieldsIds.CurrentValue.OptInOriginDk,
          _emarsysFieldsIds.CurrentValue.OptIn,
          _emarsysFieldsIds.CurrentValue.OptInEmailFr,
          _emarsysFieldsIds.CurrentValue.OptInEmailEs,
          _emarsysFieldsIds.CurrentValue.OptInEmailIt,
          _emarsysFieldsIds.CurrentValue.OptInEmailPt,
          _emarsysFieldsIds.CurrentValue.OptInEmailDk,
          _emarsysFieldsIds.CurrentValue.OptInSmsFr,
          _emarsysFieldsIds.CurrentValue.OptInSmsEs,
          _emarsysFieldsIds.CurrentValue.OptInSmsIt,
          _emarsysFieldsIds.CurrentValue.OptInSmsPt,
          _emarsysFieldsIds.CurrentValue.OptInSmsDk,
          _emarsysFieldsIds.CurrentValue.MobileSmsOptIn,
          _emarsysFieldsIds.CurrentValue.OptInAbandonedCartFr,
          _emarsysFieldsIds.CurrentValue.OptInAbandonedCartEs,
          _emarsysFieldsIds.CurrentValue.OptInAbandonedCartIt,
          _emarsysFieldsIds.CurrentValue.OptInAbandonedCartPt,
          _emarsysFieldsIds.CurrentValue.OptInAbandonedCartDk,
          _emarsysFieldsIds.CurrentValue.OptInBirthdayFr,
          _emarsysFieldsIds.CurrentValue.OptInBirthdayEs,
          _emarsysFieldsIds.CurrentValue.OptInBirthdayIt,
          _emarsysFieldsIds.CurrentValue.OptInBirthdayPt,
          _emarsysFieldsIds.CurrentValue.OptInBirthdayDk,
          _emarsysFieldsIds.CurrentValue.CreatedFr,
          _emarsysFieldsIds.CurrentValue.CreatedEs,
          _emarsysFieldsIds.CurrentValue.CreatedIt,
          _emarsysFieldsIds.CurrentValue.CreatedPt,
          _emarsysFieldsIds.CurrentValue.CreatedDk,
          _emarsysFieldsIds.CurrentValue.ContactFr,
          _emarsysFieldsIds.CurrentValue.ContactEs,
          _emarsysFieldsIds.CurrentValue.ContactIt,
          _emarsysFieldsIds.CurrentValue.ContactPt,
          _emarsysFieldsIds.CurrentValue.ContactDk,
          _emarsysFieldsIds.CurrentValue.FloristId,
          _emarsysFieldsIds.CurrentValue.FloristScoreNps,
          _emarsysFieldsIds.CurrentValue.FloristEntryDate,
          _emarsysFieldsIds.CurrentValue.FloristActif,
          _emarsysFieldsIds.CurrentValue.ItfPlusFrMember,
          _emarsysFieldsIds.CurrentValue.ItfPlusEsMember,
          _emarsysFieldsIds.CurrentValue.ItfPlusItMember,
          _emarsysFieldsIds.CurrentValue.ItfPlusPtMember,
          _emarsysFieldsIds.CurrentValue.ItfPlusFrDateStart,
          _emarsysFieldsIds.CurrentValue.ItfPlusEsDateStart,
          _emarsysFieldsIds.CurrentValue.ItfPlusItDateStart,
          _emarsysFieldsIds.CurrentValue.ItfPlusPtDateStart,
          _emarsysFieldsIds.CurrentValue.ItfPlusFrDateEnd,
          _emarsysFieldsIds.CurrentValue.ItfPlusEsDateEnd,
          _emarsysFieldsIds.CurrentValue.ItfPlusItDateEnd,
          _emarsysFieldsIds.CurrentValue.ItfPlusPtDateEnd
        };

    fields.RemoveAll(x => string.IsNullOrWhiteSpace(x));

    return fields;
  }

  public async Task<ContactPayload?> GetContactAsync(string email)
  {
    var fields = ListAllContactFieldsId();

    if (fields.Count == 0 || string.IsNullOrWhiteSpace(email))
      return null;

    var payload = new GetContactPayload(_emarsysFieldsIds.CurrentValue.Email, fields, email);

    try
    {
      var res = await PostAsync(payload, Combine(_settings.CurrentValue.ContactEndpoint, _settings.CurrentValue.GetContactEndpoint));
      if (res.IsSuccessStatusCode)
        return await ReadInformationsAsync<ContactPayload>(res, Serializer.SerializerType.NewtonSoft, _jsonSerializerContactSettings);
      return null;
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to get contact from Emarsys API");
      throw;
    }
  }

  public async Task<ContactPayload?> GetContactAsync(List<string> emails)
  {
    var fields = ListAllContactFieldsId();

    if (fields.Count == 0 || emails.Count == 0)
      return null;

    var payload = new GetContactPayload(_emarsysFieldsIds.CurrentValue.Email, fields, emails);

    try
    {
      var res = await PostAsync(payload, Combine(_settings.CurrentValue.ContactEndpoint, _settings.CurrentValue.GetContactEndpoint));
      if (res.IsSuccessStatusCode)
        return await ReadInformationsAsync<ContactPayload>(res, Serializer.SerializerType.NewtonSoft, _jsonSerializerContactSettings);
      return null;
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to get contacts from Emarsys API");
      throw;
    }
  }

  public async Task<ContactCreationPayload?> CreateOrUpdateContactAsync(string keyId, Dictionary<string, string> contact)
  {
    if (string.IsNullOrEmpty(keyId) || contact.Count == 0)
      return null;

    var payload = new PutContactPayload(keyId, new() { contact });

    try
    {
      var res = await PutAsync(payload, _settings.CurrentValue.ContactEndpoint, "?create_if_not_exists=1");
      var contactCreation = await ReadInformationsAsync<ContactCreationPayload>(res, Serializer.SerializerType.NewtonSoft, _jsonSerializerCreateContactSettings);
      return contactCreation;
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to create or update contact from Emarsys API");
      throw;
    }
  }

  public async Task<ContactCreationBatchPayload?> CreateOrUpdateContactsAsync(string keyId, List<Dictionary<string, string>> contacts)
  {
    if (string.IsNullOrEmpty(keyId) || contacts.Count == 0)
      return null;

    var payload = new PutContactPayload(keyId, contacts);

    try
    {
      var res = await PutAsync(payload, _settings.CurrentValue.ContactEndpoint, "?create_if_not_exists=1");
      _logger.LogInformation("CreateOrUpdateContactsAsync: Received payload: {Response}", await res.Content.ReadAsStringAsync());
      var contactCreation = await ReadInformationsAsync<ContactCreationBatchPayload>(res, Serializer.SerializerType.NewtonSoft, _jsonSerializerCreateContactSettings);
      return contactCreation;
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to create or update contact batch from Emarsys API");
      throw;
    }
  }

  public string Combine(params string[] uriParts)
  {
    return CombineUri(uriParts);
  }

  public async Task<EmarsysRdsResponse> UpsertRdsRecordsAsync(string connectionName, string tableName, List<EmarsysRdsRecord> records)
  {
    if (string.IsNullOrWhiteSpace(connectionName) || string.IsNullOrWhiteSpace(tableName) || records?.Count == 0)
      return null;
    try
    {
      var res = await PostAsync(records, $"rds/v1/connections/{connectionName}/tables/{tableName}/records/upsert");
      return await ReadInformationsAsync<EmarsysRdsResponse>(res);
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to upsert RDS records to Emarsys API");
      throw;
    }
  }

  public async Task<EmarsysRdsResponse> DeleteRdsRecordByIdAsync(string connectionName, string tableName, string recordId)
  {
    if (string.IsNullOrWhiteSpace(connectionName) || string.IsNullOrWhiteSpace(tableName) || string.IsNullOrWhiteSpace(recordId))
      return null;

    var recordsToDelete = new List<object> { new { id = recordId } };

    try
    {
      var res = await PostAsync(recordsToDelete, $"rds/v1/connections/{connectionName}/tables/{tableName}/records/remove");
      return await ReadInformationsAsync<EmarsysRdsResponse>(res);
    }
    catch (Exception e)
    {
      _logger.LogError(e, "Failed to delete RDS record from Emarsys API");
      throw;
    }
  }

}
public interface IEmarsysHttpService : IHttpClient
{
  Task<ContactPayload?> GetContactAsync(string email);
  Task<ContactPayload?> GetContactAsync(List<string> emails);
  Task<ContactCreationPayload?> CreateOrUpdateContactAsync(string keyId, Dictionary<string, string> contact);
  Task<ContactCreationBatchPayload?> CreateOrUpdateContactsAsync(string keyId, List<Dictionary<string, string>> contacts);
  string Combine(params string[] uriParts);
  Task<EmarsysRdsResponse> UpsertRdsRecordsAsync(string connectionName, string tableName, List<EmarsysRdsRecord> records);
  Task<EmarsysRdsResponse> DeleteRdsRecordByIdAsync(string connectionName, string tableName, string recordId);
}