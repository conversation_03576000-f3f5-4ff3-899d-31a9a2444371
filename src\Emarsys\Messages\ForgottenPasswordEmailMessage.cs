﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {
        public class ForgottenPasswordEmailMessage : BaseMessage<ForgottenPasswordEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.PasswordEmails?.FirstOrDefault()?.Customer?.Email;
        }
    }
}

public class ForgottenPasswordEmailPayload : IPayload
{
    public List<PasswordEmail> PasswordEmails { get; set; } = new List<PasswordEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
