﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.Legacy.Payloads
{
    public class StockReferencePayload : IPayload
    {
        public string FloristId { get; set; }
        public string ProductId { get; set; }
        public string Type { get; set; }
        public int Quantity { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }

        public string GetId() => $"{FloristId}_{ProductId}";
    }
}
