# See https://docs.microsoft.com/en-us/azure/devops/pipelines/tasks/test/publish-test-results?view=azure-devops&tabs=yaml#docker
# N. Rey 22/06/2020

FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /src

COPY ["src/IT.Microservices.OrderReactor.csproj", "/src/IT.Microservices.OrderReactor/"]
COPY ["src/NuGet.config", "/src/IT.Microservices.OrderReactor/"]

RUN dotnet restore "IT.Microservices.OrderReactor/IT.Microservices.OrderReactor.csproj"

WORKDIR "/src/IT.Microservices.OrderReactor"

COPY . .

RUN dotnet build "src/IT.Microservices.OrderReactor.csproj" -c Release
RUN dotnet test "tests/IT.Microservices.OrderReactor.UnitTests/IT.Microservices.OrderReactor.UnitTests.csproj" -c Release --logger "trx;LogFileName=testresults.trx"; exit 0
RUN dotnet publish "src/IT.Microservices.OrderReactor.csproj" -c Release -o out

ENTRYPOINT sleep 10000