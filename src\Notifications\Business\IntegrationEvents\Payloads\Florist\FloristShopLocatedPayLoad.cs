﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist
{
    public class FloristShopLocatedPayLoad : IPayload
    {
        public string FloristId { get; set; }
        public string Name { get; set; }
        public string Street { get; set; }
        public string ZipCode { get; set; }
        public string City { get; set; }
        public string CountryCode { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
