﻿using Elastic.Apm.NetCoreAll;
using Elasticsearch.Net;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.ElasticSearch.Repository;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nest;
using System;
using System.Linq;

namespace ITF.SharedLibraries.ElasticSearch.Extensions
{
    public static class ElasticSearchExtensions
    {
        public static IServiceCollection UseElasticSearch(this IServiceCollection services, IConfiguration config, string varEnv = "ElasticSearch", bool debugEnable = false)
        {
            var configuration = config.Get<Configuration>(varEnv);
            var pool = new StaticConnectionPool(configuration.Nodes.Select(u => new Uri(u.NodeUrl)));
            var settings = new ConnectionSettings(pool);
            settings = settings.DisablePing();
            if (debugEnable)
                settings = settings
                    .EnableDebugMode()
                    .DisableDirectStreaming()
                    .PrettyJson();
            var client = new ElasticClient(settings);

            services.AddSingleton<IElasticClient>(cli => client);
            return services;
        }

        public static IApplicationBuilder UseElasticSearchAPM(this IApplicationBuilder app, IConfiguration config)
        {
            try
            {
                if (!Elastic.Apm.Agent.IsConfigured)
                    app.UseAllElasticApm(config);
            }
            catch { }

            return app;
        }

        public static void PutFilteredMappingSettings<T, K>(object obj, ILogger logger)
        {
            try
            {
                var types = Types.GetAllTypesImplementingOpenGenericType(typeof(IMappings<>));
                var methodInfo = typeof(T).GetMethod("MapFields");

                types?.ToList().ForEach(t =>
                {
                    try
                    {
                        // https://stackoverflow.com/a/15138990/4734707
                        if (typeof(K).IsAssignableFrom(t))
                        {
                            // https://stackoverflow.com/a/3958026/4734707
                            // Allows to map typed objects to the current collection
                            var refMethod = methodInfo?.MakeGenericMethod(t, typeof(string), t);
                            refMethod.Invoke(obj, null);
                        }
                    }
                    catch (Exception e)
                    {
                        logger?.LogError(e, "Fail to apply mapping settings of type {type}", t.ToString());
                    }
                });
            }
            catch(Exception e)
            {
                logger?.LogError(e, "Error on {methodname}", nameof(PutMappingSettings));
            }
        }

        public static void PutMappingSettings<T>(object obj, ILogger logger)
        {
            try
            {
                var types = Types.GetAllTypesImplementingOpenGenericType(typeof(IMappings<>));
                var methodInfo = typeof(T).GetMethod("MapFields");

                types?.ToList().ForEach(t =>
                {
                    try
                    {
                        // https://stackoverflow.com/a/3958026/4734707
                        // Allows to map typed objects to the current collection
                        var refMethod = methodInfo?.MakeGenericMethod(t, typeof(string), t);
                        refMethod.Invoke(obj, null);
                    }
                    catch (Exception e)
                    {
                        logger?.LogError(e, "Fail to apply mapping settings of type {type}", t.ToString());
                    }
                });
            }
            catch (Exception e)
            {
                logger?.LogError(e, "Error on {methodname}", nameof(PutMappingSettings));
            }
        }

        public static bool IsUnrecoverable(this ElasticsearchClientException exception)
            => new[] { PipelineFailure.BadAuthentication, PipelineFailure.SniffFailure, PipelineFailure.CouldNotStartSniffOnStartup,
                            PipelineFailure.MaxTimeoutReached, PipelineFailure.MaxRetriesReached, PipelineFailure.Unexpected, PipelineFailure.NoNodesAttempted,
                            PipelineFailure.FailedProductCheck}.Contains(exception.FailureReason.GetValueOrDefault());
   

    }
}
