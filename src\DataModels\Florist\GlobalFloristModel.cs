﻿using ITF.SharedModels.Group.Enums;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using MongoDB.Driver.GeoJsonObjectModel;
using Newtonsoft.Json.Converters;
using static ITF.SharedModels.Messages.Italy.Florist.Legacy.Messages.V1;
using static ITF.SharedModels.Messages.Italy.Document.Legacy.Messages.V1;
using Newtonsoft.Json;
using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedModels.Messages.Italy.Florist.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using System.Diagnostics.CodeAnalysis;
using System;
using ITF.Lib.Common.Notifications.Messages;

namespace ITF.SharedModels.DataModels.Florist;

[BsonIgnoreExtraElements]
public class GlobalFloristModel : BaseProjectedEvents, IEquatable<GlobalFloristModel>
{
    public List<Accessory> Accessories { get; set; } = new();
    public List<DailyCalendar> Calendar { get; set; } = new();
    public List<Contact> Contacts { get; set; } = new();
    public string PersonalCode { get; set; }
    public string UnitCountryCode { get; set; }
    public List<Document> Documents { get; set; } = new();
    public FloristActivity FloristActivity { get; set; } = new();
    public string FloristId { get; set; }
    public string InitialLegacyPassword { get; set; }
    public ShopLocation ShopLocation { get; set; } = new();
    public List<SpecialDay> SpecialDays { get; set; } = new();
    public List<NonDeliveredPlace> NonDeliveredPlaces { get; set; } = new();
    public List<UserModel> Users { get; set; } = new();
    public List<string> DeliveryServiceProviders { get; set; } = new();
    /// <summary>
    /// List of country specific fields
    /// </summary>
    public Dictionary<string, dynamic> Attributes { get; set; } = new();

    public override void SetId() => Id = FloristId;

    public bool Equals(GlobalFloristModel parameter)
    {
        if (parameter == null)
            return false;
        return (
            FloristId == parameter.FloristId &&
            FloristActivity.Equals(parameter.FloristActivity) &&               
            Contacts.Equals(parameter.Contacts) &&
            ShopLocation.Equals(parameter.ShopLocation) &&
            Accessories.Equals(parameter.Accessories) &&
            NonDeliveredPlaces.Equals(parameter.NonDeliveredPlaces) &&
            SpecialDays.Equals(parameter.SpecialDays) &&
            Calendar.Equals(parameter.Calendar) &&
            Documents.Equals(parameter.Documents) &&
            PersonalCode.Equals(parameter.PersonalCode) &&
            Users.Equals(parameter.Users) &&
            InitialLegacyPassword.Equals(parameter.InitialLegacyPassword)
            );
    }

    public override bool Equals(object obj)
    {
        return Equals(obj as GlobalFloristModel);
    }

    public override int GetHashCode() => new
    {
        Id,
        UnitCountryCode,
        FloristId,
        PersonalCode,
        FloristActivity,
        Contacts,
        ShopLocation,
        Calendar,
        Accessories,
        NonDeliveredPlaces,
        SpecialDays,
        Documents
    }.GetHashCode();

    public static implicit operator GlobalFloristModel(LegacyFloristCreatedMessage message)
    {

        var entity = new GlobalFloristModel
        {
            FloristActivity = new FloristActivity
            {
                AgreementOn = message.Payload.FloristActivity.AgreementOn,
                LastReinstateOn = message.Payload.FloristActivity.LastReinstateOn,
                LastSuspensionOn = message.Payload.FloristActivity.LastSuspensionOn,
                LastSuspensionReason = message.Payload.FloristActivity.LastSuspensionReason,
                BlockedInExecution = message.Payload.FloristActivity.BlockedInExecution,
                BlockedInTransmission = message.Payload.FloristActivity.BlockedInTransmission,
                Deleted = message.Payload.FloristActivity.Deleted,

            },
            UnitCountryCode = message.Payload.UnitCountryCode,
            FloristId = message.Payload.FloristIdentifier,
            PersonalCode = message.Payload.PersonalCode,
            ShopLocation = new ShopLocation
            {
                City = message.Payload.ShopLocation.City,
                Province = message.Payload.ShopLocation.Province,
                CountryCode = message.Payload.ShopLocation.CountryCode,
                Name = message.Payload.ShopLocation.Name,
                Street = message.Payload.ShopLocation.Street,
                ZipCode = message.Payload.ShopLocation.ZipCode,
                Gps = new GeoJsonPoint<GeoJson2DGeographicCoordinates>(
                    new GeoJson2DGeographicCoordinates(message.Payload.ShopLocation.Gps.Longitude, message.Payload.ShopLocation.Gps.Latitude))
            }

        };

       
        if (message.Payload.Contacts != null)
        {
            foreach (var contact in message.Payload.Contacts)
            {
                entity.Contacts.Add(new Contact
                {
                    Entry = contact.Entry,
                    Type = contact.Type,
                });
            }
        }
        if (message.Payload.Accessories != null)
        {
            foreach (var accessory in message.Payload.Accessories)
            {
                entity.Accessories.Add(new Accessory
                {
                    InStock = accessory.InStock,
                    ProductCode = accessory.ProductCode,
                    ProductName = accessory.ProductName
                });
            }
        }
        if (message.Payload.Documents != null)
        {
            foreach (var document in message.Payload.Documents)
            {
                entity.Documents.Add(new Document
                {
                    DocType = document.DocType,
                    FileExtension = document.FileExtension,
                    FileName = document.FileName,
                    OrderReference = document.OrderReference,
                    Url = document.Url,
                    Year = document.Year,
                    Month = document.Month,
                });
            }
        }
        if (message.Payload.Calendar != null)
        {
            foreach (var day in message.Payload.Calendar)
            {
                entity.Calendar.Add(new DailyCalendar
                {
                    BreakTimeEnd = day.BreakTimeEnd,
                    BreakTimeStart = day.BreakTimeStart,
                    CloseHour = day.CloseHour,
                    DayOfWeek = day.DayOfWeek,
                    IsBreakTimePresent = day.IsBreakTimePresent,
                    OpenHour = day.OpenHour,
                    Type = day.Type
                });
            }
        }
        if (message.Payload.SpecialDays != null)
        {
            foreach (var day in message.Payload.SpecialDays)
            {
                entity.SpecialDays.Add(new SpecialDay
                {
                    Start = day.Start,
                    End = day.End,
                    Type = day.Type
                });
            }
        }
        if (message.Payload.Attributes != null)
        {
            foreach (var attr in message.Payload.Attributes)
            {
                entity.Attributes.Add(attr.Key, attr.Value);
            }
        }

        entity.SetId();
        return entity;
    }
}
public class NonDeliveredPlace : IEqualityComparer<NonDeliveredPlace>
{
    public string Id { get; set; } = string.Empty;
    public string ZipCode { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;

    public static implicit operator NonDeliveredPlace(NonDeliveredPlacesPayload nonDeliveredPlace)
    {
        return new NonDeliveredPlace { Id = nonDeliveredPlace.Id, City = nonDeliveredPlace.City, CountryCode = nonDeliveredPlace.CountryCode, ZipCode = nonDeliveredPlace.ZipCode };
    }
    public bool Equals(NonDeliveredPlace parameter)
    {
        if (parameter == null) return false;

        return (Id == parameter.Id &&
            ZipCode == parameter.ZipCode &&
            City == parameter.City &&
            CountryCode == parameter.CountryCode
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as NonDeliveredPlace);
    }
    public override int GetHashCode() => new
    {
        Id,
        ZipCode,
        City,
        CountryCode
    }.GetHashCode();

    public bool Equals(NonDeliveredPlace? x, NonDeliveredPlace? y)
    {
        if (x is null && y is null)
            return true;
        if (x is null || y is null)
            return false;
        return x.Equals(y);
    }

    public int GetHashCode([DisallowNull] NonDeliveredPlace obj)
    {
        if (obj is null)
            return 0;

        return obj.GetHashCode();
    }
}
public class ShopLocation
{
    public string Name { get; set; }
    public string Street { get; set; }
    public string ZipCode { get; set; }
    public string City { get; set; }
    public string Province { get; set; }
    public string CountryCode { get; set; }
    public GeoJsonPoint<GeoJson2DGeographicCoordinates> Gps { get; set; }

    public static implicit operator ShopLocation(LegacyFloristAddressUpdatedMessage message)
    {
        var entity = new ShopLocation
        {
            Street = message.Payload.Street,
            ZipCode = message.Payload.ZipCode,
            City = message.Payload.City,
            Province = message.Payload.Province,
            CountryCode = message.Payload.CountryCode,
            Gps = new GeoJsonPoint<GeoJson2DGeographicCoordinates>(
            new GeoJson2DGeographicCoordinates(message.Payload.Gps.Longitude, message.Payload.Gps.Latitude))
        };
        return entity;
    }

    public bool Equals(ShopLocation parameter)
    {
        return (Name == parameter.Name &&
            Street == parameter.Street &&
            ZipCode == parameter.ZipCode &&
            City == parameter.City &&
            Province == parameter.Province &&
            CountryCode == parameter.CountryCode &&
            Gps == parameter.Gps
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as ShopLocation);
    }
    public override int GetHashCode() => new
    {
        Name,
        Street,
        ZipCode,
        City,
        Province,
        CountryCode,
        Gps
    }.GetHashCode();
}

public class Contact
{
    [JsonConverter(typeof(StringEnumConverter))]
    [BsonRepresentation(BsonType.String)]
    public ContactTypeEnum Type { get; set; }
    public string Entry { get; set; }
    public bool Equals(Contact parameter)
    {
        return (Type == parameter.Type &&
            Entry == parameter.Entry
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as Contact);
    }
    public override int GetHashCode() => new
    {
        Type,
        Entry
    }.GetHashCode();
}

public class Accessory : IEqualityComparer<Accessory>   
{
    public string ProductCode { get; set; }
    public string ProductName { get; set; }
    public bool InStock { get; set; }

    public static implicit operator Accessory(StockReferencePayload stockeReference)
    {
        return new Accessory { ProductCode = stockeReference.ProductId, ProductName = string.Empty, InStock = stockeReference.Quantity > 0 };
    }
    public bool Equals(Accessory parameter)
    {
        if (parameter == null) return false;

        return (ProductCode == parameter.ProductCode &&
            ProductName == parameter.ProductName &&
            InStock == parameter.InStock
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as Accessory);
    }
    public override int GetHashCode() => new
    {
        ProductCode,
        ProductName,
        InStock
    }.GetHashCode();

    public bool Equals(Accessory? x, Accessory? y)
    {
        if (x is null && y is null)
            return true;
        if (x is null || y is null)
            return false;
        return x.ProductCode == y.ProductCode &&
            x.ProductName == y.ProductName &&
            x.InStock == y.InStock;
    }

    public int GetHashCode([DisallowNull] Accessory obj)
    {
        if (obj is null)
            return 0;

        int hash = 17;

        hash = hash * 31 + (obj.ProductCode?.GetHashCode() ?? 0);
        hash = hash * 31 + (obj.ProductName?.GetHashCode() ?? 0);
        hash = hash * 31 + obj.InStock.GetHashCode();



        return hash;
    }
}

public class Document
{
    [JsonConverter(typeof(StringEnumConverter))]
    [BsonRepresentation(BsonType.String)]
    public DocTypeEnum DocType { get; set; }
    public string OrderReference { get; set; }
    public string FileName { get; set; }
    public string FileExtension { get; set; }
    public string Url { get; set; } // url to call to get file bytes
    public int Year { get; set; }
    public int Month { get; set; }
    public string OctopusOrderId { get; set; }
    public string CTOrderId { get; set; }

    public static implicit operator Document(LegacyDocumentGeneratedMessage message)
    {
        var entity = new Document
        {
            OrderReference = message.Payload.OrderCode,
            Year = message.Payload.DocumentYear,
            Month = message.Payload.DocumentMonth,
            Url = message.Payload.DocumentUrl,
            DocType = message.Payload.DocType,
            FileName = message.Payload.Filename,
            FileExtension = message.Payload.FileExtension,
            OctopusOrderId = message.Payload.OctopusOrderId,
            CTOrderId = message.Payload.CTOrderId
        };

        return entity;
    }

    public bool Equals(Document parameter)
    {
        if (parameter == null) return false;

        return (DocType == parameter.DocType &&
            OrderReference == parameter.OrderReference &&
            FileName == parameter.FileName &&
            FileExtension == parameter.FileExtension &&
            Url == parameter.Url &&
            Year == parameter.Year &&
            Month == parameter.Month &&
            OctopusOrderId == parameter.OctopusOrderId &&
            CTOrderId == parameter.CTOrderId
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as Document);
    }
    public override int GetHashCode() => new
    {
        DocType,
        OrderReference,
        FileName,
        FileExtension,
        Url,
        Year,
        Month,
        OctopusOrderId,
        CTOrderId
    }.GetHashCode();
}

public class DailyCalendar : IEqualityComparer<DailyCalendar>
{
    public DayOfWeek DayOfWeek { get; set; }
    public TimeSpan? OpenHour { get; set; }
    public TimeSpan? CloseHour { get; set; }
    public bool IsBreakTimePresent { get; set; }
    public TimeSpan? BreakTimeStart { get; set; }
    public TimeSpan? BreakTimeEnd { get; set; }
    [JsonConverter(typeof(StringEnumConverter))]
    [BsonRepresentation(BsonType.String)]
    public DailyCalendarTypeEnum Type { get; set; }
    public bool Equals(DailyCalendar parameter)
    {
        if (parameter == null) return false;

        return (DayOfWeek == parameter.DayOfWeek &&
            OpenHour == parameter.OpenHour &&
            CloseHour == parameter.CloseHour &&
            IsBreakTimePresent == parameter.IsBreakTimePresent &&
            BreakTimeStart == parameter.BreakTimeStart &&
            BreakTimeEnd == parameter.BreakTimeEnd &&
            Type == parameter.Type
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as DailyCalendar);
    }
    public override int GetHashCode() => new
    {
        DayOfWeek,
        OpenHour,
        CloseHour,
        IsBreakTimePresent,
        BreakTimeStart,
        BreakTimeEnd,
        Type
    }.GetHashCode();

    public bool Equals(DailyCalendar? x, DailyCalendar? y)
    {
        if (x is null && y is null)
            return true;
        if (x is null || y is null)
            return false;
        return x.Type == y.Type &&
            x.BreakTimeEnd == y.BreakTimeEnd &&
            x.BreakTimeStart == y.BreakTimeStart &&
            x.IsBreakTimePresent == y.IsBreakTimePresent &&
            x.CloseHour == y.CloseHour &&
            x.OpenHour == y.OpenHour &&
            x.DayOfWeek == y.DayOfWeek;
    }

    public int GetHashCode([DisallowNull] DailyCalendar obj)
    {
        if (obj is null)
            return 0;

        int hash = 17;

        hash = hash * 31 + obj.Type.GetHashCode();
        hash = hash * 31 + obj.BreakTimeEnd?.GetHashCode() ?? default;
        hash = hash * 31 + obj.BreakTimeStart?.GetHashCode() ?? default;
        hash = hash * 31 + obj.IsBreakTimePresent.GetHashCode();
        hash = hash * 31 + obj.CloseHour?.GetHashCode() ?? default;
        hash = hash * 31 + obj.OpenHour?.GetHashCode() ?? default;
        hash = hash * 31 + obj.DayOfWeek.GetHashCode();



        return hash;
    }

    public static implicit operator DailyCalendar((CalendarPayload calendar, DailyCalendarTypeEnum dailyCalendarTypeEnum) src)
    {
        var entity = new DailyCalendar
        {
            Type = src.dailyCalendarTypeEnum,
            BreakTimeEnd = null,
            BreakTimeStart = null,
            IsBreakTimePresent = false,
            DayOfWeek = (DayOfWeek)src.calendar.DayOfWeek,
            CloseHour = src.calendar.WorkHourClosing.HasValue ? src.calendar.WorkHourClosing.Value.TimeOfDay : default(TimeSpan),
            OpenHour = src.calendar.WorkHourOpening.HasValue ? src.calendar.WorkHourOpening.Value.TimeOfDay : default(TimeSpan),
        };

        return entity;
    }
}

public class SpecialDay : IEqualityComparer<SpecialDay>
{
    public DateTime Start { get; set; }
    public DateTime End { get; set; }
    [JsonConverter(typeof(StringEnumConverter))]
    [BsonRepresentation(BsonType.String)]
    public SpecialDayTypeEnum Type { get; set; }
    public bool Equals(SpecialDay parameter)
    {
        if (parameter == null) return false;

        return (Start == parameter.Start &&
            End == parameter.End &&
            Type == parameter.Type
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as SpecialDay);
    }
    public override int GetHashCode() => new
    {
        Start,
        End,
        Type
    }.GetHashCode();

    public bool Equals(SpecialDay? x, SpecialDay? y)
    {
        if (x is null && y is null)
            return true;
        if (x is null || y is null)
            return false;
        return x.Start == y.Start &&
            x.End == y.End &&
            x.Type == y.Type;
    }

    public int GetHashCode([DisallowNull] SpecialDay obj)
    {
        if (obj is null)
            return 0;

        int hash = 17;

        hash = hash * 31 + obj.Start.GetHashCode();
        hash = hash * 31 + obj.End.GetHashCode();
        hash = hash * 31 + obj.Type.GetHashCode();



        return hash;
    }

    public static implicit operator SpecialDay(LegacySpecialDayAddedMessage message)
    {
        var entity = new SpecialDay
        {
            Start = message.Payload.Start,
            End = message.Payload.End,
            Type = message.Payload.Type
        };

        return entity;
    }

    public static implicit operator SpecialDay(LegacySpecialDayRemovedMessage message)
    {
        var entity = new SpecialDay
        {
            Start = message.Payload.Start,
            End = message.Payload.End,
            Type = message.Payload.Type
        };

        return entity;
    }

    public static implicit operator SpecialDay((CalendarExceptionPayload specialDay, SpecialDayTypeEnum specialDayTypeEnum) src)
    {
        var entity = new SpecialDay
        {
            Start = src.specialDay.Date,
            End = src.specialDay.Date,
            Type = src.specialDayTypeEnum
        };

        return entity;
    }
}

public class FloristActivity
{
    public DateTime AgreementOn { get; set; }
    public string LastSuspensionReason { get; set; }
    public DateTime? LastSuspensionOn { get; set; }
    public DateTime? LastReinstateOn { get; set; }
    public bool BlockedInExecution { get; set; } = false;
    public bool BlockedInTransmission { get; set; } = false;
    public bool Deleted { get; set; } = false;
    public bool Suspended { get; set; } = false;
    public bool Equals(FloristActivity parameter)
    {
        if (parameter == null) return false;

        return (AgreementOn == parameter.AgreementOn &&
            LastSuspensionReason == parameter.LastSuspensionReason &&
            LastSuspensionOn == parameter.LastSuspensionOn &&
            LastReinstateOn == parameter.LastReinstateOn &&
            BlockedInExecution == parameter.BlockedInExecution &&
            BlockedInTransmission == parameter.BlockedInTransmission &&
            Deleted == parameter.Deleted &&
            Suspended == parameter.Suspended
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as FloristActivity);
    }
    public override int GetHashCode() => new
    {
        AgreementOn,
        LastSuspensionReason,
        LastSuspensionOn,
        LastReinstateOn,
        BlockedInExecution,
        BlockedInTransmission,
        Deleted,
        Suspended
    }.GetHashCode();
}

public class FloristCredential
{
    public string CredentialType { get { return "password"; } }
    public string Password { get; set; }
    public bool Equals(FloristCredential parameter)
    {
        return (CredentialType == parameter.CredentialType &&
            Password == parameter.Password
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as FloristCredential);
    }
    public override int GetHashCode() => new
    {
        CredentialType,
        Password
    }.GetHashCode();
}

public class UserActivity
{
    public string CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string LastModifiedBy { get; set; }
    public DateTime? LastModified { get; set; }
    public bool Equals(UserActivity parameter)
    {
        return (CreatedBy == parameter.CreatedBy &&
            CreatedAt == parameter.CreatedAt &&
            LastModifiedBy == parameter.LastModifiedBy &&
            LastModified == parameter.LastModified
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as UserActivity);
    }
    public override int GetHashCode() => new
    {
        CreatedBy,
        CreatedAt,
        LastModifiedBy,
        LastModified
    }.GetHashCode();
}
public class GlobalFloristOrderPerDayModel : BaseProjectedEvents, IEquatable<GlobalFloristOrderPerDayModel>
{
    public string FloristIdentifier { get; set; }
    public DateTime DeliveryDate { get; set; }
    public int Counter { get; set; }

    public bool Equals(GlobalFloristOrderPerDayModel parameter)
    {
        return (FloristIdentifier == parameter.FloristIdentifier &&
            DeliveryDate == parameter.DeliveryDate &&
            Counter == parameter.Counter
            );
    }
    public override bool Equals(object obj)
    {
        return Equals(obj as GlobalFloristModel);
    }

    public override void SetId() => Id = FloristIdentifier + "_" + DeliveryDate.ToString("yyyyMMdd");

    public override int GetHashCode() => new
    {
        Id,
        FloristIdentifier,
        DeliveryDate,
        Counter
    }.GetHashCode();

}


