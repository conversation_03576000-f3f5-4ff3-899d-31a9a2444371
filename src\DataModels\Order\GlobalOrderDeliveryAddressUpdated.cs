﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveryAddressUpdated
    {
        public string OrderIdentifier { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public static implicit operator GlobalOrderDeliveryAddressUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryAddressUpdatedMessage v)
        {
            return new GlobalOrderDeliveryAddressUpdated
            {
                Address = v?.Payload?.Address,
                City = v?.Payload?.City,
                ZipCode = v?.Payload?.ZipCode,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
                Latitude = v.Payload.Latitude,
                Longitude = v.Payload.Longitude
            };
        }
    }
}
