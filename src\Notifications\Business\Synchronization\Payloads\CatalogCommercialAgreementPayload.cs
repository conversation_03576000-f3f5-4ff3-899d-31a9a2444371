﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class CatalogCommercialAgreementPayload : IPayload
    {
        public List<CatalogCommercialAgreement> CatalogCommercialAgreements { get; set; } = new List<CatalogCommercialAgreement>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class CatalogCommercialAgreement
    {
        public long PriceDiscTableRecId { get; set; }
        public string ProductNumber { get; set; }
        public string Size { get; set; }
        public string Style { get; set; }
        public string Channel { get; set; }
        public double PrivexMin { get; set; }
        public string Currency { get; set; }
        public double Privex { get; set; }
        public string Unit { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime LastModified { get; set; }
        public DateTime FromDate { get; set; }
        public double TheoricalMargin { get; set; }
        public double PrivexMax { get; set; }
        public double Price { get; set; }
        public double MarketingComplement { get; set; }
    }
}
