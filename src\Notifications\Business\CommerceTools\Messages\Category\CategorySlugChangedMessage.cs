﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.CommerceTools.Payloads.Category;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Category
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class CategorySlugChangedMessage : BaseMessage<CategorySlugChangedPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.CategorySlugChangedMessage?.Resource?.Id;
            }
        }
    }
}
