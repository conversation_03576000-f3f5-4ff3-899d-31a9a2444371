﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class FloristContactsPayload : IPayload
    {
        public List<FloristContact> FloristContacts { get; set; } = new List<FloristContact>();

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class FloristContact
    {
        public string PartyNumber { get; set; }
        public string FloristId { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Value { get; set; }
        public string Purpose { get; set; }
        public bool IsPrimary { get; set; }
        public DateTime LastModified { get; set; }
    }
}
