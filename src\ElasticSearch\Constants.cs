﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.ElasticSearch
{
    public static class Constants
    {
        public const string NORMALIZER = "case_insensitive";
        public const string KEYWORD = "keyword";
        public const string SEARCH_AS_YOU_TYPE = "searchasyoutype";
        public const string LOWERCASE = "lowercase";
    }
}
