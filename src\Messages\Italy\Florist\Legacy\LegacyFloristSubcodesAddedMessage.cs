﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristSubcodesAddedMessage : BaseMessage<LegacyFloristSubcodesAddedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.MasterFloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyFloristSubcodesAddedPayload : LegacyPayload, IEquatable<LegacyFloristSubcodesAddedPayload>
    {
        public string MasterFloristIdentifier { get; set; }
        public List<string> SlaveFloristCodes { get; set; }

        public bool Equals(LegacyFloristSubcodesAddedPayload parameter)
        {
            return (MasterFloristIdentifier == parameter.MasterFloristIdentifier &&
                SlaveFloristCodes == parameter.SlaveFloristCodes
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristSubcodesAddedPayload);
        }

        public override int GetHashCode() => new
        {
            MasterFloristIdentifier,
            SlaveFloristCodes
        }.GetHashCode();
    }
}
