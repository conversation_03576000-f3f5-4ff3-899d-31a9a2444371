﻿using System;

namespace ITF.SharedLibraries.Geocoding
{
    public class Exceptions
    {
        public class GeocodingProviderTransientException : Exception
        {
            public GeocodingProviderTransientException()
            {
            }
            public GeocodingProviderTransientException(string message)
                : base(message)
            {
            }

            public GeocodingProviderTransientException(string message, Exception inner)
                : base(message, inner)
            {
            }
        }

        public class GeocodingProviderNoMatchingResultException : Exception
        {
            public GeocodingProviderNoMatchingResultException()
            {
            }
            public GeocodingProviderNoMatchingResultException(string message)
                : base(message)
            {
            }

            public GeocodingProviderNoMatchingResultException(string message, Exception inner)
                : base(message, inner)
            {
            }
        }

        public class GeocodingException : Exception
        {
            public GeocodingException()
            {
            }
            public GeocodingException(string message)
                : base(message)
            {
            }

            public GeocodingException(string message, Exception inner)
                : base(message, inner)
            {
            }
        }
    }
}
