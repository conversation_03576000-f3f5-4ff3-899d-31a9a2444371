﻿namespace ITF.SharedModels.Events.Domain.Florist
{
    public class ShopLocation
    {
        public string ShopName { get; set; }
        public string ShopStreet { get; set; }
        public string ShopCity { get; set; }
        public string ShopPostalCode { get; set; }
        public string ShopCountry { get; set; }
        public float ShopLatitude { get; set; }
        public float ShopLongitude { get; set; }

        public ShopLocation(string shopName, string shopStreet, string shopCity, string shopPostalCode, string shopCountry, float shopLatitude, float shopLongitude)
        {
            ShopName = shopName;
            ShopStreet = shopStreet;
            ShopCity = shopCity;
            ShopPostalCode = shopPostalCode;
            ShopCountry = shopCountry;
            ShopLatitude = shopLatitude;
            ShopLongitude = shopLongitude;
        }
    }
}
