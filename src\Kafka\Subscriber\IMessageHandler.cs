﻿using Confluent.Kafka;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Kafka.Subscriber
{
    public interface IMessageHandler
    {
        Task HandleMessage(object data, string topic = null, int? partition = null, long? offset = null);
        Task HandleError(Error e) { return Task.CompletedTask; }
        Task HandleLog(LogMessage l) { return Task.CompletedTask; }
        Task HandleStatistics(string s) { return Task.CompletedTask; }
        Task HandleTopicsPartitionsAssigned(List<TopicPartition> topics) { return Task.CompletedTask; }
        Task HandleTopicsPartitionsRevoked(List<TopicPartitionOffset> topics) { return Task.CompletedTask; }
        Task HandleOffsetCommited(CommittedOffsets offset) { return Task.CompletedTask; }
        Task HandleExceptions(Exception e) { return Task.CompletedTask; }
    }
}
