﻿using System;
using System.Collections.Generic;
using System.Linq;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Emarsys.Models.Entity;

namespace ITF.SharedLibraries.Emarsys;
public static partial class Messages
{
    public static partial class V1
    {

        public class DeliveryReassuranceEmailMessage : BaseMessage<DeliveryReassuranceEmailPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.ReassuranceEmails?.FirstOrDefault()?.Customer?.Email;
        }
    }
}

public class DeliveryReassuranceEmailPayload : IPayload
{
    public List<ReassuranceEmail> ReassuranceEmails { get; set; } = new List<ReassuranceEmail>();
    public string EventID { get; set; } = Guid.NewGuid().ToString();
    public DateTime EventDate { get; set; } = DateTime.Now;
}
