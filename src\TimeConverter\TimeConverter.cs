﻿using Microsoft.Extensions.Logging;
using System;
using TimeZoneConverter;

namespace ITF.SharedLibraries.TimeConverter
{
    public class TimeConverter : ITimeConverter
    {
        private readonly ILogger<TimeConverter> _logger;

        public TimeConverter(ILogger<TimeConverter> logger)
        {
            _logger = logger;
        }

        public DateTime GetDateFromCountry(DateTime date, string country = "FR")
        {
            string cultureStr = country switch
            {
                "FR" => "Romance Standard Time",
                _ => "Romance Standard Time",
            };

            try
            {
                TimeZoneInfo cstZone = TimeZoneInfo.FindSystemTimeZoneById(TZConvert.WindowsToIana(cultureStr));
                DateTime dt = date;
                dt = DateTime.SpecifyKind(dt, DateTimeKind.Unspecified);
                return TimeZoneInfo.ConvertTimeFromUtc(dt, cstZone);
            }
            catch (InvalidTimeZoneException)
            {
                try
                {
                    TimeZoneInfo cstZone = TimeZoneInfo.FindSystemTimeZoneById(cultureStr);
                    DateTime dt = date;
                    dt = DateTime.SpecifyKind(dt, DateTimeKind.Unspecified);
                    return TimeZoneInfo.ConvertTimeFromUtc(dt, cstZone);
                }
                catch (Exception e)
                {
                    _logger?.LogError(e, "The corresponding timezone to {countryCode} can't be found", country);
                    return date;
                }
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "The corresponding timezone to {countryCode} can't be found", country);
                return date;
            }
        }

        public DateTime GetDateTimeNowUTC()
            => DateTime.UtcNow;

        public DateTime GetDateTimeInUTC(DateTime date)
            => date.ToUniversalTime();
    }
}
