﻿using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;

namespace ITF.SharedLibraries.EnvironmentVariable
{
    public static class EnvironmentVariable
    {
        public static T Get<T>(string envVar)
        {
            var enVarStr = Environment.GetEnvironmentVariable(envVar);

            if (enVarStr is null)
                throw new Exception($"The environment variable {envVar} is not available, the application can't works properly !");

            return JsonConvert.DeserializeObject<T>(enVarStr);
        }

        public static string GetStringOrThrow(string envVar)
        {
            var enVarStr = Environment.GetEnvironmentVariable(envVar);

            if (enVarStr is null)
                throw new Exception($"The environment variable {envVar} is not available, the application can't works properly !");

            return enVarStr;
        }

        public static string GetStringOrThrow(this IConfiguration config, string configName)
        {
            var configVar = config.GetValue<string>(configName);
            if (string.IsNullOrWhiteSpace(configVar))
                throw new Exception($"The application can't run, mandatory variables ({configName}) is not available from IConfiguration !");

            return configVar;
        }

        public static string GetString(this IConfiguration config, string configName) => 
            string.IsNullOrWhiteSpace(config.GetValue<string>(configName)) ? null : config.GetValue<string>(configName);


        public static T Get<T>(this IConfiguration config, string configName)
        {
            var result = config.GetSection(configName).Get<T>();

            if (result != null)
                return result;

            var enVarStr = config.GetStringOrThrow(configName);
            return JsonConvert.DeserializeObject<T>(enVarStr);
        }
    }
}
