﻿using System.Collections.Generic;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;

public class ContactPayload
{
    public DataObject data { get; set; }
    public int replyCode { get; set; }
    public string replyText { get; set; }

    public class DataObject
    {
        public List<ErrorEmarsys> errors { get; set; } = new List<ErrorEmarsys>();
        public List<Contact> result { get; set; } = new List<Contact>();
    }

    public class ErrorEmarsys
    {
        public string key { get; set; }
        public int errorCode { get; set; }
        public string errorMsg { get; set; }
    }
}

public class GetContactPayload
{
    public string keyId { get; set; }
    public List<string> fields { get; set; }
    public List<string> keyValues { get; set; }

    public GetContactPayload(string keyId, List<string> fields, string uniqueId)
    {
        this.keyId = keyId;
        this.fields = fields;
        keyValues = new List<string>
        {
            uniqueId
        };
    }
    public GetContactPayload(string keyId, List<string> fields, List<string> uniqueIds)
    {
        this.keyId = keyId;
        this.fields = fields;
        keyValues = uniqueIds;
    }
}

public class PutContactPayload
{
    public string keyId { get; set; }
    public List<Dictionary<string, string>> contacts { get; set; }

    public PutContactPayload(string keyId, List<Dictionary<string, string>> contacts)
    {
        this.keyId = keyId;
        this.contacts = contacts;
    }
}

public class ContactCreationPayload
{
    public DataObject data { get; set; }
    public int replyCode { get; set; }
    public string replyText { get; set; }

    public class DataObject
    {
        public List<ErrorEmarsys> errors { get; set; } = new List<ErrorEmarsys>();
        public List<string> ids { get; set; } = new List<string>();
    }

    public class ErrorEmarsys
    {
        public string key { get; set; }
        public int errorCode { get; set; }
        public string errorMsg { get; set; }
    }
}

public class ContactCreationBatchPayload
{
    public DataObject data { get; set; }
    public int replyCode { get; set; }
    public string replyText { get; set; }

    public class DataObject
    {
        public Dictionary<string, List<ErrorEmarsys>> errors { get; set; } = new();
        public List<string> ids { get; set; } = new();
    }

    public class ErrorEmarsys
    {
        public string key { get; set; }
        public int errorCode { get; set; }
        public string errorMsg { get; set; }
    }
}
