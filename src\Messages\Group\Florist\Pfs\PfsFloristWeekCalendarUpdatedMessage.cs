﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.Group.Florist.Pfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristWeekCalendarUpdatedMessage :  BaseMessage<PfsFloristWeekCalendarUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                                   => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristWeekCalendarUpdatedMessage(string floristIdentifier, List<DailyCalendar> calendar)
                {
                    this.Payload = new PfsFloristWeekCalendarUpdatedPayload { FloristIdentifier = floristIdentifier, Calendar = calendar };
                }
            }
        }
    }

    public class PfsFloristWeekCalendarUpdatedPayload : LegacyPayload, IEquatable<PfsFloristWeekCalendarUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public List<DailyCalendar> Calendar { get; set; }

        public bool Equals(PfsFloristWeekCalendarUpdatedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier && 
                    Calendar == parameter.Calendar);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristWeekCalendarUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Calendar
        }.GetHashCode();
    }
}