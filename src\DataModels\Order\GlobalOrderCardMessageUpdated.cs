﻿using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderCardMessageUpdated
    {
        public string OrderIdentifier { get; set; }
        public string CardMessage { get; set; }

        public static implicit operator GlobalOrderCardMessageUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderCardMessageUpdatedMessage v)
        {
            return new GlobalOrderCardMessageUpdated
            {
                CardMessage = v?.Payload?.CardMessage,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
