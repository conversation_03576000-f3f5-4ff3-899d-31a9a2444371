﻿using System;
using System.Collections.Generic;
using System.Text;
using VaultSharp;
using VaultSharp.V1.AuthMethods;
using VaultSharp.V1.AuthMethods.Token;
using System.Threading.Tasks;
using System.Linq;
using System.Text.Json;
using Newtonsoft.Json;
using VaultSharp.V1.AuthMethods.AppRole;

namespace ITF.SharedLibraries.HashicorpVault
{
    public class HashicorpVaultClient : IHashicorpVaultClient
    {
        private readonly Configuration _configuration;
        private readonly IVaultClient _vaultClient;

        public HashicorpVaultClient(Configuration configuration)
        {
            _configuration = configuration;

            IAuthMethodInfo authMethod =
                new AppRoleAuthMethodInfo(configuration.Role_id, configuration.Secret_id);

            var vaultClientSettings = new VaultClientSettings(configuration.Url, authMethod);
            _vaultClient = new VaultClient(vaultClientSettings);
        }

        public async Task<T> GetAsync<T>(string key, string mountPoint = null) where T : class
        {
            var secret = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync($"{_configuration.Path}/{key}", mountPoint: mountPoint is null ? _configuration.MountPoint : mountPoint);
            if (typeof(T) == typeof(string))
                return secret.Data.Data.Values.FirstOrDefault() as T;

            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(secret.Data.Data));
        }
    }
}
