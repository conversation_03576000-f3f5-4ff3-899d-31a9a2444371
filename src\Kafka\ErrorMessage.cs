﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Kafka;
public class ErrorMessage : IClrType, IDistributedTracing
{
    public string ClrType { get; set; }
    public string DistributedTracingData { get; set; }
    public string CausationId { get; set; }
    public string MessageId { get; set; }
    public string Key { get; set; } // the routing key for helping ka<PERSON><PERSON> regroup same group of messages into the same partition - optional

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string Severity { get; set; }
    public string ErrorType { get; set; }
    public string BoundedContext { get; set; }
    public string RelatedMs { get; set; }
    public string RelatedMethod { get; set; }
    public string RelateProcess { get; set; }
    public string RelatedKafkaMessage { get; set; }
    public string ExceptionCaptured { get; set; }
    public string DataCaptured { get; set; }
    public string Message { get; set; }


}
