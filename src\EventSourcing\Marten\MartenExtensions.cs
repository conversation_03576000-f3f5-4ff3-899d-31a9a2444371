﻿using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.EventSourcing.Interfaces;
using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using ITF.SharedLibraries.EventSourcing.Projections.Services;
using ITF.SharedLibraries.EventSourcing.Repository;
using ITF.SharedLibraries.EventSourcing.Services;
using JasperFx.CodeGeneration;
using Marten;
using Marten.Events;
using Marten.Events.Daemon.Resiliency;
using Marten.Services;
using Marten.Services.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using Weasel.Core;
using static ITF.SharedLibraries.EventSourcing.Projections.DelegateHandler;

namespace ITF.SharedLibraries.EventSourcing.Marten
{
    public static class MartenExtensions
    {
        public static IServiceCollection AddMartenEventStore(this IServiceCollection services, IConfiguration config, Action<StoreOptions> configureOptions = null, string varEnv = "EventStore")
        {
            var configuration = config.Get<Configuration>(varEnv);
            services.Configure<Configuration>(config.GetSection(varEnv));

            services.AddSingleton(configuration);
            services.AddMarten(sp => SetStoreOptions(configuration, configureOptions))
                .UseLightweightSessions()
                .ApplyAllDatabaseChangesOnStartup()
                .AddAsyncDaemon(DaemonMode.HotCold);

            services.AddSingleton<IAggregateStoreRepository, MartenRepository>();

            return services;
        }

        private static StoreOptions SetStoreOptions(Configuration configuration, Action<StoreOptions> configureOptions = null)
        {
            var options = new StoreOptions();
            options.Connection(configuration.ConnectionString);
            options.AutoCreateSchemaObjects = AutoCreate.CreateOrUpdate;

            options.UseDefaultSerialization(serializerType: SerializerType.SystemTextJson);

            options.Serializer(new SystemTextJsonSerializer
            {
                EnumStorage = EnumStorage.AsString,
                Casing = Casing.Default
            });

            options.Events.MetadataConfig.CausationIdEnabled = true;
            options.Events.MetadataConfig.CorrelationIdEnabled = true;

            options.Events.StreamIdentity = StreamIdentity.AsString;
            options.GeneratedCodeMode = TypeLoadMode.Auto;

            options.RetryPolicy(DefaultRetryPolicy.Times(5));

            configureOptions?.Invoke(options);

            return options;
        }

        public static IServiceCollection AddMartenProjectionsManager(this IServiceCollection services, ProjectionHandler checkPointHandler, params ProjectionHandler[] projectionDelegateHandlers)
        {
            checkPointHandler.Invoke(services);
            projectionDelegateHandlers.ToList().ForEach(h =>
            {
                // Resolve the promise
                h.Invoke(services);
            });
            services.AddSingleton<ISubscriptionManager, MartenProjectionManager>();

            // Allows to access the background service as singleton and stop it
            services.AddSingleton<EventSubscriberService>();
            return services;
        }
    }
}
