﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace ITF.SharedLibraries.Sinch
{
    public static class Extensions
    {
        public static IServiceCollection AddSinchService(this IServiceCollection app, IConfiguration config, string varEnv)
        {
            SinchConfig? sinchConfig = config.GetSection(varEnv).Get<SinchConfig?>();

            if (sinchConfig != null)
            {
                app.AddSingleton(sinchConfig);
                return app.AddScoped<ISinchService, SinchService>();
            }
            else
                throw new InvalidOperationException("Sinch config not found with key '" + varEnv + "'");
        }
    }
}
