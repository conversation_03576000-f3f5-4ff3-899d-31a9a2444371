﻿using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;

public class BillingAddressEmail
{
  [SwaggerSchema("Customer's First Name")]
  public string FirstName { get; set; }

  [SwaggerSchema("Customer's Last Name")]
  public string LastName { get; set; }

  [SwaggerSchema("Customer's Civility")]
  public string Salutation { get; set; }

  [SwaggerSchema("Customer's FiscalCode")]
  public string FiscalCode { get; set; }

  [SwaggerSchema("Customer's Address")]
  public string Street { get; set; }

  [SwaggerSchema("Customer's Address")]
  public string AdditionalAddress { get; set; }

  [SwaggerSchema("Customer's Address")]
  public string ZipCode { get; set; }

  [SwaggerSchema("Customer's Address")]
  public string City { get; set; }

  [SwaggerSchema("Customer's Address")]
  public string Region { get; set; }

  [SwaggerSchema("Customer's Address in 2-letters format (e.g. FR, ES, PT, IT, SE, DK)")]
  public string Country { get; set; }
}
