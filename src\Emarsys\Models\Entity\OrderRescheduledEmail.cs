﻿using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity
{
    public class OrderRescheduledEmail
    {
        [SwaggerSchema(Nullable = false)]
        public string OrderId { get; set; }

        [SwaggerSchema(Format = "yyyy-MM-dd")]
        public string OldDeliveryDate { get; set; }

        [SwaggerSchema(Format = "yyyy-MM-dd")]
        public string NewDeliveryDate { get; set; }

        public CustomerEmail Customer { get; set; } = new();
    }
}
