﻿using Newtonsoft.Json;

namespace ITF.SharedLibraries.RAO.DTO
{
    [Serializable]
    public class InitDeliveryCourierRequestDto
    {
        [JsonProperty("orderId")]
        public string OrderId { get; set; } = string.Empty;
        [JsonProperty("jobId")]
        public string JobId { get; set; } = string.Empty;
        [JsonProperty("price")]
        public double Price { get; set; }
        [JsonProperty("scheduled")]
        public bool Scheduled { get; set; }
        [JsonProperty("scheduledDate")]
        public string ScheduledDate { get; set; } = string.Empty;
        [JsonProperty("provider")]
        public string Provider { get; set; } = string.Empty;
    }
}
