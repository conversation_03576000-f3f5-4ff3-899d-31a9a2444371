﻿using Elastic.Apm.SerilogEnricher;
using Elastic.CommonSchema.Serilog;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.OpenTracing.Extensions;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using Serilog.Core;
using Serilog.Sinks.Elasticsearch;
using System;
using System.Collections.Generic;

namespace ITF.SharedLibraries.Logging
{
    public static class LoggerFactory
    {
        public static Logger GetELKLogger(IConfiguration config, string varEnv = "ElasticSearchLog")
        {
            var configuration = config.Get<Configuration>(varEnv);

            return new LoggerConfiguration()
            .ReadFrom.Configuration(config)
            .Enrich.WithElasticApmCorrelationInfo()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(configuration.ElasticSearchLog))
            {
                AutoRegisterTemplate = true,
                IndexFormat = "mslogs-{0:yyyy.MM.dd}",
                DetectElasticsearchVersion = true,
                RegisterTemplateFailure = RegisterTemplateRecovery.IndexAnyway,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                FailureCallback = (l,e) => Console.WriteLine($"Unable to submit event {l?.RenderMessage()} to ElasticSearch. Exception : " + e?.ToString()),
                EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                                        EmitEventFailureHandling.WriteToFailureSink |
                                        EmitEventFailureHandling.RaiseCallback,
                BufferCleanPayload = (failingEvent, statuscode, exception) =>
                {
                    dynamic e = JObject.Parse(failingEvent);
                    return JsonConvert.SerializeObject(new Dictionary<string, object>()
                        {
                            { "action", "DeniedByElasticSearch"},
                            { "@timestamp",e["@timestamp"]},
                            { "level","Error"},
                            { "message","Error: "+e.message},
                            { "messageTemplate",e.messageTemplate},
                            { "failingStatusCode", statuscode},
                            { "failingException", exception}
                        });
                },
                CustomFormatter = new EcsTextFormatter()
            })
            .CreateLogger();
        }

        public static Logger GetLogger(IConfiguration config) => 
            new LoggerConfiguration()
                .ReadFrom.Configuration(config)
                .CreateLogger();
       

        public static Logger GetOpenTracingLogger(IConfiguration config) =>  
            new LoggerConfiguration()
            .ReadFrom.Configuration(config)
            .WriteTo.OpenTracing()
            .CreateLogger();
        
    }
}
