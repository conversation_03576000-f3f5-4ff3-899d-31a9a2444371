﻿using EventStore.Client;
using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using ITF.SharedLibraries.MongoDB;
using ITF.SharedLibraries.MongoDB.Repository;
using MongoDB.Driver;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Projections.Repositories
{
    public class MongoDbEventStoreCheckPointRepository<T> : MongoRepository<T>, IEventStoreCheckpointStore where T : MongoEventStoreDbCheckPointPosition, new()
    {
        private readonly Configuration _configuration;

        public MongoDbEventStoreCheckPointRepository(Configuration configuration, IMongoClient mongoClient) : base(mongoClient, configuration.DatabaseName, configuration.CollectionName)
        {
            _configuration = configuration;
        }

        public async Task<Position> GetCheckpoint()
        {
            var result = await FindByIdAsync(nameof(MongoEventStoreDbCheckPointPosition));
            if (result is null)
            {
                var position = Position.Start;
                await StoreCheckpoint(position);
                return position;
            }
            return new Position(result.CommitPosition, result.PreparePosition);
        }

        public async Task StoreCheckpoint(Position checkpoint)
        {
            var result = new T
            {
                CommitPosition = checkpoint.CommitPosition,
                PreparePosition = checkpoint.PreparePosition,
                Id = nameof(MongoEventStoreDbCheckPointPosition)
            };
            await ReplaceOneAsync(result);
        }
    }
}
