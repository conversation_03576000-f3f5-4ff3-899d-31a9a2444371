﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ITF.SharedLibraries.ExtensionMethods
{
    public static class Batch
    {
        public static IEnumerable<IEnumerable<T>> GetBatch<T>(
        this IEnumerable<T> source, int size)
        {
            T[] bucket = null;
            var count = 0;

            foreach (var item in source ?? new List<T>())
            {
                bucket ??= new T[size];

                bucket[count++] = item;

                if (count != size)
                    continue;

                yield return bucket.Select(x => x);

                bucket = null;
                count = 0;
            }

            // Return the last bucket with all remaining elements
            if (bucket != null && count > 0)
            {
                Array.Resize(ref bucket, count);
                yield return bucket.Select(x => x);
            }
        }

        public static IEnumerable<IEnumerable<T>> Split<T>(this IEnumerable<T> list, int size)
        {
            for (var i = 0; i < (float)list.Count() / size; i++)
            {
                yield return list.Skip(i * size).Take(size);
            }
        }
    }
}
