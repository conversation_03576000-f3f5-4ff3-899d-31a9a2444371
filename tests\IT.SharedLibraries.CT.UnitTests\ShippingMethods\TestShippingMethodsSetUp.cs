﻿using commercetools.Base.Client;
using commercetools.Sdk.Api;
using IT.SharedLibraries.CT.ShippingMethods;
using IT.SharedLibraries.CT.Zones;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace IT.SharedLibraries.CT.UnitTests.ShippingMethods
{
    public class TestShippingMethodsSetUp
    {
        //public IConfiguration Configuration { get; }
        public TestShippingMethodsSetUp()
        {
            //Configuration = configuration;
        }
        private static IConfiguration GetConfiguration()
        {
            return new ConfigurationBuilder().
                AddJsonFile("appsettings.json").
                AddEnvironmentVariables().
                Build();
        }

        protected WebApplication GetWebApplication()
        {
            var builder = WebApplication.CreateBuilder();
            builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            builder.Services.UseCommercetoolsApi(GetConfiguration(), "Client");

            var clientConfiguration = GetConfiguration().GetSection("Client").Get<ClientConfiguration>();
            Settings.SetCurrentProjectKey(clientConfiguration.ProjectKey);

            builder.Services.AddSingleton<IZoneService, ZoneService>();
            builder.Services.AddSingleton<IShippingMethodService, ShippingMethodService>();
            var app = builder.Build();
            return app;
        }
    }
}
