﻿namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderAssignationRemoved
    {
        public string FloristIdentier { get; set; }
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderAssignationRemoved(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignationRemovedMessage v)
        {
            return new GlobalOrderAssignationRemoved
            {
                FloristIdentier = v?.Payload?.FloristIdentifier,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
            };
        }
    }
}
