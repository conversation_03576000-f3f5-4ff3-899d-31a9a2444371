﻿using commercetools.Sdk.Api.Models.Orders;
using IT.SharedLibraries.CT.Orders;
using System.Collections.Generic;
using System.Threading.Tasks;
using static ITF.SharedModels.Messages.France.Order.Messages.V1;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace IT.Microservices.OrderReactor.Domain
{
    public interface IOrderUseCase
    {
        Task SychronizeProcess(LegacyOrderCreatedMessage message);
        Task SychronizeProcess(LegacyOrderAssignedMessage message);
        Task SychronizeProcess(LegacyOrderCancelledMessage message);
        Task SychronizeProcess(LegacyOrderDeliveryTimeUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveryStatusUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveryCostUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveryDateUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderCardMessageUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveryAddressUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderNotesUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderRecipientCoordinatesUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveredOnBehalfMessage message);
        Task SychronizeProcess(LegacyOrderAcceptedMessage message);
        Task SychronizeProcess(LegacyOrderRejectedMessage message);
        Task SychronizeProcess(LegacyOrderDeliveredMessage message);
        Task SychronizeProcess(LegacyOrderItemUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderItemExecutorAmountUpdatedMessage message);
        Task SychronizeProcess(OrderReclamationMessage message);
        Task SychronizeProcess(OrderCustomerFeedbackMessage message);
        Task SychronizeProcess(LegacyOrderAssignationRemovedMessage message);
        Task SychronizeProcess(LegacyOrderAcceptedOnBehalfMessage message);
        Task SychronizeProcess(LegacyOrderRejectedOnBehalfMessage message);
        Task SychronizeProcess(LegacyOrderSentMessage message);
        Task SychronizeProcess(LegacyOrderRecipientNameUpdatedMessage message);
        Task SychronizeProcess(LegacyOrderRecipientPhoneNumberUpdatedMessage message);

        // France RAO Legacy Messages
        Task SychronizeProcess(OrderPlacedMessage message);
        Task SychronizeProcess(OrderAssignmentMessage message);
        Task SychronizeProcess(OrderUpdatedMessage message);
        Task SychronizeProcess(OrderManagementStatusMessage message);
        Task SychronizeProcess(OrderDeliveryCourierUpdatedMessage message);
        Task SychronizeProcess(OrderDeliveryCourierResetedMessage message);
        Task SychronizeProcess(OrderDeliveryCourierInitializedMessage message);
        Task SychronizeProcess(InvoiceMessage message);
        Task<GetFloristCounterPerDayResult> GetFloristCounterPerDay(string? orderId, string? floristId);
        void LogFieldDifference(KeyValuePair<OrderDifference, object> field, IOrder order);
    }
}
