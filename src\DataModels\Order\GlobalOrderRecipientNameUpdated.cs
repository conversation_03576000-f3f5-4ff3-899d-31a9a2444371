﻿namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderRecipientNameUpdated
    {
        public string OrderIdentifier { get; set; }
        public string RecipientFirstName { get; set; }
        public string RecipientLastName { get; set; }

        public static implicit operator GlobalOrderRecipientNameUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderRecipientNameUpdatedMessage v)
        {
            return new GlobalOrderRecipientNameUpdated { 
                OrderIdentifier = v?.Payload.OrderIdentifier,
                RecipientFirstName = v?.Payload?.RecipientFirstName,
                RecipientLastName = v?.Payload?.RecipientLastName,
            };
        }
    }
}
