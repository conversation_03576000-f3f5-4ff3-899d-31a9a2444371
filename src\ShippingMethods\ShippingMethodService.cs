﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.ShippingMethods;
using commercetools.Sdk.Api.Models.Zones;
using commercetools.Sdk.Api.Serialization;
using IT.SharedLibraries.CT.Exceptions;
using IT.SharedLibraries.CT.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.ShippingMethods
{
    public class ShippingMethodService : IShippingMethodService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ShippingMethodService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;
        private readonly IOptionsMonitor<CommerceToolCustomSettings> _commonSettings;

        public ShippingMethodService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ShippingMethodService> logger, SerializerService serializerService,
            IOptionsMonitor<CommerceToolCustomSettings> commonSettings)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
            _commonSettings = commonSettings;
        }
        public async Task<IShippingMethodPagedQueryResponse> GetByCartId(string cartId)
        {
            IShippingMethodPagedQueryResponse shippingMethod = null;
            try
            {
                shippingMethod = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ShippingMethods()
                    .MatchingCart()
                    .Get()
                    .WithCartId(cartId)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping methods for cart with id {cartId}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving shipping methods for cart with id {cartId} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping methods for cart with id {cartId} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return shippingMethod;
        }

        public async Task<IShippingMethod> GetByKey(string key)
        {
            IShippingMethod shippingMethod = null;
            try
            {
                shippingMethod = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ShippingMethods()
                    .WithKey(key)
                    .Get()
                    .WithExpand("zoneRates[*].zone")
                    .WithExpand("taxCategory")
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping method with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving shipping method with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping method with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return shippingMethod;
        }

        public async Task<IShippingMethod> UpdateSetFixedShippingRate(string key, Zone zone, decimal newFixedShippingRate, string currencyCode)
        {
            if (zone == null)
            {
                _logger.LogWarning($"Zone cannot be null");
                return null;
            }

            IShippingMethod shippingMethod = await GetByKey(key);

            if (shippingMethod.ZoneRates.Any(z => z.Zone.Id == zone.Id) == false)
            {
                _logger.LogWarning($"Shipping method with key {key} doesn't have any zone rate available");
                return null;
            }

            IShippingRate currentShippingRate = shippingMethod.ZoneRates.FirstOrDefault(z => z.Zone.Id == zone.Id).ShippingRates.FirstOrDefault();

            List<IShippingMethodUpdateAction> actions = new List<IShippingMethodUpdateAction>();
            actions.Add(new ShippingMethodRemoveShippingRateAction
            {
                Zone = new ZoneResourceIdentifier { Key = zone.Key },
                ShippingRate = new ShippingRateDraft { Price = new Money { CentAmount = currentShippingRate.Price.CentAmount, CurrencyCode = currentShippingRate.Price.CurrencyCode } }
            });
            actions.Add(new ShippingMethodAddShippingRateAction
            {
                Zone = new ZoneResourceIdentifier { Key = zone.Key },
                ShippingRate = new ShippingRateDraft { Price = new Money { CentAmount = (long)(newFixedShippingRate * 100), CurrencyCode = currencyCode } }
            });

            IShippingMethodUpdate update = new ShippingMethodUpdate
            {
                Actions = actions,
                Version = shippingMethod.Version,
            };

            try
            {
                shippingMethod = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ShippingMethods()
                    .WithKey(key)
                    .Post(update)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while updating shipping method fixed rate with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving shipping method fixed rate with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping method fixed rate with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }

            return shippingMethod;
        }

        public async Task<decimal> GetInternationalDeliveryFeeForPfs()
        {
            string key = _commonSettings.CurrentValue.OutboundOrderShippingMethodKey;
            IShippingMethod shippingMethod = await GetByKey(key);
            decimal deliveryCost = 0;
            if (shippingMethod == null)
            {
                _logger.LogWarning($"Shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey} is null");
                throw new CtCustomException($"Shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey} is null");
            }
            else
            {
                if (shippingMethod.ZoneRates.Any())
                {
                    var zoneRate = shippingMethod?.ZoneRates?.FirstOrDefault();
                    if (zoneRate != null)
                    {
                        var shippingRate = zoneRate?.ShippingRates?.FirstOrDefault();
                        if (shippingRate != null)
                        {
                            deliveryCost = shippingRate.Price.CentAmount / (decimal)Math.Pow(10, shippingRate.Price.FractionDigits);
                        }
                        else
                        {
                            _logger.LogWarning($"Cannot find shipping rate within shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey}");
                            throw new CtCustomException($"Cannot find shipping within shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey}");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Cannot find any zone rate within shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey}");
                        throw new CtCustomException($"Cannot find zone rate within shipping method with key {_commonSettings.CurrentValue.OutboundOrderShippingMethodKey}");
                    }
                }
            }
            return deliveryCost;
        }

        public async Task<decimal> GetDeliveryFeeForPfs(bool isMourning)
        {
            string key = null;
            if (!isMourning)
            {
                key = _commonSettings.CurrentValue.PfsShippingMethodKey;
            }
            else
            {
                key = _commonSettings.CurrentValue.MourningShippingMethodKey;
            }
            IShippingMethod shippingMethod = null;
            try
            {
                shippingMethod = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .ShippingMethods()
                    .WithKey(key)
                    .Get()
                    .WithExpand("zoneRates[*].zone")
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping method ismourning {isMourning}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving shipping method ismourning {isMourning} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving shipping method ismourning {isMourning} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            decimal deliveryCost = 0;
            if (shippingMethod == null)
            {
                _logger.LogWarning($"Shipping method ismourning {isMourning} is null");
                throw new CtCustomException($"Shipping method ismourning {isMourning} is null");
            }
            else
            {
                string countryCode = _commonSettings.CurrentValue.LocalCountryCode;
                if(countryCode == null)
                {
                    _logger.LogWarning($"CountryCode is null");
                    throw new CtCustomException($"CountryCode is null");
                }
                if(shippingMethod.ZoneRates.Any(zone => zone.Zone.Obj.Locations.Any(loc => loc.Country == countryCode)))
                {
                    var zoneRate = shippingMethod?.ZoneRates?.FirstOrDefault(zone => zone.Zone.Obj.Locations.Any(loc => loc.Country == countryCode));
                    if(zoneRate != null)
                    {
                        var shippingRate = zoneRate?.ShippingRates?.FirstOrDefault();
                        if (shippingRate != null)
                        {
                            deliveryCost = shippingRate.Price.CentAmount / (decimal)Math.Pow(10, shippingRate.Price.FractionDigits);
                        }
                        else
                        {
                            _logger.LogWarning($"Cannot find shipping rate for {countryCode} into shipping method with key={key}");
                            throw new CtCustomException($"Cannot find shipping rate for {countryCode} into shipping method with key={key}");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Cannot find zone rate for {countryCode} into shipping method with key={key}");
                        throw new CtCustomException($"Cannot find zone rate for {countryCode} into shipping method with key={key}");
                    }
                }
            }
            return deliveryCost;
        }

        public async Task<decimal> GetDeliveryFeeForPfs(IShippingMethod shippingMethod, bool isMourning)
        {
            decimal deliveryCost = 0;
            if (shippingMethod == null)
            {
                _logger.LogWarning($"Shipping method ismourning {isMourning} is null");
                throw new CtCustomException($"Shipping method ismourning {isMourning} is null");
            }
            else
            {
                string countryCode = _commonSettings.CurrentValue.LocalCountryCode;
                if (countryCode == null)
                {
                    _logger.LogWarning($"CountryCode is null");
                    throw new CtCustomException($"CountryCode is null");
                }
                if (shippingMethod.ZoneRates.Any(zone => zone.Zone.Obj.Locations.Any(loc => loc.Country == countryCode)))
                {
                    var zoneRate = shippingMethod?.ZoneRates?.FirstOrDefault(zone => zone.Zone.Obj.Locations.Any(loc => loc.Country == countryCode));
                    if (zoneRate != null)
                    {
                        var shippingRate = zoneRate?.ShippingRates?.FirstOrDefault();
                        if (shippingRate != null)
                        {
                            deliveryCost = shippingRate.Price.CentAmount / (decimal)Math.Pow(10, shippingRate.Price.FractionDigits);
                        }
                        else
                        {
                            _logger.LogWarning($"Cannot find shipping rate for {countryCode} into shipping method with key={shippingMethod.Key}");
                            throw new CtCustomException($"Cannot find shipping rate for {countryCode} into shipping method with key={shippingMethod.Key}");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Cannot find zone rate for {countryCode} into shipping method with key={shippingMethod.Key}");
                        throw new CtCustomException($"Cannot find zone rate for {countryCode} into shipping method with key={shippingMethod.Key}");
                    }
                }
            }
            return deliveryCost;
        }
    }
}
