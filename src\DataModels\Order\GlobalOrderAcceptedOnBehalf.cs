﻿namespace ITF.SharedModels.DataModels.Order
{

    public class GlobalOrderAcceptedOnBehalf
    {
        public string OrderIdentifier { get; set; }

        public static implicit operator GlobalOrderAcceptedOnBehalf(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAcceptedOnBehalfMessage v)
        {
            return new GlobalOrderAcceptedOnBehalf { OrderIdentifier = v?.Payload.OrderIdentifier };
        }
    }
}
