﻿using ITF.SharedLibraries.EventSourcing.Projections.Interfaces;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Projections.Services
{
    public class EventSubscriberService : CustomBackgroundService.CustomBackgroundService
    {
        private readonly ISubscriptionManager _subscriptionManager;
        private readonly ILogger<EventSubscriberService> _logger;

        public EventSubscriberService(
            ISubscriptionManager subscriptionManager,
            ILogger<EventSubscriberService> logger)
        {
            _subscriptionManager = subscriptionManager;
            _logger = logger;
        }

        protected override Task ExecuteAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("{service} is starting", nameof(EventSubscriberService));
            cancellationToken.Register(() =>
                _logger.LogInformation("{service} background task is stopping", nameof(EventSubscriberService)));

            var subscriber = _subscriptionManager;
            new Thread(async () => await subscriber.StartProjections(cancellationToken)).Start();
            return Task.CompletedTask;
        }
    }
}
