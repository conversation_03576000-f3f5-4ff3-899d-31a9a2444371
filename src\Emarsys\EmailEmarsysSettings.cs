﻿using System;

namespace ITF.SharedLibraries.Emarsys;

public class EmailEmarsysSettings
{
    public string EventEndpoint { get; set; } = "event/";
    public string ContactEndpoint { get; set; } = "contact/";
    public string GetContactEndpoint { get; set; } = "getdata/";
    public string ForgottenPasswordEmailEvent { get; set; } = "0";
    public string ForgottenPasswordB2BEmailEvent { get; set; } = "0";
    public string AbandonedCartEmailEvent { get; set; } = "0";
    public string DeliveryConfirmationEmailEvent { get; set; } = "0";
    public string OrderConfirmationEmailEvent { get; set; } = "0";
    public string OrderInvoiceEmailEvent { get; set; } = "0";
    public string RegisterCustomerEmailEvent { get; set; } = "0";
    public string RegisterCustomerB2BEmailEvent { get; set; } = "0";
    public string AbsentRecipientEmailEvent { get; set; } = "0";
    public string OrderPreparationEmailEvent { get; set; } = "0";
    public string DeliveryReassuranceEmailEvent { get; set; } = "0";
    public string AccountDeletionNotificationEmailEvent { get; set; } = "0";
    public string AccountDeletedEmailEvent { get; set; } = "0";
    public string OrderRescheduledEmailEvent { get; set; } = "0";
    public string OrderCancelledEmailEvent { get; set; } = "0";
    public string NPSEmailEvent { get; set; } = "0";
    public string IfloraCustomerWaitingEmailEvent { get; set; } = "0";
    public string IfloraRecipientConfirmationEmailEvent { get; set; } = "0";
    public string IfloraRecipientRefusalEmailEvent { get; set; } = "0";
    public string Country { get; set; }
    public bool DontSendEmailForOrderWithDeliveryDatePassed { get; set; }
    public DateTime ActivationDateUTC { get; set; }
    public string AbandonedCartUrl { get; set; }
    public int DontSendEmailToCartWithActivityOlderThanInMinutes { get; set; }
    public bool IsMockingEnabled { get; set; } = false;
    public string MockedEmail { get; set; }

}
