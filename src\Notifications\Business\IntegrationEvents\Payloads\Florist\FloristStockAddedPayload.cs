﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.Florist
{
    public class FloristStockAddedPayload : IPayload
    {
        public string FloristId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public bool IsInStock { get; set; }
        public string ModifiedBy { get; set; }
        public DateTime LastModified { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }
}
