using System;
using System.IO;
using Xunit;
using AutoFixture;
using System.Threading;
using System.Linq;
using System.Collections.Generic;
using FluentAssertions;
using static ITF.SharedLibraries.UnitTests.Kafka.Message.Messages.V1;
using static ITF.SharedLibraries.UnitTests.Kafka.Message.CommerceTools.Messages.V1;
using commercetools.Sdk.Api.Models.Subscriptions;
using commercetools.Sdk.Api.Models.Messages;

namespace ITF.SharedLibraries.UnitTests.Kafka.Integrationtest
{
    [Collection("Kafka integration tests")]
    public sealed class KafkaTests : IClassFixture<KafkaFixture>
    {
        private readonly KafkaFixture _kafkaFixture;

        public KafkaTests(KafkaFixture kafkaFixture)
        {
            _kafkaFixture = kafkaFixture;
            _kafkaFixture._kafkaSubscriber.InitKafka();
        }

        //[Fact]
        //public async void ShouldProduceAsyncOrSyncAndReceiveMessages()
        //{
        //    const int nb = 10_000;
        //    const string topic = "integrationtesttopic";
        //    var messages = new List<CustomMessage>();
        //    // Arrange & Act
        //    for (int i = 0; i < nb; i++)
        //    {
        //        var fixture = new Fixture();
        //        var message = fixture
        //        .Build<CustomMessage>()
        //        .Without(p => p.ClrType)
        //        .Create();

        //        messages.Add(message);

        //        if (i < nb/2)
        //            await _kafkaFixture._kafkaPublisher.PublishAsync(message, topic, message.GetMessageKey());
        //        else
        //            _kafkaFixture._kafkaPublisher.Publish(message, topic, message.GetMessageKey());
        //    }

        //    const int nbct = 10_000;
        //    var data = File.ReadAllText("Resources/Kafka/CTmessages.json");
        //    var payloads = _kafkaFixture.SerializerService.Deserialize<List<IDeliveryPayload>>(data);
        //    var messagesCt = new List<CustomCTMessage>();
        //    // Arrange & Act
        //    for (int i = 0; i < nbct; i++)
        //    {
        //        var fixture = new Fixture();
        //        var message = new CustomCTMessage { Payload = new() };

        //        message.CreatedAt = new DateTime(2022, 03, 07, 15, 41, 35);
        //        message.NbTry = fixture.Create<int>();
        //        message.DistributedTracingData = fixture.Create<string>();
        //        message.CausationId = fixture.Create<string>();

        //        message.Payload.Id = fixture.Create<string>();
        //        message.Payload.EventDate = new DateTime(2022, 03, 07, 15, 41, 31);
        //        message.Payload.EventID = fixture.Create<string>();

        //        message.Payload.ProductPublishedMessage = (payloads.ElementAt(0) as MessageDeliveryPayload).Message as ProductPublishedMessage;
        //        message.Payload.ProductUnpublishedMessage = (payloads.ElementAt(1) as MessageDeliveryPayload).Message as ProductUnpublishedMessage;
        //        message.Payload.CategoryCreatedMessage = (payloads.ElementAt(2) as MessageDeliveryPayload).Message as CategoryCreatedMessage;
        //        message.Payload.CategorySlugChangedMessage = (payloads.ElementAt(3) as MessageDeliveryPayload).Message as CategorySlugChangedMessage;

        //        messagesCt.Add(message);

        //        await _kafkaFixture._kafkaPublisher.PublishAsync(message, topic, message.GetMessageKey(), ExtensionMethods.Serializer.SerializerType.CommerceTools);
        //    }

        //    var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(60));

        //    await _kafkaFixture._kafkaSubscriber.ConsumerCheckAsync(_kafkaFixture._kafkaSubscriber.GetSubscribers().ElementAt(0), timeoutCts.Token);

        //    // Assert (ct messages)
        //    Assert.Equal(nb + nbct, _kafkaFixture._concurrentBag.Count);
        //    for (int i = 0; i < nb; i++)
        //    {
        //        Assert.IsType<CustomCTMessage>(_kafkaFixture._concurrentBag.ElementAt(i));
        //        var currentMsg = (_kafkaFixture._concurrentBag.ElementAt(i) as CustomCTMessage);
        //        var foundMsg = messagesCt.FirstOrDefault(m => m.Payload.Id == currentMsg.Payload.Id);
        //        Assert.NotNull(foundMsg);

        //        currentMsg.Should()
        //        .BeEquivalentTo(foundMsg);
        //    }

        //    // Assert (standard messages)
        //    for (int i = nb; i < (nbct + nb); i++)
        //    {
        //        Assert.IsType<CustomMessage>(_kafkaFixture._concurrentBag.ElementAt(i));
        //        var currentMsg = (_kafkaFixture._concurrentBag.ElementAt(i) as CustomMessage);
        //        var foundMsg = messages.FirstOrDefault(m => m.Payload.Id == currentMsg.Payload.Id);
        //        Assert.NotNull(foundMsg);

        //        currentMsg.Should()
        //        .BeEquivalentTo(foundMsg);
        //    }
        //}
    }
}
