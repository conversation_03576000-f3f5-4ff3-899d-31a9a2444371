﻿using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Me;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.CustomAttributes;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class CartExtensionMethods
    {
        private static void CheckCustom(this ICartDraft cartDraft)
        {
            if (cartDraft.Custom == null)
            {
                cartDraft.Custom = new CustomFieldsDraft
                {
                    Type = new TypeResourceIdentifier
                    {
                        Key = "cart-custom"
                    },
                    Fields = new FieldContainer()
                };
            }
        }
        public static void SetCardMessage(this ICartDraft cartDraft, string message)
        {
            if(cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.MESSAGE, message);
            }
        }
        public static void SetSignature(this ICartDraft cartDraft, string signature)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.SIGNATURE, signature);
            }
        }
        public static void SetOccasionCode(this ICartDraft cartDraft, string occasionCode)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.OCCASION_CODE, occasionCode);
            }
        }
        public static void SetDevice(this ICartDraft cartDraft, string device)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.DEVICE, device);
            }
        }
        public static void SetCartAbandonedTag(this ICartDraft cartDraft, string cartAbandonedTag)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.CART_ABANDONED_TAG, cartAbandonedTag);
            }
        }
        public static void SetIP(this ICartDraft cartDraft, string ip)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.IP, ip);
            }
        }
        public static void SetTransmitterFloristId(this ICartDraft cartDraft, string floristId)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID, floristId);
            }
        }
        public static void SetFloristOrderStatus(this ICartDraft cartDraft, string status)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS, status);
            }
        }
        public static void SetLegacyOrderNumber(this ICartDraft cartDraft, string legacyOrderNumber)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.LEGACY_ORDER_NUMBER, legacyOrderNumber);
            }
        }
        public static void SetSRC(this ICartDraft cartDraft, string src)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.SRC, src);
            }
        }
        public static void SetDeliveryMode(this ICartDraft cartDraft, string deliveryMode)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.DELIVERY_MODE, deliveryMode);
            }
        }
        public static void SetExecutingFloristId(this ICartDraft cartDraft, string executingFloristId)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID, executingFloristId);
            }
        }
        public static void SetExecutingFloristType(this ICartDraft cartDraft, string executingFloristType)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_TYPE, executingFloristType);
            }
        }
        public static void SetIsInvoiceRequested(this ICartDraft cartDraft, bool isInvoiceRequested)
        {
            if (cartDraft != null)
            {
                cartDraft.CheckCustom();
                cartDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.INVOICE_REQUEST, isInvoiceRequested);
            }
        }
    }
}
