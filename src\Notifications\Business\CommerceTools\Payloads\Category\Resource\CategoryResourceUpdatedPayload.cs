﻿using commercetools.Sdk.Api.Models.Categories;
using commercetools.Sdk.Api.Models.Subscriptions;
using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Notifications.Business.CommerceTools.Messages.Category.Resource
{
    public class CategoryResourceUpdatedPayload : IPayload
    {
        public string EventID { get; set; } = Guid.NewGuid().ToString();
        public DateTime EventDate { get; set; } = DateTime.Now;
        public ICategory Category { get; set; }
        public ResourceUpdatedDeliveryPayload ResourceUpdated { get; set; }
    }
}
