﻿using System.Diagnostics.CodeAnalysis;

namespace IT.SharedLibraries.CT.ExtensionMethods.Domain
{

    public record BundleVariant 
    {
        public string ProductId { get; set; }
        public string VariantId { get; set; }
    }
    //public class BundleVariant : IEquatable<BundleVariant>, IEqualityComparer<BundleVariant>
    //{
    //    public string ProductId { get; set; }
    //    public string VariantId { get; set; }
    //    public override bool Equals(object? obj)
    //    {
    //        var item = obj as BundleVariant;
    //        return this.Equals(item);
    //    }

    //    public override string ToString()
    //    {
    //        if (!String.IsNullOrEmpty(ProductId) && !String.IsNullOrEmpty(VariantId))
    //        {
    //            return $"{ProductId}#{VariantId}";
    //        }
    //        return string.Empty;
    //    }

    //    public bool Equals(BundleVariant s1, BundleVariant s2)
    //    {
    //        if (s1 == null && s2 == null)
    //            return true;
    //        else if (s1 == null || s2 == null)
    //            return false;
    //        else if(s1.Equals(s2))
    //            return true;
    //        else
    //            return false;
    //    }

    //    public override int GetHashCode() => new { ProductId, VariantId }.GetHashCode();

    //    public bool Equals(BundleVariant? item)
    //    {
    //        if (item == null)
    //        {
    //            return false;
    //        }

    //        return this.ProductId.Equals(item.ProductId) && this.VariantId.Equals(item.VariantId);
    //    }

    //    public int GetHashCode([DisallowNull] BundleVariant obj) => new { ProductId, VariantId }.GetHashCode();
    //}
}
