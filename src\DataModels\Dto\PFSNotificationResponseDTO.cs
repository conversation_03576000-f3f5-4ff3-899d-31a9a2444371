﻿using System;
using System.Collections.Generic;

namespace ITF.SharedModels.DataModels.Dto
{
    public class PFSNotificationResponseDTO
    {
        public string Id { get; set; }
        public DateTime MessageDate { get; set; }
        public string OrderNumber { get; set; }
        public string AssignmentState { get; set; } // OrderAssignmentType => ASSIGNED / UPDATED / REJECTED / CANCELED
        public string Type { get; set; } // OrderNotificationType => order | modification | transmission | cancelation | review 
        public DateTime DeliveryDate { get; set; }
        public bool IsRead { get; set; }
        public string OrderId { get; set; } // CommerceTools order ID
    }

    public class PFSNotificationResponseListsDTO
    {
        public List<PFSNotificationResponseDTO> Orders { get; set; } = new();
        public List<PFSNotificationResponseDTO> Modifications { get; set; } = new();
        public List<PFSNotificationResponseDTO> Cancellations { get; set; } = new();
        public List<PFSNotificationResponseDTO> Refused { get; set; } = new();
    }

}
