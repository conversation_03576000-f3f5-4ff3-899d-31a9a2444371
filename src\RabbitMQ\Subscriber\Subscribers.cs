﻿using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.RabbitMQ.Subscriber
{
    public class Subscribers
    {
        public string ClassName { get; set; }
        public IConnection Connection { get; set; }
        public IChannel Channel { get; set; }
        public SubscriberConfiguration SubscriberConfiguration { get; set; }
        public AsyncEventingBasicConsumer Consumer { get; set; }
    }
}
