﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads
{
    public class GfsCountryPayload : IPayload
    {
        public int? Id { get; set; }
        public bool? Enabled { get; set; }
        public string CountryCode { get; set; }
        public string Name { get; set; }
        public int? TimeZone { get; set; }
        public int? CurrencyId { get; set; }
        public int? DialingCode { get; set; }
        public int? UnitId { get; set; }
        public DateTime LastUpdate { get; set; }
        public List<GfsCountryTime> CountryTimes { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class GfsCountryTime
    {
        public int? Id { get; set; }
        public string CountryCode { get; set; }
        public int? DayOfWeek { get; set; }
        public DateTime? OpenTime { get; set; }
        public DateTime? CloseTime { get; set; }
        public DateTime? SameDayDeadline { get; set; }
    }
}
