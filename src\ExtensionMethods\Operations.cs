﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace ITF.SharedLibraries.ExtensionMethods
{
    public static class Operations
    {
        public static bool IsIn(this object value, params object[] checkList) => checkList.Contains(value);

        public static List<T> RemoveDuplicates<T>(this IEnumerable<List<T>> value)
        {
            var results = new List<T>();
            value.ToList().ForEach(v =>
            {
                results = results.Union(v).ToList();
            });

            return results;
        }

        public static IEnumerable<DateTime> ToDateTime(this IEnumerable<string> value)
        {
            foreach(var v in value)
            {
                if (DateTime.TryParse(v, out DateTime dt))
                    yield return dt;
            }
        }

        public static bool IsAnyReached(this IEnumerable<DateTime> triggers, DateTime current, DateTime lastProcessed)
        {
            List<bool> results = new();
            foreach (var trigger in triggers)
            {
                results.Add(IsReached(trigger, current, lastProcessed));
            }
            return results.Any(r => r == true);
        }

        private static bool IsReached(this DateTime trigger, DateTime current, DateTime lastProcessed)
        {
            if (lastProcessed.Year == 1900)
                return true;

            if (current.TimeOfDay > trigger.TimeOfDay && lastProcessed.Date < current.Date)
                return true;

            return false;
        }

        public static DateTime SetStartOfTheDay(this DateTime dt)
            => new DateTime(dt.Year, dt.Month, dt.Day, 0, 0 ,0);

        public static DateTime SetEndOfTheDay(this DateTime dt)
            => new DateTime(dt.Year, dt.Month, dt.Day, 23, 59, 59);

        public static string ToOdataOrConditions(this IEnumerable<string> value, string filterVariable)
        {
            int count = value.Count();

            if(count == 0)
                return string.Empty;

            if (count == 1)
                return $"{filterVariable} eq '{value.FirstOrDefault()}'";

            var sb = new System.Text.StringBuilder();
            value.ToList().ForEach(v =>
            {
                sb.Append($"{filterVariable} eq '{v}' or ");
            });
            var finalStr = sb.ToString();
            return finalStr.Remove(finalStr.Length - 4);
        }

        public static IEnumerable<T> AddRanges<T>(this IEnumerable<T> value, Dictionary<string, string> headers, params IEnumerable<T>[] args)
        {
            var list = value.ToList();
            foreach(var a in args)
            {
                list.AddRange(a);
            }
            return list;
        }

        // Either Add or overwrite
        public static void AddOrUpdate<K, V>(this ConcurrentDictionary<K, V> dictionary, K key, V value)
        {
            dictionary.AddOrUpdate(key, value, (oldkey, oldvalue) => value);
        }

        public static bool IsPropertyExist(dynamic obj, string name)
        {
            try
            {
                if (obj is ExpandoObject)
                    return ((IDictionary<string, object>)obj).ContainsKey(name);

                return obj.GetType().GetProperty(name) != null;
            }
            catch
            {
                return false;
            }
        }

        public static string RemoveDiacritics(this string text)
        {
            if (text == null)
                return string.Empty;

            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString()?/*.Replace("-", " ")?.Replace("+", " ")?*/.Normalize(NormalizationForm.FormC);
        }

        public static TWorkerType GetHostedService<TWorkerType>
            (this IServiceProvider serviceProvider) =>
            serviceProvider
            .GetServices<IHostedService>()
            .OfType<TWorkerType>()
            .FirstOrDefault();

        public static IEnumerable<TWorkerType> GetAllHostedService<TWorkerType>
            (this IServiceProvider serviceProvider) =>
            serviceProvider
            .GetServices<IHostedService>()
            .OfType<TWorkerType>();

        public static DateTime TrimMilliseconds(this DateTime dt)
        {
            return new DateTime(dt.Year, dt.Month, dt.Day, dt.Hour, dt.Minute, dt.Second, 0, dt.Kind);
        }

        public static bool AreDeepEquals<T>(IEnumerable<T> list1, IEnumerable<T> list2)
            where T : IEquatable<T>
        {
            if (list1 is null && list2 is null)
                return true;

            if (list1 is null && list2 != null)
                return false;

            if (list1 != null && list2 is null)
                return false;

            if (list1.Count() != list2.Count())
                return false;


            foreach (var item in list1.Select((value, i) => new { i, value }))
            {
                if (!list1.ElementAt(item.i).Equals(list2.ElementAt(item.i)))
                    return false;
            }

            return true;
        }

        public static bool AreDeepEquals<T>(T obj1, T obj2)
            where T : IEquatable<T>
        {
            if (obj1 is null && obj2 is null)
                return true;

            if (obj1 is null && obj2 != null)
                return false;

            if (obj1 != null && obj2 is null)
                return false;

            return obj1.Equals(obj2);
        }
    }
}
