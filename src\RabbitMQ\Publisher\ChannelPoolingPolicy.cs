﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Text;

namespace ITF.SharedLibraries.RabbitMQ.Publisher
{
    // cf. https://www.c-sharpcorner.com/article/publishing-rabbitmq-message-in-asp-net-core/
    class ChannelPoolingPolicy : IPooledObjectPolicy<IChannel>
    {
        private readonly Configuration _configuration;
        private IConnection _connection;
        private readonly ILogger _logger;
        public ChannelPoolingPolicy(ILoggerFactory loggerFactory, Configuration configuration)
        {
            _logger = loggerFactory.CreateLogger<ChannelPoolingPolicy>();
            _configuration = configuration;
            _connection = GetConnectionAsync().GetAwaiter().GetResult();
        }

        public IChannel Create()
        {
            if (_connection == null)
                _connection = GetConnectionAsync().GetAwaiter().GetResult();
            return CreateChannelAsync().GetAwaiter().GetResult();
        }

        private async Task<IChannel> CreateChannelAsync()
        {
            return await _connection.CreateChannelAsync();
        }

        public bool Return(IChannel obj)
        {
            if (obj != null && obj.IsOpen)
            {
                return true;
            }
            else
            {
                obj?.Dispose();
                return false;
            }
        }
        private async Task<IConnection> GetConnectionAsync()
        {
            try
            {
                var factory = new ConnectionFactory()
                {
                    HostName = _configuration.HostName,
                    UserName = _configuration.UserName,
                    Password = _configuration.Password,
                    Port = _configuration.Port,
                    VirtualHost = _configuration.VHost,
                    AutomaticRecoveryEnabled = true,
                    NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
                };
                return await factory.CreateConnectionAsync();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Exception, impossible de créer la connection RabbitMQ : {error}", e.Message);
            }
            return null;
        }
    }
}
