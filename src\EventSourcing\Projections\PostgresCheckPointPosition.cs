﻿using ITF.SharedLibraries.Postgres.Repository.EntityFramework;
using System;

namespace ITF.SharedLibraries.EventSourcing.Projections
{
    public class PostgresCheckPointPosition : BaseEntity<string>
    {
        public ulong PreparePosition { get; set; }
        public ulong CommitPosition { get; set; }
        public DateTime CreatedAt { get; set; }

        protected PostgresCheckPointPosition()
        {
        }

        public PostgresCheckPointPosition(string checkPointName, ulong commitPosition, ulong preparePosition)
        {
            Id = checkPointName;
            CommitPosition = commitPosition;
            PreparePosition = preparePosition;
            CreatedAt = DateTime.Now;
        }
    }
}
