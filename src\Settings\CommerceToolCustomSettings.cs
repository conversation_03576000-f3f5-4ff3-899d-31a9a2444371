﻿using System.Collections.Generic;

namespace IT.SharedLibraries.CT.Settings
{
    public class CommerceToolCustomSettings
    {
        public string LocalCountryCode { get; set; }
        public string LocalCountryChannelKey { get; set; }
        public string LocalCountryStoreKey { get; set; }
        public string CtMoruningProductTypeKey { get; set; }
        public string LocalCountryAccessoriesCategoryKey { get; set; }
        public string LocalCountryProductsCategoryKey { get; set; }
        //public decimal OutboundOrderShippingAmount { get; set; }
        //public decimal OutboundOrderShippingTaxRate { get; set; }
        public string OutboundOrderShippingMethodKey { get; set; }
        public string MourningShippingMethodKey { get; set; }
        public string PfsShippingMethodKey { get; set; }
        public decimal MarketingFeeToBeSettedOnProductForInternationalOutbound { get; set; } = 0;
        public List<string> ProductKeysNotToBeDisplayed { get; set; } = new();
        public double DuplicatedOrderTestTimeSeconds { get; set; } = -10;
    }
}
