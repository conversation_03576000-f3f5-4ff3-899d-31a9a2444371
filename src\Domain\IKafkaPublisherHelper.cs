﻿using ITF.SharedModels.Messages.Italy;
using static ITF.SharedModels.Messages.Group.Order.Messages.V1;
using System.Threading.Tasks;
using ITF.Lib.Common.Notifications.Messages;

namespace IT.Microservices.OrderReactor.Domain
{
    public interface IKafkaPublisherHelper
    {
        Task<PublishKafkaResult> Publish(OrderNewHistoryRecordMessage data, string kafkaTopic);
        Task<PublishKafkaResult> Publish(OrderNumberGeneratedMessage data, string kafkaTopic);
        Task<PublishKafkaResult> Publish<T>(object message, string kafkaTopic, string partitionKey = null) where T : class, IClrType, IDistributedTracing;
    }
}
