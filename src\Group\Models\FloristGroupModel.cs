﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.SharedModels.Group.Enums;
using MongoDB.Driver.GeoJsonObjectModel;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Group.Models
{
    public class FloristGroupModel : BaseClass<string>
    {
        public List<string> LastProjectedIds { get; set; } = new();
        public DateTime LastUpdate { get; set; }
        public string FloristId { get; set; }
        public string PersonalCode { get; set; }
        public FloristActivity FloristActivity { get; set; } = new();
        public UserActivity UserActivity { get; set; } = new();
        public ShopLocation ShopLocation { get; set; } = new();
        public List<Contact> Contacts { get; set; } = new();
        public List<Accessory> Accessories { get; set; } = new();
        public List<DailyCalendar> Calendar { get; set; } = new();
        public List<ClosingDay> ClosingDays { get; set; } = new();
        public List<Document> Documents { get; set; } = new();
        /// <summary>
        /// List of country specific fields
        /// </summary>
        public Dictionary<string, object> Attributes { get; set; } = new();
        /// <summary>
        /// Code of the country unit of the florist. Use to connect the fronted in the right micro services cluster.
        /// </summary>
        public string UnitCountryCode { get; set; }

        public override void SetId() => Id = FloristId;
    }

    public class UserActivity
    {
        public string CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string LastModifiedBy { get; set; }
        public DateTime? LastModified { get; set; }
    }

    public class FloristActivity
    {
        public DateTime AgreementOn { get; set; }
        public string LastSuspensionReason { get; set; }
        public DateTime? LastSuspensionOn { get; set; }
        public DateTime? LastReinstateOn { get; set; }
        public bool BlockedInExecution { get; set; }
        public bool BlockedInTransmission { get; set; }
        public bool Deleted { get; set; }
    }

    public class ShopLocation
    {
        public string Name { get; set; }
        public string Street { get; set; }
        public string ZipCode { get; set; }
        public string City { get; set; }
        public string CountryCode { get; set; }
        public GeoJsonPoint<GeoJson2DGeographicCoordinates> Gps { get; set; }
    }

    public class Contact
    {
        public Contact(string _type, string _entry)
        {
            Type = _type;
            Entry = _entry;
        }

        public string Type { get; set; }
        public string Entry { get; set; }
    }

    public class Accessory
    {
        public Accessory(string productCode, string productName, int quantiy)
        {
            ProductCode = productCode;
            ProductName = productName;
            Quantity = quantiy;
        }

        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public int Quantity { get; set; }
    }

    public class DailyCalendar
    {
        public DailyCalendar() { }

        public int DayOfWeek { get; set; }
        public bool IsOpen { get; set; }
        public TimeSpan OpenHour { get; set; }
        public TimeSpan CloseHour { get; set; }
        public bool IsBreakTimePresent { get; set; }
        public TimeSpan BreakTimeStart { get; set; }
        public TimeSpan BreakTimeEnd { get; set; }
    }

    public class ClosingDay
    {
        public string ClosingDayId { get; set; }
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
    }

    public class Document
    {
        public DocTypeEnum DocType { get; set; }
        public string OrderReference { get; set; }
        public string FileName { get; set; }
        public string FileExtension { get; set; }
        public string Url { get; set; } // url to call to get file bytes
    }
}
