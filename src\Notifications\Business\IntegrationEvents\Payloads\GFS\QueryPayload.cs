﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS
{
    public class QueryPayload : GFSPayloadBase, IPayload
    {
        public string Subject { get; set; }
        public string Text { get; set; }
        public List<QueryField> QueryFields { get; set; }
        public int FloristgateNumber { get; set; }

        public string EventID { get; set; }
        public DateTime EventDate { get; set; }
    }

    public class QueryField
    {
        public int Field { get; set; }
        public int ItemNumber { get; set; }
        public string Value { get; set; }
    }
}
