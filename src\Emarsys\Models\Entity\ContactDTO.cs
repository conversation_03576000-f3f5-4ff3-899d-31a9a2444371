﻿namespace ITF.SharedLibraries.Emarsys.Models.Entity;

public class ContactDTO
{
    public string ContactId { get; set; }
    public string Email { get; set; }
    public SalutationEmailEnum Salutation { get; set; }
    public string LastName { get; set; }
    public string FirstName { get; set; }
    public string CompanyName { get; set; }
    public string B2C { get; set; }
    public string B2B { get; set; }
    public string B2F { get; set; }
    public string DateOfBirth { get; set; }
    public string Mobile { get; set; }
    public string PostalCode { get; set; }
    public string City { get; set; }
    public string Country { get; set; }
    public string OptInOriginFr { get; set; }
    public string OptInOriginEs { get; set; }
    public string OptInOriginIt { get; set; }
    public string OptInOriginPt { get; set; }
    public string OptInOriginDk { get; set; }
    public string OptIn { get; set; } = "1";
    public string OptInEmailFr { get; set; }
    public string OptInEmailEs { get; set; }
    public string OptInEmailIt { get; set; }
    public string OptInEmailPt { get; set; }
    public string OptInEmailDk { get; set; }
    public string OptInSmsFr { get; set; }
    public string OptInSmsEs { get; set; }
    public string OptInSmsIt { get; set; }
    public string OptInSmsPt { get; set; }
    public string OptInSmsDk { get; set; }
    public string MobileSmsOptIn { get; set; } = "1";
    public string OptInAbandonedCartFr { get; set; }
    public string OptInAbandonedCartEs { get; set; }
    public string OptInAbandonedCartIt { get; set; }
    public string OptInAbandonedCartPt { get; set; }
    public string OptInAbandonedCartDk { get; set; }
    public string OptInBirthdayFr { get; set; }
    public string OptInBirthdayEs { get; set; }
    public string OptInBirthdayIt { get; set; }
    public string OptInBirthdayPt { get; set; }
    public string OptInBirthdayDk { get; set; }
    public string CreatedFr { get; set; }
    public string CreatedEs { get; set; }
    public string CreatedIt { get; set; }
    public string CreatedPt { get; set; }
    public string CreatedDk { get; set; }
    public string ContactFr { get; set; }
    public string ContactEs { get; set; }
    public string ContactIt { get; set; }
    public string ContactPt { get; set; }
    public string ContactDk { get; set; }
    public string FloristId { get; set; }
    public string FloristScoreNps { get; set; }
    public string FloristEntryDate { get; set; }
    public string FloristActif { get; set; }
    public string ItfPlusFrMember { get; set; }
    public string ItfPlusEsMember { get; set; }
    public string ItfPlusItMember { get; set; }
    public string ItfPlusPtMember { get; set; }
    public string ItfPlusFrDateStart { get; set; }
    public string ItfPlusEsDateStart { get; set; }
    public string ItfPlusItDateStart { get; set; }
    public string ItfPlusPtDateStart { get; set; }
    public string ItfPlusFrDateEnd { get; set; }
    public string ItfPlusEsDateEnd { get; set; }
    public string ItfPlusItDateEnd { get; set; }
    public string ItfPlusPtDateEnd { get; set; }

    public ContactDTO(Contact contact)
    {
        ContactId = contact.ContactId;
        Email = contact.Email;
        Salutation = contact.Salutation;
        FirstName = contact.FirstName;
        LastName = contact.LastName;
        CompanyName = contact.CompanyName;
        B2C = contact.B2C;
        B2B = contact.B2B;
        B2F = contact.B2F;
        DateOfBirth = contact.DateOfBirth;
        Mobile = contact.Mobile;
        PostalCode = contact.PostalCode;
        City = contact.City;
        Country = contact.Country;
        OptInOriginFr = contact.OptInOriginFr;
        OptInOriginEs = contact.OptInOriginEs;
        OptInOriginIt = contact.OptInOriginIt;
        OptInOriginPt = contact.OptInOriginPt;
        OptIn = contact.OptIn;
        OptInEmailFr = contact.OptInEmailFr;
        OptInEmailEs = contact.OptInEmailEs;
        OptInEmailIt = contact.OptInEmailIt;
        OptInEmailPt = contact.OptInEmailPt;
        OptInEmailDk = contact.OptInEmailDk;
        OptInSmsFr = contact.OptInSmsFr;
        OptInSmsEs = contact.OptInSmsEs;
        OptInSmsIt = contact.OptInSmsIt;
        OptInSmsPt = contact.OptInSmsPt;
        OptInSmsDk = contact.OptInSmsDk;
        MobileSmsOptIn = contact.MobileSmsOptIn;
        OptInAbandonedCartFr = contact.OptInAbandonedCartFr;
        OptInAbandonedCartEs = contact.OptInAbandonedCartEs;
        OptInAbandonedCartIt = contact.OptInAbandonedCartIt;
        OptInAbandonedCartPt = contact.OptInAbandonedCartPt;
        OptInAbandonedCartDk = contact.OptInAbandonedCartDk;
        OptInBirthdayFr = contact.OptInBirthdayFr;
        OptInBirthdayEs = contact.OptInBirthdayEs;
        OptInBirthdayIt = contact.OptInBirthdayIt;
        OptInBirthdayPt = contact.OptInBirthdayPt;
        OptInBirthdayDk = contact.OptInBirthdayDk;
        CreatedFr = contact.CreatedFr;
        CreatedEs = contact.CreatedEs;
        CreatedIt = contact.CreatedIt;
        CreatedPt = contact.CreatedPt;
        CreatedDk = contact.CreatedDk;
        ContactFr = contact.ContactFr;
        ContactEs = contact.ContactEs;
        ContactIt = contact.ContactIt;
        ContactPt = contact.ContactPt;
        ContactDk = contact.ContactDk;
        FloristId = contact.FloristId;
        FloristScoreNps = contact.FloristScoreNps;
        FloristEntryDate = contact.FloristEntryDate;
        FloristActif = contact.FloristActif;
        ItfPlusFrMember = contact.ItfPlusFrMember;
        ItfPlusEsMember = contact.ItfPlusEsMember;
        ItfPlusItMember = contact.ItfPlusItMember;
        ItfPlusPtMember = contact.ItfPlusPtMember;
        ItfPlusFrDateStart = contact.ItfPlusFrDateStart;
        ItfPlusEsDateStart = contact.ItfPlusEsDateStart;
        ItfPlusItDateStart = contact.ItfPlusItDateStart;
        ItfPlusPtDateStart = contact.ItfPlusPtDateStart;
        ItfPlusFrDateEnd = contact.ItfPlusFrDateEnd;
        ItfPlusEsDateEnd = contact.ItfPlusEsDateEnd;
        ItfPlusItDateEnd = contact.ItfPlusItDateEnd;
        ItfPlusPtDateEnd = contact.ItfPlusPtDateEnd;
    }
}
