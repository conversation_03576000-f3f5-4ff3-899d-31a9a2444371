﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Zones;
using IT.SharedLibraries.CT.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Zones
{
    public class ZoneService : IZoneService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ZoneService> _logger;
        private readonly string _projectKey;
        private readonly IOptionsMonitor<CommerceToolCustomSettings> _commonSettings;

        public ZoneService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ZoneService> logger,
            IOptionsMonitor<CommerceToolCustomSettings> commonSettings)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
            _commonSettings = commonSettings;
        }

        public async Task<IZone> GetByKey(string key)
        {
            IZone zone = null;
            try
            {
                zone = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Zones()
                    .WithKey(key)
                    .Get()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while retrieving zone with key {key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving zone with key {key} because of {nfex.Message} - {nfex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while retrieving zone with key {key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return zone;
        }
    }
}
