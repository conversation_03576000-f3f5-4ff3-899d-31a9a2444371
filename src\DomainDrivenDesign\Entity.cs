﻿using ITF.Lib.Common.DomainDrivenDesign.Interfaces;
using System;

namespace ITF.Lib.Common.DomainDrivenDesign
{
    public abstract class Entity<TId> : IInternalEventHandler
            where TId : Value<TId>
    {
        private readonly Action<object> _applier;

        public TId? Id { get; protected set; }

        protected Entity(Action<object> applier) => _applier = applier;

        protected abstract void When(object @event);

        protected void Apply(object @event)
        {
            // When(@event); <= https://github.com/PacktPublishing/Hands-On-Domain-Driven-Design-with-.NET-Core/issues/2
            _applier(@event);
        }

        void IInternalEventHandler.Handle(object @event) => When(@event);
    }
}
