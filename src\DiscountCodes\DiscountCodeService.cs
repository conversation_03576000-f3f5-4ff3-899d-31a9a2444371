﻿using commercetools.Base.Client;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.CartDiscounts;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.DiscountCodes;
using commercetools.Sdk.Api.Serialization;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;

namespace IT.SharedLibraries.CT.DiscountCodes
{
    public class DiscountCodeService : IDiscountCodeService
    {
        private readonly IClient _ctClient;
        private readonly IConfiguration _config;
        private readonly ILogger<DiscountCodeService> _logger;
        private readonly SerializerService _serializerService;
        private string _projectKey = null;

        public DiscountCodeService(IClient commerceToolsClient,
            IConfiguration configuration,
            ILogger<DiscountCodeService> logger,
            SerializerService serializerService)
        {
            _ctClient = commerceToolsClient;
            _config = configuration;
            _logger = logger;
            _serializerService = serializerService;
        }

        public void Initialize()
        {
            _projectKey = _config.GetSection("Client:ProjectKey").Value;
        }

        public async Task<IDiscountCode> CreateDiscountCode(string prefix, int daysDelay, int useLimit, LocalizedString discountLocalizedName, string templateCartDiscountKey)
        {
            // Generate voucher
            string voucherCode = VoucherGenerator.GenerateVoucherCode();

            IDiscountCodeDraft discountCodeDraft = new DiscountCodeDraft()
            {
                Name = discountLocalizedName,
                Code = prefix + "-" + voucherCode,
                Description = new LocalizedString() { { "fr", "" } },
                ValidFrom = DateTime.Now,
                ValidUntil = DateTime.Now.AddDays(daysDelay),
                MaxApplications = useLimit,
                MaxApplicationsPerCustomer = useLimit
            };

            // Get common cart discount
            ICartDiscount cartDiscount = await GetCartDiscountByKey(templateCartDiscountKey)
                ?? throw new InvalidOperationException($"Cart discount with key {templateCartDiscountKey} was not found!");

            discountCodeDraft.CartDiscounts ??= new List<ICartDiscountResourceIdentifier>();
            discountCodeDraft.CartDiscounts.Add(new CartDiscountResourceIdentifier()
            {
                Id = cartDiscount.Id
            });

            // Create discount code
            IDiscountCode discountCode = await CreateDiscountCodeFromDraft(discountCodeDraft)
                ?? throw new InvalidOperationException("Discount code failed to be created");

            _logger.LogInformation("Discount code created on CT. Id:{Id}, Code:{Code}", discountCode.Id, discountCode.Code);

            return discountCode;
        }

        public async Task<IDiscountCode> CreateDiscountCodeFromDraft(IDiscountCodeDraft discountCodeDraft)
        {
            try
            {
                return await _ctClient
                    .WithApi().WithProjectKey(_projectKey)
                    .DiscountCodes()
                    .Post(discountCodeDraft)
                    .ExecuteAsync();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to create the discount code from the draft {Draft} from CT API", discountCodeDraft?.Serialize(SerializerType.CommerceTools, _serializerService));
                throw;
            }
        }

        public async Task<ICartDiscount> GetCartDiscountByKey(string cartDiscountKey)
        {
            try
            {
                var res = await _ctClient
                    .WithApi().WithProjectKey(_projectKey)
                    .CartDiscounts()
                    .Get().WithWhere($"key = \"{cartDiscountKey}\"")
                    .ExecuteAsync();

                return res?.Results?.FirstOrDefault();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to Get the cart discount with the cart discount key {Key} from CT API", cartDiscountKey);
                throw;
            }
        }

        public async Task<ICartDiscount> GetCartDiscountById(string cartDiscountId)
        {
            try
            {
                var res = await _ctClient
                    .WithApi().WithProjectKey(_projectKey)
                    .CartDiscounts()
                    .WithId(cartDiscountId).Get()
                    .ExecuteAsync();

                return res;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to Get the cart discount with the cart discount id {Id} from CT API", cartDiscountId);
                throw;
            }
        }

        public async Task<bool> SaveVoucherInCart(string cartId, string voucherCode, long cartVersion)
        {
            try
            {
                await _ctClient.WithApi().WithProjectKey(_projectKey)
                    .Carts().WithId(cartId).Post(new CartUpdate()
                    {
                        Actions = {
                            new CartSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.VOUCHER,
                                Value = voucherCode
                            }
                        },
                        Version = cartVersion
                    }).ExecuteAsync();
                return true;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to save the voucher in the cart {CartId} version {Version} with CT API", cartId, cartVersion);
                return false;
            }
        }
    }
}
