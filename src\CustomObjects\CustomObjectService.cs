﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Client;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.CustomObjects;
using commercetools.Sdk.Api.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.CustomObjects
{
    public class CustomObjectService : ICustomObjectService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CustomObjectService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public CustomObjectService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CustomObjectService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }

        public async Task<List<ICustomObject>> GetByContainer(string container)
        {
            List<ICustomObject> customObjects = new();
            try
            {
                var resultSet = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .CustomObjects()
                    .WithContainer(container)
                    .Get()
                    //.WithWhere($"value in (\"{coid}\")")
                    //.WithExpand($"key = \"{channelKey}\"")
                    .ExecuteAsync();

                if (resultSet != null)
                {
                    customObjects = resultSet.Results.ToList();
                }
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading custom object with container={container}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading custom object with container={container} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return customObjects;
        }

        public async Task<ICustomObject> CreateOrUpdate(ICustomObjectDraft draft)
        {
            ICustomObject customObject = null;
            try
            {
                customObject = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .CustomObjects()
                    .Post(draft)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while creating CustomObjects with key={draft.Key}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while creating CustomObjects with key={draft.Key} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return customObject;
        }

        public async Task<ICustomObject> Delete(string container, string key)
        {
            ICustomObject customObject = null;
            try
            {
                customObject = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .CustomObjects()
                    .WithContainerAndKey(container, key)
                    .Delete()
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while deleting CustomObjects with key={key} and container={container}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while deleting CustomObjects with key={key} and container={container} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return customObject;
        }
    }
}
