﻿namespace ITF.Lib.Common.Notifications.Messages
{
    public interface IDistributedTracing
    {
        //public IDictionary<string, string> TracingKeys { get; set; }
        // https://www.elastic.co/guide/en/apm/agent/dotnet/master/public-api.html#manually-propagating-distributed-tracing-context
        public string? DistributedTracingData { get; set; }
        public string? CausationId { get; set; }
        public string MessageId { get; set; }
        public void SetDistributedTracingData(string distributedTracingData)
        {
            DistributedTracingData = distributedTracingData;
        }
        public void SetCausationId(string causationId)
        {
            CausationId = causationId;
        }
    }
}
