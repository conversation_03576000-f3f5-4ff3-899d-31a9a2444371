﻿using commercetools.Sdk.Api.Models.Channels;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.CustomObjects;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ProductTypes;
using Google.Protobuf.WellKnownTypes;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods.Domain;
using System;
using System.Collections.Generic;
using System.Linq;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class VariantDraftExentionMethods
    {
        public static void SetChannel(this IProductVariantDraft variant, string channelId)
        {
            if (channelId == null) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) is SetAttribute<IReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) as SetAttribute<IReference>;
                if (attribute.Value is List<ChannelReference>)
                {
                    attribute.Value = new List<ChannelReference> { new ChannelReference { Id = channelId } };
                }
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new SetAttribute<ChannelReference>()
                {
                    Name = CtProductCustomAttributesNames.VariantAttributes.CHANNELS,
                    Value = new List<ChannelReference> { new ChannelReference { Id = channelId } }
                });
            }
        }

        public static void SetRelatedAccessories(this IProductVariantDraft variant, List<string> productIdList)
        {
            if (productIdList == null || productIdList.Count <= 0) return;

            List<ProductReference> productReferences = new List<ProductReference>();
            foreach(var productid in productIdList)
            {
                productReferences.Add(new ProductReference { Id = productid });
            }

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) is SetAttribute<IReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) as SetAttribute<IReference>;
                if (attribute.Value is List<ProductReference>)
                {
                    attribute.Value = productReferences;
                }
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new SetAttribute<ProductReference>()
                {
                    Name = CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES,
                    Value = productReferences
                });
            }
        }

        public static void SetVariantLabel(this IProductVariantDraft variant, string language, string value)
        {
            if (value == null) return;

            var localizedString = new LocalizedString();
            localizedString.Add(language, value);

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LABEL)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LABEL)) is LocalizedStringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LABEL)).Value = localizedString;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LocalizedStringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.LABEL, Value = localizedString });
            }
        }

        public static void SetMinQuantity(this IProductVariantDraft variant, int? value)
        {
            if (value == null) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)) is commercetools.Sdk.Api.Models.Products.LongAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value = (long?)value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LongAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY, Value = (long?)value });
            }
        }
        public static void SetMaxQuantity(this IProductVariantDraft variant, int? value)
        {
            if (value == null) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)) is commercetools.Sdk.Api.Models.Products.LongAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value = (long?)value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LongAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY, Value = (long?)value });
            }
        }
        public static void SetDeliveryType(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE)) is commercetools.Sdk.Api.Models.Products.PlainEnumAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new PlainEnumAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE, Value = value });
            }
        }
        public static void SetProductType(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)) is commercetools.Sdk.Api.Models.Products.PlainEnumAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new PlainEnumAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE, Value = value });
            }
        }
        public static void SetCmsPublishedOn(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) is IAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) as IAttribute;
                if (attribute.Value is List<IAttributePlainEnumValue>)
                {
                    attribute.Value = new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = value, Label = value } };
                }
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                //variant.Attributes.Add(new SetAttribute<List<IAttributePlainEnumValue>>()
                //{
                //    Name = CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON,
                //    Value = new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = value, Label = value } }
                //});
                variant.Attributes.Add(new SetAttribute<IAttributePlainEnumValue>()
                {
                    Name = CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON,
                    Value = new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = value, Label = value } }
                });
            }
        }
        public static void SetSize(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) is LocalizedStringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LocalizedStringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.SIZE, Value = value });
            }
        }
        public static void SetColor(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) is LocalizedStringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LocalizedStringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.COLOUR, Value = value });
            }
        }

        //public static void SetAvailableColors(this IProductVariantDraft variant, List<AttributeLocalizedEnumValue> values)
        //public static void SetAvailableColors(this IProductVariantDraft variant, List<AttributeLocalizedEnumValue> values)
        //{
        //    if (values == null || values.Count == 0) return;

        //    if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.AVAILABLE_COLOURS)) &&
        //            variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.AVAILABLE_COLOURS)) is SetAttribute<IAttributeLocalizedEnumValue>)
        //    {
        //        variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.AVAILABLE_COLOURS)).Value = values.Select(v => v.Key);
        //    }
        //    else
        //    {
        //        if (variant.Attributes == null)
        //        {
        //            variant.Attributes = new List<IAttribute>();
        //        }
        //        variant.Attributes.Add(new SetAttribute<IAttributeLocalizedEnumValue>() { Name = CtProductCustomAttributesNames.VariantAttributes.AVAILABLE_COLOURS, Value = values.Select(v => v.Key) });
        //    }
        //}

        public static void SetProductClassification(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION)) is commercetools.Sdk.Api.Models.Products.LocalizedStringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new LocalizedStringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION, Value = value });
            }
        }

        public static void SetProductDeliveryCategorisation(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION)) is StringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new StringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION, Value = value });
            }
        }

        public static void SetSwedishProductOnlyMembersId(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID)) is StringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new StringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID, Value = value });
            }
        }
        public static void SetSwedishGiftCardDeliveryType(this IProductVariantDraft variant, SwedishGiftCardDeliveryType value)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)) is IAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)) as IAttribute;
                if (attribute.Value is List<IAttributePlainEnumValue>)
                {
                    if(value == SwedishGiftCardDeliveryType.NONE)
                    {
                        attribute.Value = null;
                    }
                    else
                    {
                        string stringValue = value.ToString();
                        attribute.Value = stringValue; // new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = stringValue, Label = stringValue } };
                    }
                }
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                //variant.Attributes.Add(new SetAttribute<List<IAttributePlainEnumValue>>()
                //{
                //    Name = CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON,
                //    Value = new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = value, Label = value } }
                //});
                if (value != SwedishGiftCardDeliveryType.NONE)
                {
                    string stringValue = value.ToString();
                    variant.Attributes.Add(new SetAttribute<IAttributePlainEnumValue>()
                    {
                        Name = CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE,
                        Value = stringValue // new List<IAttributePlainEnumValue> { new AttributePlainEnumValue { Key = stringValue, Label = stringValue } }
                    });
                }
                    
            }
        }

        //public static void SetVariantTaxInPercent(this IProductVariantDraft variant, decimal value)
        //{

        //    if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)) &&
        //            variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)) is DecimalAttribute)
        //    {
        //        variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)).Value = value;
        //    }
        //    else
        //    {
        //        if (variant.Attributes == null)
        //        {
        //            variant.Attributes = new List<IAttribute>();
        //        }
        //        variant.Attributes.Add(new DecimalAttribute { Name = CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT, Value = value });
        //    }
        //}


        public static void SetLegacyProductIdentifier(this IProductVariantDraft variant, string value)
        {
            if (String.IsNullOrWhiteSpace(value)) return;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)) is StringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new StringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER, Value = value });
            }
        }

        public static void SetAllowPriceDifferentFromVariantsOne(this IProductVariantDraft variant, bool value)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) is commercetools.Sdk.Api.Models.Products.BooleanAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)).Value = value;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }
                variant.Attributes.Add(new BooleanAttribute() { Name = CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS, Value = value });
            }
        }

        public static void SetSummary(this IProductVariantDraft variant, string language, string value)
        {
            if (String.IsNullOrWhiteSpace(language) || String.IsNullOrWhiteSpace(value)) return;


            var localizedString = new LocalizedString();
            localizedString.Add(language, value);

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SUMMARY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SUMMARY)) is LocalizedStringAttribute)
            {
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SUMMARY)).Value = localizedString;
            }
            else
            {
                if (variant.Attributes == null)
                {
                    variant.Attributes = new List<IAttribute>();
                }

                variant.Attributes.Add(new LocalizedStringAttribute() { Name = CtProductCustomAttributesNames.VariantAttributes.SUMMARY, Value = localizedString });
            }
        }
    }

    public static class VariantDraftExtensionMethods
    {
        //public static decimal GetSingleMarketingFee(this IProductVariantDraft variant)
        //{
        //    int minquantity = VariantExtensionMethods.GetMinQuantity(variant);
        //    if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.MARKETING_FEE)
        //        && variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.MARKETING_FEE) is MoneyAttribute
        //        && ((MoneyAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.MARKETING_FEE)).Value is CentPrecisionMoney)
        //    {
        //        var marketingFeeAttrbiute = ((CentPrecisionMoney)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.MARKETING_FEE).Value);
        //        decimal marketingFee = marketingFeeAttrbiute.CentAmount / (decimal)Math.Pow(10, marketingFeeAttrbiute.FractionDigits);
        //        if (minquantity != 0)
        //        {
        //            return marketingFee / minquantity;
        //        }
        //        else
        //        {
        //            return marketingFee;
        //        }
        //    }
        //    return 0;
        //}
        public static bool GetAllowPriceDifferentFromVariantsOne(this IProductVariantDraft variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) is commercetools.Sdk.Api.Models.Products.BooleanAttribute)
            {
                return (bool)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)).Value;
            }
            return false;
        }
        public static string GetSizeKey(this IProductVariantDraft variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) is LocalizedStringAttribute)
            {
                var attribute = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE);
                return attribute.Value.ToString();
            }
            else if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) is StringAttribute)
            {
                var attribute = (StringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE);
                return attribute.Value.ToString();
            }
            return null;
        }
        public static string GetColourKey(this IProductVariantDraft variant)
        {
            //if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR))
            //{
            //    return ((AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR)?.Value).Key;
            //}
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) is LocalizedStringAttribute)
            {
                var attribute = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR);
                return attribute.Value.ToString();
            }
            else if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) is StringAttribute)
            {
                var attribute = (StringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR);
                return attribute.Value.ToString();
            }
            return null;
        }
        public static string GetDeliveryType(this IProductVariantDraft variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE))
            {
                return variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE)?.Value.ToString();
            }
            return null;
        }
        public static int GetMinQuantity(this IProductVariantDraft variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value is long)
            {
                return (int)(long)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value;
            }
            return 0;
        }
        public static int GetMaxQuantity(this IProductVariantDraft variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value is long)
            {
                return (int)(long)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value;
            }
            return 0;
        }
        public static string GetVariantLabel(this IProductVariantDraft variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.LABEL))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.LABEL);
                if (((LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetChannelId(this IProductVariantDraft variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) is SetAttribute<IReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) as SetAttribute<IReference>;
                if (attribute.Value is List<ChannelReference>)
                {
                    var channelRefs = (attribute.Value as List<ChannelReference>);
                    return channelRefs.FirstOrDefault().Id;
                }
            }
            return null;
        }
        public static IEnumerable<string> GetRelatedAccessoriesIds(this IProductVariantDraft variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) is SetAttribute<ProductReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) as SetAttribute<ProductReference>;
                if (attribute.Value is List<ProductReference> list)
                {
                    return list.Select(r => r.Id);// (List<IProductReference>)list;
                }
            }
            return null;
        }
        public static string GetSummary(this IProductVariantDraft variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SUMMARY))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SUMMARY);
                if (((LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
    }

    public static class VariantExtensionMethods
    {

        public static bool GetAllowPriceDifferentFromVariantsOne(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)) is commercetools.Sdk.Api.Models.Products.BooleanAttribute)
            {
                return (bool)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.ALLOW_PRICE_DIFFERENT_FROM_VARIANTS)).Value;
            }
            return false;
        }
        public static int GetMinQuantity(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value is long)
            {
                return (int)(long)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MIN_QUANTITY)).Value;
            }
            return 0;
        }
        public static int GetMaxQuantity(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value is long)
            {
                return (int)(long)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.MAX_QUANTITY)).Value;
            }
            return 0;
        }
        public static string GetVariantLabel(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.LABEL))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.LABEL);
                if (((LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetLegacyProductIdentifier(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)).Value is string)
            {
                return (string)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.LEGACY_PRODUCT_IDENTIFIER)).Value;
            }
            return null;
        }


        public static string GetProductType(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)).Value is AttributeLocalizedEnumValue)
            {
                var attr = (AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.PRODUCT_TYPE)).Value;
                return (string)attr.Key;
            }
            return null;
        }

        public static SwedishGiftCardDeliveryType GetSwedishGiftCardDeliveryType(this IProductVariant variant)
        {
            if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)).Value != null &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)).Value is AttributeLocalizedEnumValue)
            {
                var attr = (AttributePlainEnumValue)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SE_GIFTCARD_DELIVERY_TYPE)).Value;

                SwedishGiftCardDeliveryType giftCardDeliverytype = SwedishGiftCardDeliveryType.NONE;
                var value = (string)attr.Key;
                if (!string.IsNullOrWhiteSpace(value))
                {
                    if (System.Enum.TryParse<SwedishGiftCardDeliveryType>(value.ToUpper(), out giftCardDeliverytype))
                    {
                        return giftCardDeliverytype;
                    }
                }
                else return SwedishGiftCardDeliveryType.NONE;

            }
            return SwedishGiftCardDeliveryType.NONE;
        }

        //public static decimal GetTaxInPercent(this IProductVariant variant)
        //{
        //    if (variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)) &&
        //            variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)).Value != null &&
        //            variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)).Value is decimal)
        //    {
        //        return (decimal)variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.TAX_IN_PERCENT)).Value;
        //    }
        //    return 0;
        //}
        public static string GetDeliveryType(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE)
                && variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE) is PlainEnumAttribute)
            {
                var attribute = (PlainEnumAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.DELIVERY_TYPE);

                if (attribute.Value is AttributePlainEnumValue)
                {
                    var enumValue = (attribute.Value as AttributePlainEnumValue);
                    return enumValue.Key;
                }
            }
            return null;
        }
        public static string GetChannelId(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) is SetAttribute<IReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CHANNELS)) as SetAttribute<IReference>;
                if (attribute.Value is List<IReference> list)
                {
                    //var channelRefs = (attribute.Value as List<ChannelReference>);
                    return list.FirstOrDefault().Id;
                }
            }
            return null;
        }
        public static List<string> GetCmsPublishedOn(this IProductVariant variant)
        {
            List<string> stores = new List<string>();

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) is IAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_PUBLISHED_ON)) as IAttribute;
                if (attribute != null && attribute.Value is List<IAttributePlainEnumValue> values)
                {
                    stores = values.Select(v => v.Key).ToList();
                }
            }
            return stores;
        }

        public static string? GetCmsUpdatedAt(this IProductVariant variant)
        {
            DateTime deliveryDate = DateTime.MinValue;

            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_UPDATED_AT)) &&
                variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.CMS_UPDATED_AT)) is StringAttribute updatedAtAttribute)
            {
                return updatedAtAttribute?.Value?.ToString() ?? null;
            }
            return null;
        }


        public static IEnumerable<string> GetRelatedAccessoriesIds(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) is SetAttribute<IReference>)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.RELATED_ACCESSORIES)) as SetAttribute<IReference>;
                if (attribute.Value is List<IReference> list)
                {
                    return list.Select(r => r.Id);// (List<IProductReference>)list;
                }
            }
            return null;
        }

        public static string GetBundleVariantsObjectId(this IProductVariant variant)
        {
            string? customObjectId = null;
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) is ReferenceAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) as ReferenceAttribute;
                if (attribute != null && attribute.Value is CustomObjectReference coReference)
                {
                    if (coReference.Id != null)
                    {

                        customObjectId = coReference.Id;
                    }
                }
            }
            return customObjectId;
        }

        public static IEnumerable<BundleVariant> GetBundleVariants(this IProductVariant variant)
        {
            List<BundleVariant> bundleVariants = new List<BundleVariant>();
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) is ReferenceAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) as ReferenceAttribute;
                if (attribute != null && attribute.Value is CustomObjectReference coReference)
                {
                    if (coReference.Obj is CustomObject customObject && coReference?.Obj?.Value != null && coReference?.Obj?.Value is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                    {
                        bundleVariants = CastToListBundleVariant(jsonElement.GetRawText());

                    }
                }
            }
            return bundleVariants;
        }

        private static List<BundleVariant> CastToListBundleVariant(string valueString)
        {
            List<BundleVariant> bundleVariants = new List<BundleVariant>();
            var variantReferences = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(valueString);
            if (variantReferences != null)
            {
                foreach (var variantReferene in variantReferences)
                {
                    BundleVariant bv = new();
                    string[] parts = variantReferene.Split(new char[] { '#' });
                    if (parts.Length >= 1)
                    {
                        bv.ProductId = parts[0];
                    }
                    if (parts.Length >= 2)
                    {
                        bv.VariantId = parts[1];
                    }
                    if (!String.IsNullOrWhiteSpace(bv.ProductId))
                    {
                        bundleVariants.Add(bv);
                    }
                }
            }
            return bundleVariants;
        }

        public static CustomObject? GetBundleVariantsCustomObject(this IProductVariant variant)
        {
            CustomObject result = null;
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) is ReferenceAttribute)
            {
                var attribute = variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.BUNDLE_VARIANTS)) as ReferenceAttribute;
                if (attribute != null && attribute.Value is CustomObjectReference coReference)
                {
                    if (coReference.Obj is CustomObject customObject)
                    {
                        result = customObject;
                    }
                }
            }
            return result;
        }

        public static string GetComposition(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COMPOSITION))
            {
                return variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COMPOSITION)?.Value.ToString();
            }
            return null;
        }
        public static string GetSummary(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SUMMARY))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SUMMARY);
                if (((LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristProductComposition(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_COMPOSITION))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_COMPOSITION);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristFlowersFoliage(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_FLOWERS_FOLIAGE))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_FLOWERS_FOLIAGE);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristIncludedContainer(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_INCLUDED_CONTAINER))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_INCLUDED_CONTAINER);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristSubstitution(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_SUBSTITUTION))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_SUBSTITUTION);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristMandatoryColors(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_MANDATORY_COLORS))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_MANDATORY_COLORS);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristMandatoryFlowers(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_MANDATORY_FLOWERS))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_MANDATORY_FLOWERS);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetFloristProductDimensions(this IProductVariant variant, string language)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_DIMENSIONS))
            {
                var localizedString = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.FLORIST_PRODUCT_DIMENSIONS);
                if (((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).ContainsKey(language))
                {
                    return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault(i => i.Key == language).Value;
                }
                return ((commercetools.Sdk.Api.Models.Common.LocalizedString)localizedString.Value).FirstOrDefault().Value;
            }
            return null;
        }
        public static string GetSizeKey(this IProductVariant variant)
        {
            //if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE))
            //{
            //    return (AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE)?.Value;
            //}
            //return null;
            if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) is LocalizedStringAttribute)
            {
                var attribute = (LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE);
                return attribute.ToString();
            }
            else if (variant.Attributes != null && variant.Attributes.Any(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.SIZE)) is LocalizedEnumAttribute)
            {
                var attribute = (LocalizedEnumAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE);
                if (attribute.Value is AttributeLocalizedEnumValue)
                {
                    var enumValue = (AttributeLocalizedEnumValue)attribute.Value;
                    return enumValue.Key;
                }
            }
            return null;
        }
        public static string GetColourKey(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) is LocalizedStringAttribute)
            {
                return ((LocalizedStringAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR)?.Value).ToString();
            }
            else if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR) &&
                    variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtProductCustomAttributesNames.VariantAttributes.COLOUR)) is LocalizedEnumAttribute)
            {
                var attribute = (LocalizedEnumAttribute)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR);
                if (attribute.Value is AttributeLocalizedEnumValue)
                {
                    var enumValue = (AttributeLocalizedEnumValue)attribute.Value;
                    return enumValue.Key;
                }
            }
            return null;
        }

        public static AttributeLocalizedEnumValue GetSize(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE))
            {
                return (AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SIZE)?.Value;
            }
            return null;
        }

        public static AttributeLocalizedEnumValue GetProductClassification(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION))
            {
                return (AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.PRODUCT_CLASSIFICATION)?.Value;
            }
            return null;
        }
        public static AttributeLocalizedEnumValue GetColour(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR))
            {
                return (AttributeLocalizedEnumValue)variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.COLOUR)?.Value;
            }
            return null;
        }
        public static string GetProductDeliveryCategorisation(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION))
            {
                return variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.PRODUCT_DELIVERY_CATEGORISATION)?.Value.ToString();
            }
            return null;
        }
        public static string GetSwedishOnlyMembersId(this IProductVariant variant)
        {
            if (variant.Attributes != null && variant.Attributes.Any(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID))
            {
                return variant.Attributes.FirstOrDefault(attr => attr.Name == CtProductCustomAttributesNames.VariantAttributes.SE_ONLY_MEMBERS_ID)?.Value.ToString();
            }
            return null;
        }
    }
}
