﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;
using System;

namespace ITF.SharedModels.Messages.Group.Florist.Pfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class PfsFloristEmailUpdatedMessage : BaseMessage<PfsFloristEmailUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier + "_" + Payload?.Email;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public PfsFloristEmailUpdatedMessage() { }


                public PfsFloristEmailUpdatedMessage(string floristIdentifier, string email)
                {
                    this.Payload = new PfsFloristEmailUpdatedPayload { FloristIdentifier = floristIdentifier, Email = email };
                }
            }


        }
    }

    public class PfsFloristEmailUpdatedPayload : LegacyPayload, IEquatable<PfsFloristEmailUpdatedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string Email { get; set; }

        public bool Equals(PfsFloristEmailUpdatedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                Email == parameter.Email
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as PfsFloristEmailUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Email
        }.GetHashCode();
    }
}
