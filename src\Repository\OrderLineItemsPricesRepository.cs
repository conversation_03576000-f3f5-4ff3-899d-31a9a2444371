﻿using ITF.SharedLibraries.MongoDB.Repository;
using ITF.SharedModels.DataModels.Order;
using MongoDB.Driver;

namespace IT.SharedLibraries.CT.Repository;
public  interface IOrderLineItemsPricesRepository : IMongoRepository<OrderLineItemsPrices>
{
    Task<OrderLineItemsPrices?> GetByOrderIdAsync(string orderId);
}

public class OrderLineItemsPricesRepository(IMongoClient mongoClient) : MongoRepository<OrderLineItemsPrices>(mongoClient, "order_lineitemsprices", "order_subset"), IOrderLineItemsPricesRepository
{
    public async Task<OrderLineItemsPrices?> GetByOrderIdAsync(string orderId)
    {
        var elements =  await Collection.FindAsync(x => x.OrderId == orderId);
        var elementsList = await elements.ToListAsync();

        return elementsList.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
    }
}
