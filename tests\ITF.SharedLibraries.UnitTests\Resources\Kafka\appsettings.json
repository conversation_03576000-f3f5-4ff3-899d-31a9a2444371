{
  "Kafka": {
    "BootStrapServers": "localhost:9092",
    "LingerMs": 0.5,
    "QueueBufferingMaxMessages": 100000,
    "TopicsToCreateConfigurations": [
      {
        "TopicName": "integrationtesttopic",
        "ReplicationFactor": 1,
        "NumberOfPartitions": 20,
        "RetentionMs": 180000 //3min
      }
    ],
    "SubscriberConfigurations": [
      {
        "AutoOffsetReset": 1,
        "ClassName": "MessageHandler",
        "EnableAutoCommit": false,
        "ManualCommitPeriod": 1,
        "EnablePartitionEof": false,
        "GroupId": "test-consumer-group",
        "SessionTimeouMs": 10000,
        "StatisticsIntervalMs": 3000,
        "TopicName": "integrationtesttopic"
      }
    ]
  }
}