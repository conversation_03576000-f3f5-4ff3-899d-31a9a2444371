﻿using ITF.SharedLibraries.ElasticSearch;
using System;

namespace ITF.SharedLibraries.EventSourcing.Projections
{
    public class ElasticSearchCheckPointPosition : BaseElastic<ElasticSearchCheckPointPosition>
    {
        public ulong PreparePosition { get; set; }
        public ulong CommitPosition { get; set; }

        protected ElasticSearchCheckPointPosition()
        {
        }

        public ElasticSearchCheckPointPosition(string checkPointName, ulong commitPosition, ulong preparePosition)
        {
            Id = checkPointName;
            CommitPosition = commitPosition;
            PreparePosition = preparePosition;
        }

        public override void SetId()
        {
            throw new NotImplementedException();
        }
    }
}
