﻿using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.Kafka.Subscriber;
using Microsoft.Extensions.DependencyInjection;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using System.Threading.Tasks;

namespace IT.Microservices.OrderReactor.Infrastructure.Handler;

public class RAOLegacyOrderHandler : KafkaBaseMessageHandler
{
    private readonly IServiceScopeFactory _scopeFactory;

    public RAOLegacyOrderHandler(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;
    }

    public override async Task HandleMessage(object data, string topic = null, int? partition = null, long? offset = null)
    {
        using var scope = _scopeFactory.CreateScope();
        var myFacade = scope.ServiceProvider.GetRequiredService<IOrderFacade>();

        var result = data switch
        {
            OrderPlacedMessage d => myFacade.Process(d, topic, partition, offset),
            OrderAssignmentMessage d => myFacade.Process(d, topic, partition, offset),
            OrderUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
            OrderManagementStatusMessage d => myFacade.Process(d, topic, partition, offset),
            OrderDeliveryCourierUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
            OrderDeliveryCourierResetedMessage d => myFacade.Process(d, topic, partition, offset),
            OrderDeliveryCourierInitializedMessage d => myFacade.Process(d, topic, partition, offset),

            InvoiceMessage d => myFacade.Process(d,topic, partition, offset),
            _ => Task.CompletedTask
        };

        await result;
    }
}
