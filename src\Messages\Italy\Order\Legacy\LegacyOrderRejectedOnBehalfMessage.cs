﻿using ITF.Lib.Common.Notifications.Messages;
using System;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderRejectedOnBehalfMessage : BaseMessage<LegacyOrderRejectedOnBehalfPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier + "-" + (string.IsNullOrWhiteSpace(Payload?.Reason) ? "empty" : Payload?.Reason);

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }
            }
        }
    }

    public class LegacyOrderRejectedOnBehalfPayload : LegacyPayload, IEquatable<LegacyOrderRejectedOnBehalfPayload>
    {
        public string OrderIdentifier { get; set; }
        public string Reason { get; set; }
        public bool Equals(LegacyOrderRejectedOnBehalfPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier
                && Reason == parameter.Reason
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderRejectedOnBehalfPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Reason
        }.GetHashCode();
    }
}
