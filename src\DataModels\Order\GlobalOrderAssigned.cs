﻿using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using System;
using System.Collections.Generic;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderAssigned
    {
        public string FloristIdentifier { get; set; }
        public string OrderIdentifier { get; set; }
        public DateTime? ToBeAcceptedBefore { get; set; }
        public decimal? DeliveryAmount { get; set; } = null;
        public List<GlobalOrderAssignedProduct> Products { get; set; } = new();

        public static implicit operator GlobalOrderAssigned(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderAssignedMessage v)
        {
            GlobalOrderAssigned obj = new GlobalOrderAssigned
            {
                DeliveryAmount = v?.Payload?.DeliveryAmount,
                FloristIdentifier = v?.Payload?.FloristIdentifier,
                OrderIdentifier = v?.Payload?.OrderIdentifier,
                ToBeAcceptedBefore = v?.Payload?.ToBeAcceptedBefore
            };
            if(v?.Payload?.Products != null)
            {
                foreach (var product in v?.Payload?.Products)
                {
                    obj.Products.Add(new GlobalOrderAssignedProduct { ExecutorAmount = product.ExecutorAmount, ProductKey = product.ProductKey, VariantKey = product.VariantKey });
                }
            }
            return obj;
        }
    }

    public class GlobalOrderAssignedProduct
    {
        public string LineItemId { get; set; }
        public string ProductKey { get; set; }
        public string VariantKey { get; set; }
        public string? RibbonText { get; set; } = null;
        public decimal? MarketingFee { get; set; } = null;
        public decimal? ExecutorAmount { get; set; } = null;

        public static implicit operator GlobalOrderAssignedProduct(ProductInformations src)
        {
            return new GlobalOrderAssignedProduct
            {
                ProductKey = src.GetProductKey(),
                VariantKey = src.GetVariantKey(),
                ExecutorAmount = src.GetExecutingFloristAmount(),
                MarketingFee = Convert.ToDecimal(src.Margin),
                RibbonText = src.RibbonText,
                LineItemId = string.Empty,
            };
        }
    }

}
