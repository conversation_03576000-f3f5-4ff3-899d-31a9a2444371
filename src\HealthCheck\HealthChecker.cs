﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.HealthCheck
{
    public class HealthChecker : IHealthCheck
    {
        private readonly IServiceProvider _serviceProvider;

        public HealthChecker(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            var hcProvider = _serviceProvider.GetService<HealthCheckProvider>();
            if (hcProvider != null)
            {
                if (hcProvider.IsHealthy())
                    return Task.FromResult(HealthCheckResult.Healthy("The MS is healthy :-)"));
                else
                    return Task.FromResult(new HealthCheckResult(context.Registration.FailureStatus, "The MS is unhealthy :-("));
            }
            else
                return Task.FromResult(HealthCheckResult.Healthy("The MS is healthy :-)"));
        }
    }
}
