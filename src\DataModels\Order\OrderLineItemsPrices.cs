﻿using ITF.Lib.Common.DomainDrivenDesign;

namespace ITF.SharedModels.DataModels.Order;

// For now only use in FR and only for orders from CT with a Bundle in CT ,
// used to store the Bundle items with their ExecutingFloristAmount into Mongo as we just have the Bundle LineItem in CT for now
// --> we still update the whole ExecutingFloristAmount of the bundle lineItem in CT by summing all the ExecutingFloristAmount bundled items sent by the RAO
// for alignment of data in CT
public class OrderLineItemsPrices : BaseClass<string>
{
    public OrderLineItemsPrices(string orderId , List<LineItemPrice> lineItemsPrices)
    {
        Id = orderId;
        OrderId = orderId;
        LineItemsPrice = lineItemsPrices;
    }

    public string OrderId { get; set; }

    public List<LineItemPrice> LineItemsPrice { get; set; }

    public override void SetId() => Id = OrderId;

}

public record LineItemPrice(string Sku, string VariantSku, decimal Price);
