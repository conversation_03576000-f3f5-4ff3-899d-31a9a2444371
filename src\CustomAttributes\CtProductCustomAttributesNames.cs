﻿namespace IT.SharedLibraries.CT.CustomAttributes
{
    public static class CtProductCustomAttributesNames
    {
        public static class VariantAttributes
        {
            public const string SUMMARY = "summary";
            public const string PRODUCT_CLASSIFICATION = "product_classification";
            public const string BUNDLED_PRODUCTS = "bundled_products";
            public const string MIN_QUANTITY = "min_quantity";
            public const string MAX_QUANTITY = "max_quantity";
            public const string SIZE = "size";
            public const string COLOUR = "colour";
            public const string MARKETING_FEE = "marketing_fee";
            public const string HAS_RIBBON_TEXT = "has_ribbon_text";
            public const string COMPOSITION = "composition";
            public const string FLORIST_PRODUCT_COMPOSITION = "florist_product_composition";
            public const string FLORIST_FLOWERS_FOLIAGE = "florist_flowers_foliage";
            public const string FLORIST_INCLUDED_CONTAINER = "florist_included_container";
            public const string FLORIST_PRODUCT_SUBSTITUTION = "florist_product_substitution";
            public const string FLORIST_PRODUCT_DIMENSIONS = "florist_product_dimensions";
            public const string FLORIST_PRODUCT_MANDATORY_FLOWERS = "florist_product_mandatory_flowers";
            public const string FLORIST_PRODUCT_MANDATORY_COLORS = "florist_product_mandatory_colors";
            public const string FLORIST_PREPARATION_TIME = "florist_preparation_time";
            public const string CMS_PUBLISHED_ON = "cms_published_on";
            public const string CMS_UPDATED_AT = "cms_updated_at";
            public const string DELIVERY_TYPE = "delivery_type";
            public const string CHANNELS = "channels";
            public const string RELATED_ACCESSORIES = "related_accessories";
            public const string PRODUCT_TYPE = "product_type";

            //public const string AVAILABLE_COLOURS = "available-colors";
            public const string PRODUCT_DELIVERY_CATEGORISATION = "product_delivery_categorisation";
            public const string LEGACY_PRODUCT_IDENTIFIER = "legacy_product_identifier";
            //public const string TAX_AMOUNT = "tax_amount";
            //public const string TAX_IN_PERCENT = "tax_in_percent";
            public const string LABEL = "label";
            public const string BUNDLE_VARIANTS = "bundle_variants";
            public const string SE_ONLY_MEMBERS_ID = "se_only_members_id";
            public const string SE_GIFTCARD_DELIVERY_TYPE = "se_giftcard_delivery_type";

        }
        public const string ALLOW_PRICE_DIFFERENT_FROM_VARIANTS = "allow_price_different_from_variants";
        public const string LEGAL_NOTICE = "legal_notice";

        public const string LOCALIZED_ATTRIBUTE_KEY = "it";
    }
}