﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.Legacy.Messages
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class FloristCourierDeliveryEnabledMessage : BaseMessage<FloristCourierDeliveryEnabledPayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload.FloristId;
            }
        }
    }
}
