﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderRejectedMessage : BaseMessage<LegacyOrderRejectedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier + "-" + Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                
            }
        }
    }

    public class LegacyOrderRejectedPayload : LegacyPayload, IEquatable<LegacyOrderRejectedPayload>
    {
        public string FloristIdentifier { get; set; }
        public string OrderIdentifier { get; set; }
        public bool Equals(LegacyOrderRejectedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                OrderIdentifier == parameter.OrderIdentifier
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderRejectedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            OrderIdentifier
        }.GetHashCode();
    }
}
