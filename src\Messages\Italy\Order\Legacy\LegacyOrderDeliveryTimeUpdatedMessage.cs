﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums;
using System;
using System.Collections;
using System.Collections.Generic;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Order.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyOrderDeliveryTimeUpdatedMessage : BaseMessage<LegacyOrderDeliveryTimeUpdatedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.OrderIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public static implicit operator LegacyOrderDeliveryTimeUpdatedMessage((string ctOrderId, OrderUpdatedMessage message , Dictionary<string,string> fieldsUpdated) src)
                {
                    var payload = src.message.Payload;
                    var legacyOrderDeliveryTimeUpdatedMessage = new LegacyOrderDeliveryTimeUpdatedMessage
                    {
                        CausationId = src.message.CausationId,
                        CreatedAt = DateTime.Now,
                        DistributedTracingData = src.message.DistributedTracingData,
                        MessageId = Guid.NewGuid().ToString(),
                        NbTry = 0,
                        Payload = new LegacyOrderDeliveryTimeUpdatedPayload
                        {
                            EventDate = payload.EventDate,
                            EventID = payload.EventID,
                            OrderIdentifier = src.ctOrderId,
                            Moment = src.fieldsUpdated.TryGetValue("Moment", out string? momentValue) && !string.IsNullOrWhiteSpace(momentValue) && int.TryParse(momentValue.Trim(), out int numericMomentValue) && Enum.IsDefined(typeof(MomentEnum), numericMomentValue) ? (MomentEnum)numericMomentValue : null,
                            Time = src.fieldsUpdated.TryGetValue("Time", out string? timeValue) && !string.IsNullOrWhiteSpace(timeValue) ? timeValue : "",
                        }
                    };


                    return legacyOrderDeliveryTimeUpdatedMessage;
                }
            }
        }
    }

    public class LegacyOrderDeliveryTimeUpdatedPayload : LegacyPayload, IEquatable<LegacyOrderDeliveryTimeUpdatedPayload>
    {
        public string OrderIdentifier { get; set; }
        public MomentEnum? Moment { get; set; }
        public string? Time { get; set; }

        public bool Equals(LegacyOrderDeliveryTimeUpdatedPayload parameter)
        {
            return (OrderIdentifier == parameter.OrderIdentifier &&
                Moment == parameter.Moment &&
                Time == parameter.Time
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyOrderDeliveryTimeUpdatedPayload);
        }

        public override int GetHashCode() => new
        {
            OrderIdentifier,
            Moment,
            Time
        }.GetHashCode();
    }

}
