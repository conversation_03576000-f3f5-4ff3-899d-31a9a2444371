﻿using ITF.SharedModels.Group.Enums;
using System;

namespace ITF.SharedModels.DataModels.Order
{
    public class GlobalOrderDeliveryTimeUpdated
    {
        public string OrderIdentifier { get; set; }
        public MomentEnum? Moment { get; set; }
        public string? Time { get; set; }

        public static implicit operator GlobalOrderDeliveryTimeUpdated(Messages.Italy.Order.Legacy.Messages.V1.LegacyOrderDeliveryTimeUpdatedMessage v)
        {
            return new GlobalOrderDeliveryTimeUpdated
            {
                Moment = v?.Payload?.Moment,
                OrderIdentifier = v?.Payload.OrderIdentifier,
                Time = v?.Payload?.Time
            };
        }
    }
}
