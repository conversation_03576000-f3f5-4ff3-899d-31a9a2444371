﻿using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using Polly.Registry;
using Polly.Timeout;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;

namespace ITF.SharedLibraries.HttpClient.Polly
{
    public static class Extensions
    {
        public static IHttpClientBuilder AddHttpClientWithPolicy<T, H>(this IServiceCollection app, IConfiguration config, string varEnv)
            where T : class, IHttpClient
            where H : class, T
        {
            var configuration = config.Get<Configuration>(varEnv);

            if (configuration.DefaultConnectionLimit != 0)
                ServicePointManager.DefaultConnectionLimit = configuration.DefaultConnectionLimit;

            ServicePointManager.Expect100Continue = !configuration.DisableExpect100ToContinue;
            ServicePointManager.UseNagleAlgorithm = !configuration.DisableNagleAlgorithm;

            if (configuration.WorkerThreads > 0 && configuration.CompletionPortThreads > 0)
                System.Threading.ThreadPool.SetMinThreads(configuration.WorkerThreads, configuration.CompletionPortThreads);

            // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#use-case-applying-timeouts

            int httpTimeout = 60;
            if (configuration.HttpTimeoutInSeconds > 0)
                httpTimeout = configuration.HttpTimeoutInSeconds;

            int handlerLifetime = 5;
            if (configuration.HandlerLifetime > 0)
                handlerLifetime = configuration.HandlerLifetime;

            int policyTimeout = 20;
            if (configuration.PolicyTimeoutInSeconds > 0)
                policyTimeout = configuration.PolicyTimeoutInSeconds;

            var httpClient = app.AddHttpClient<T, H>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(httpTimeout);
            });

            if (configuration.DisableCookieAffinity)
            {
                httpClient.ConfigurePrimaryHttpMessageHandler(() =>
                {
                    return new HttpClientHandler()
                    {
                        UseCookies = !configuration.DisableCookieAffinity,
                    };
                });
            };
            return httpClient
            .SetHandlerLifetime(TimeSpan.FromMinutes(handlerLifetime))
            .AddPolicyHandler((services, request)
                => HttpPolicyExtensions.HandleTransientHttpError()
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromSeconds(1),
                    TimeSpan.FromSeconds(5),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(20),
                },
                onRetry: (outcome, timespan, retryAttempt, context) =>
                {
                    var logger = services.GetService<ILoggerFactory>().CreateLogger("Polly");

                    // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#configuring-policies-to-use-services-registered-with-di-such-as-iloggert
                    logger?.LogWarning(outcome.Exception, "Delaying for {delay}ms, then making retry {retry}.", timespan.TotalMilliseconds, retryAttempt);
                }
            ))
            .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(policyTimeout));
        }

        public static IHttpClientBuilder AddHttpClientWithPolicy<T, H>(this IServiceCollection app, IConfiguration config, string varEnv, IEnumerable<TimeSpan> sleepDurations)
            where T : class, IHttpClient
            where H : class, T
        {
            var configuration = config.Get<Configuration>(varEnv);

            if (configuration.DefaultConnectionLimit != 0)
                ServicePointManager.DefaultConnectionLimit = configuration.DefaultConnectionLimit;

            ServicePointManager.Expect100Continue = !configuration.DisableExpect100ToContinue;
            ServicePointManager.UseNagleAlgorithm = !configuration.DisableNagleAlgorithm;

            if (configuration.WorkerThreads > 0 && configuration.CompletionPortThreads > 0)
                System.Threading.ThreadPool.SetMinThreads(configuration.WorkerThreads, configuration.CompletionPortThreads);

            // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#use-case-applying-timeouts

            int httpTimeout = 60;
            if (configuration.HttpTimeoutInSeconds > 0)
                httpTimeout = configuration.HttpTimeoutInSeconds;

            int handlerLifetime = 5;
            if (configuration.HandlerLifetime > 0)
                handlerLifetime = configuration.HandlerLifetime;

            int policyTimeout = 20;
            if (configuration.PolicyTimeoutInSeconds > 0)
                policyTimeout = configuration.PolicyTimeoutInSeconds;

            var httpClient = app.AddHttpClient<T, H>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(httpTimeout);
            });

            if (configuration.DisableCookieAffinity)
            {
                httpClient.ConfigurePrimaryHttpMessageHandler(() =>
                {
                    return new HttpClientHandler()
                    {
                        UseCookies = !configuration.DisableCookieAffinity,
                    };
                });
            };

            return httpClient
            .SetHandlerLifetime(TimeSpan.FromMinutes(handlerLifetime))
            .AddPolicyHandler((services, request)
                => HttpPolicyExtensions.HandleTransientHttpError()
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(sleepDurations,
                onRetry: (outcome, timespan, retryAttempt, context) =>
                {
                    var logger = services.GetService<ILoggerFactory>().CreateLogger("Polly");

                    // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#configuring-policies-to-use-services-registered-with-di-such-as-iloggert
                    logger?.LogWarning(outcome.Exception, "Delaying for {delay}ms, then making retry {retry}.", timespan.TotalMilliseconds, retryAttempt);
                }
            ))
            .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(policyTimeout));
        }

        public static IHttpClientBuilder AddHttpClientWithPolicy<T, H>(this IServiceCollection app, IConfiguration config, string varEnv, string policyName, PolicyRegistry policyRegistry)
            where T : class, IHttpClient
            where H : class, T
        {
            var configuration = config.Get<Configuration>(varEnv);

            app.AddPolicyRegistry(policyRegistry);

            if (configuration.DefaultConnectionLimit != 0)
                ServicePointManager.DefaultConnectionLimit = configuration.DefaultConnectionLimit;

            ServicePointManager.Expect100Continue = !configuration.DisableExpect100ToContinue;
            ServicePointManager.UseNagleAlgorithm = !configuration.DisableNagleAlgorithm;

            if (configuration.WorkerThreads > 0 && configuration.CompletionPortThreads > 0)
                System.Threading.ThreadPool.SetMinThreads(configuration.WorkerThreads, configuration.CompletionPortThreads);

            // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#use-case-applying-timeouts

            int httpTimeout = 60;
            if (configuration.HttpTimeoutInSeconds > 0)
                httpTimeout = configuration.HttpTimeoutInSeconds;

            int handlerLifetime = 5;
            if (configuration.HandlerLifetime > 0)
                handlerLifetime = configuration.HandlerLifetime;

            int policyTimeout = 20;
            if (configuration.PolicyTimeoutInSeconds > 0)
                policyTimeout = configuration.PolicyTimeoutInSeconds;

            var httpClient = app.AddHttpClient<T, H>(client =>
            {
                client.Timeout = TimeSpan.FromSeconds(httpTimeout);
            });

            if (configuration.DisableCookieAffinity)
            {
                httpClient.ConfigurePrimaryHttpMessageHandler(() =>
                {
                    return new HttpClientHandler()
                    {
                        UseCookies = !configuration.DisableCookieAffinity,
                    };
                });
            };
            return httpClient
            // https://github.com/App-vNext/Polly/wiki/Polly-and-HttpClientFactory#configuring-httpclientfactory-policies-to-use-an-iloggert-from-the-call-site
            .AddPolicyHandlerFromRegistry(policyName);
        }

        public static IPolicyRegistry<string> AddPolicyRegistry(IServiceCollection serviceCollection)
             => serviceCollection.AddPolicyRegistry();

        public static void AddRetryPolicy(IPolicyRegistry<string> policyRegistry, TimeSpan[] backoff, string policyName)
        {
            var retryPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(backoff,
                onRetry: (outcome, timespan, retryAttempt, context) =>
                {
                    var logger = Log.ForContext(typeof(Policy));
                    logger?.Warning(outcome.Exception, "Delaying for {delay}ms, then making retry {retry}.", timespan.TotalMilliseconds, retryAttempt);
                });

            policyRegistry.Add(policyName, retryPolicy);
        }

        public static void AddTimeOutPolicy(IPolicyRegistry<string> policyRegistry, TimeSpan timeOut, string policyName)
        {
            var timeOutPolicy = Policy.TimeoutAsync<HttpResponseMessage>(timeOut);

            policyRegistry.Add(policyName, timeOutPolicy);
        }
    }
}
