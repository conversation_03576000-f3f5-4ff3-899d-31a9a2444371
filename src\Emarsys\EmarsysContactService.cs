﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using ITF.Lib.Common.Error;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ITF.SharedLibraries.Emarsys.Models.Entity;
using PhoneNumbers;
using commercetools.Sdk.Api.Models.Customers;

namespace ITF.SharedLibraries.Emarsys;

public class EmarsysContactService(IEmarsysHttpService emarsysHttpService, ILogger<EmarsysContactService> logger, IOptionsMonitor<EmailEmarsysSettings> settings, IOptionsMonitor<EmarsysFieldsIds> emarsysFieldsIds) : IEmarsysContactService
{
    public async Task<Result<Contact, Error>> GetContact(string email)
    {
        try
        {
            var res = await emarsysHttpService.GetContactAsync(email.ToLower().Trim());

            if (res != null && res.replyText == "OK")
            {
                if (res.data?.result.Count > 0)
                {
                    return res.data.result.First();
                }
                else
                {
                    return new Error("Not Found", $"Contact not found for email {email}");
                }
            }

            return new Error("Unknown Error", $"An error has been raised with the Emarsys API by getting contact with the email {email}");

        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {method}", nameof(GetContact));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<List<string>, Error>> CreateOrUpdateContactForOrderConfirmation(OrderEmail email, bool newsletterOptIn = false, bool smsOptIn = false)
    {
        Contact? emarsysContact = null;

        var res = await GetContact(email.Customer.Email.ToLower().Trim());

        if (res.IsSuccess)
            emarsysContact = res.Value;

        Dictionary<string, string> fields = GetFields(email, emarsysContact, newsletterOptIn, smsOptIn);

        logger.LogInformation("Contact informations sent for the order confirmation email: {contact}", fields);

        try
        {
            var response = await emarsysHttpService.CreateOrUpdateContactAsync("3", fields);

            if (response != null && response.replyText == "OK")
            {
                return response.data?.ids.Count > 0 ? response.data?.ids : new Error("Not Updated", $"Contact have not been created or updated for email {email.Customer.Email}");
            }

            return new Error("Unknown Error", $"An error has been raised with the Emarsys API by creating or updating contact with the email {email.Customer.Email}");

        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {method}", nameof(CreateOrUpdateContact));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<List<string>, Error>> CreateOrUpdateContactForAbandonnedCart(CartEmail email)
    {
        Contact? emarsysContact = null;

        var res = await GetContact(email.Customer.Email.ToLower().Trim());

        if (res.IsSuccess)
        {
            emarsysContact = res.Value;
        }

        Dictionary<string, string> fields = GetFields(email, emarsysContact);

        logger.LogInformation("Contact informations sent for the abandoned cart email: {contact}", fields);

        try
        {
            var response = await emarsysHttpService.CreateOrUpdateContactAsync("3", fields);

            if (response != null && response.replyText == "OK")
            {
                return response.data?.ids.Count > 0 ? response.data?.ids : new Error("Not Updated", $"Contact have not been created or updated for email {email.Customer.Email}");
            }

            return new Error("Unknown Error", $"An error has been raised with the Emarsys API by creating or updating contact with the email {email.Customer.Email}");

        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {method}", nameof(CreateOrUpdateContact));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<List<string>, Error>> CreateOrUpdateContact(CustomerEmail customer)
    {
        Contact? emarsysContact = null;
        var res = await GetContact(customer.Email.ToLower().Trim());

        if (res.IsSuccess)
        {
            emarsysContact = res.Value;
        }

        var fields = GetFields(customer, emarsysContact);

        logger.LogInformation("Contact informations sent for the email: {contact}", fields);

        try
        {
            var response = await emarsysHttpService.CreateOrUpdateContactAsync("3", fields);

            if (response != null && response.replyText == "OK")
            {
                return response.data?.ids.Count > 0 ? response.data?.ids : new Error("Not Updated", $"Contact have not been created or updated for email {customer.Email}");
            }

            return new Error("Unknown Error", $"An error has been raised with the Emarsys API by creating or updating contact with the email {customer.Email}");

        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {method}", nameof(CreateOrUpdateContact));
            return new Error(e, "An error has been raised.");
        }
    }

    public static string GenerateUniqueContactId(string email)
    {
        //var emailCheckedGmail = "";

        ////Remove any dots before '@' if gmail address
        //string[] emailParts = email.Split('@');
        //if (emailParts[1].ToLower().Trim().Contains("gmail"))
        //{
        //  emailCheckedGmail = String.Concat(emailParts[0].Replace(".", ""), "@", emailParts[1]);
        //}
        //else
        //{
        //  emailCheckedGmail = email;
        //}

        //Email to lower case and trim both on start and end
        var emailInLowerCaseAndTrimmed = email.ToLower().Trim();

        //Encrypt in SHA256 and return an hexadecimal string
        SHA256 sha256Hash = SHA256.Create();
        byte[] hashInBytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(emailInLowerCaseAndTrimmed));
        var uniqueContactId = string.Concat(Array.ConvertAll(hashInBytes, b => b.ToString("x2")));

        return uniqueContactId;
    }

    public static bool IsMobilePhoneNumber(PhoneNumber phoneNumber, string country)
    {
        if (country == "DK")
        {
            return true;
        }

        var phoneNumberUtil = PhoneNumberUtil.GetInstance();

        if (!phoneNumberUtil.IsValidNumber(phoneNumber)) { return false; }

        var isMobilePhone = false;

        if (phoneNumberUtil.GetNumberType(phoneNumber) == PhoneNumberType.MOBILE)
        {
            isMobilePhone = true;
        }

        //switch (country)
        //{
        //  case "FR":
        //    isMobilePhone = Regex.Match(phoneNumber, @"^(?:(?:\+|00)33)\s*[6-7](?:[\s.-]*\d{2}){4}$").Success;
        //    break;
        //  case "ES":
        //    isMobilePhone = Regex.Match(phoneNumber.Replace(" ", "").Trim(), @"^(?:(?:\+|00)34)\s*[6](?:[\s.-]*\d{2}){4}$").Success;
        //    break;
        //  case "IT":
        //    isMobilePhone = Regex.Match(phoneNumber.Replace(" ", "").Trim(), @"^(?:(?:\+|00)39)\s*[3]\d{9}$").Success;
        //    break;
        //  case "PT":
        //    isMobilePhone = Regex.Match(phoneNumber.Replace(" ", "").Trim(), @"^(?:(?:\+|00)35)\s*[1](?:[\s.-]*\d{2}){4}$").Success;
        //    break;
        //  default:
        //    return isMobilePhone;
        //}

        return isMobilePhone;
    }

    public static string FormatPhoneNumber(PhoneNumber phoneNumber, string country)
    {
        var phoneNumberUtil = PhoneNumberUtil.GetInstance();
        return phoneNumberUtil.Format(phoneNumber, PhoneNumberFormat.E164);
    }

    public string GetEmarsysCountryId(string country)
    {
        var id = "";

        switch (country)
        {
            case "France":
                id = emarsysFieldsIds.CurrentValue.CountryFrance;
                break;
            case "FR":
                id = emarsysFieldsIds.CurrentValue.CountryFrance;
                break;
            case "ES":
                id = emarsysFieldsIds.CurrentValue.CountrySpain;
                break;
            case "IT":
                id = emarsysFieldsIds.CurrentValue.CountryItaly;
                break;
            case "PT":
                id = emarsysFieldsIds.CurrentValue.CountryPortugal;
                break;
            case "DK":
                id = emarsysFieldsIds.CurrentValue.CountryDenmark;
                break;
            case "SE":
                id = emarsysFieldsIds.CurrentValue.CountrySweden;
                break;
        }

        return id;
    }

    public async Task<Result<List<Contact>, Error>> GetContactsBatch(List<string> emails)
    {
        try
        {
            ContactPayload? res = await emarsysHttpService.GetContactAsync(emails);

            if (res != null && res.replyText == "OK")
            {
                if (res.data?.result.Count > 0)
                {
                    return res.data.result;
                }
                else
                {
                    return new Error("Not Found", $"Contacts not found");
                }
            }

            return new Error("Unknown Error", $"An error has been raised with the Emarsys API on getting contacts");

        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {Method}", nameof(GetContactsBatch));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<OrderEmail>> emails)
    {
        try
        {
            List<Dictionary<string, string>> contacts = [];
            foreach (UpsertContactInfo<OrderEmail> email in emails)
            {
                var fields = GetFields(email.EmailData, email.Contact, email.NewsletterOptIn, email.SmsOptIn);
                contacts.Add(fields);
            }

            ContactCreationBatchPayload response = await emarsysHttpService.CreateOrUpdateContactsAsync("3", contacts);

            if (response != null && response.replyText == "OK")
            {
                Dictionary<string, string?> upsertResult;
                if (response.data?.ids.Count > 0)
                {
                    upsertResult = GetUpsertResults(emails, response);
                }
                else
                    return new Error("Not Updated", $"Contacts have not been created or updated for {nameof(OrderEmail)}");

                return upsertResult;
            }

            return new Error("Unknown Error", $"An error has been raised by the Emarsys API for creating or updating contacts for {nameof(OrderEmail)}");
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {Method} for {Email}", nameof(UpsertContactBatch), nameof(OrderEmail));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<CartEmail>> emails)
    {
        try
        {
            List<Dictionary<string, string>> contacts = [];
            foreach (UpsertContactInfo<CartEmail> email in emails)
            {
                var fields = GetFields(email.EmailData, email.Contact);
                contacts.Add(fields);
            }

            ContactCreationBatchPayload response = await emarsysHttpService.CreateOrUpdateContactsAsync("3", contacts);

            if (response != null && response.replyText == "OK")
            {
                Dictionary<string, string?> upsertResult;
                if (response.data?.ids.Count > 0)
                {
                    upsertResult = GetUpsertResults(emails, response);
                }
                else
                    return new Error("Not Updated", $"Contacts have not been created or updated for {nameof(CartEmail)}");

                return upsertResult;
            }

            return new Error("Unknown Error", $"An error has been raised by the Emarsys API for creating or updating contacts for {nameof(CartEmail)}");
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {Method} for {Email}", nameof(UpsertContactBatch), nameof(CartEmail));
            return new Error(e, "An error has been raised.");
        }
    }

    public async Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<CustomerEmail>> emails)
    {
        try
        {
            List<Dictionary<string, string>> contacts = [];
            foreach (UpsertContactInfo<CustomerEmail> email in emails)
            {
                var fields = GetFields(email.EmailData, email.Contact);
                contacts.Add(fields);
            }

            ContactCreationBatchPayload response = await emarsysHttpService.CreateOrUpdateContactsAsync("3", contacts);

            if (response != null && response.replyText == "OK")
            {
                Dictionary<string, string?> upsertResult;
                if (response.data?.ids.Count > 0)
                {
                    upsertResult = GetUpsertResults(emails, response);
                }
                else
                    return new Error("Not Updated", $"Contacts have not been created or updated for {nameof(CustomerEmail)}");

                return upsertResult;
            }

            return new Error("Unknown Error", $"An error has been raised by the Emarsys API for creating or updating contacts for {nameof(CustomerEmail)}");
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error in {Method} for {Email}", nameof(UpsertContactBatch), nameof(CustomerEmail));
            return new Error(e, "An error has been raised.");
        }
    }

    private static Dictionary<string, string?> GetUpsertResults<T>(List<UpsertContactInfo<T>> contacts, ContactCreationBatchPayload payload)
    {
        Dictionary<string, string?> upsertResult = [];
        foreach (string contact in contacts.Select(c => c.Email))
        {
            upsertResult.Add(contact, null);
        }

        if (payload.data!.ids.Count != contacts.Count)
        {
            // Get the first error we find on each email
            foreach (var error in payload.data!.errors.Where(e => upsertResult.ContainsKey(e.Key)))
            {
                upsertResult[error.Key] = error.Value[0].errorMsg;
            }
        }

        return upsertResult;
    }

    private Dictionary<string, string> GetFields(OrderEmail email, Contact? emarsysContact, bool newsletterOptIn, bool smsOptIn)
    {
        Dictionary<string, string> fields = new() { { emarsysFieldsIds.CurrentValue.ContactId, GenerateUniqueContactId(email.Customer.Email) } };
        var phoneNumberUtil = PhoneNumberUtil.GetInstance();

        if (!string.IsNullOrWhiteSpace(email.Customer.FirstName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.FirstName, email.Customer.FirstName);
        }
        else if (!string.IsNullOrWhiteSpace(email.BillingAddress.FirstName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.FirstName, email.BillingAddress.FirstName);
        }

        if (!string.IsNullOrWhiteSpace(email.Customer.LastName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.LastName, email.Customer.LastName);
        }
        else if (!string.IsNullOrWhiteSpace(email.BillingAddress.LastName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.LastName, email.BillingAddress.LastName);
        }

        //================ Check Civility ==================
        //See https://help.emarsys.com/hc/en-us/articles/115004634749-Overview-Single-choice-fields-and-their-values
        if (!string.IsNullOrWhiteSpace(email.Customer.Salutation))
        {
            if (email.Customer.Salutation == SalutationEmailEnum.Mr.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "1"); //Mr
            }
            else if (email.Customer.Salutation == SalutationEmailEnum.Ms.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "2"); //Ms
            }
            else if (email.Customer.Salutation == SalutationEmailEnum.Mx.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "6"); //Mx
            }
        }
        else if (!string.IsNullOrWhiteSpace(email.BillingAddress.Salutation))
        {
            if (email.BillingAddress.Salutation == SalutationEmailEnum.Mr.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "1"); //Mr
            }
            else if (email.BillingAddress.Salutation == SalutationEmailEnum.Ms.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "2"); //Ms
            }
            else if (email.BillingAddress.Salutation == SalutationEmailEnum.Mx.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "6"); //Mx
            }
        }


        if (!string.IsNullOrWhiteSpace(email.Customer.Email))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.Email, email.Customer.Email.ToLower().Trim());
        }

        try
        {
            if (!string.IsNullOrWhiteSpace(email.Customer.Phone))
            {
                if (settings.CurrentValue.Country != "IT" || (settings.CurrentValue.Country == "IT" && smsOptIn == true))
                {
                    var customerPhoneNumber = phoneNumberUtil.Parse(email.Customer.Phone, settings.CurrentValue.Country);

                    if (IsMobilePhoneNumber(customerPhoneNumber, settings.CurrentValue.Country))
                    {
                        fields.Add(emarsysFieldsIds.CurrentValue.Mobile, FormatPhoneNumber(customerPhoneNumber, settings.CurrentValue.Country));
                    }
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Phone number Format Exception for email {email}: {phone}", email.Customer.Email, email.Customer.Phone);
        }

        if (!string.IsNullOrWhiteSpace(email.BillingAddress.Street))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.Address, email.BillingAddress.Street);
        }

        if (!string.IsNullOrWhiteSpace(email.BillingAddress.ZipCode))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.PostalCode, email.BillingAddress.ZipCode);
        }

        if (!string.IsNullOrWhiteSpace(email.BillingAddress.City))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.City, email.BillingAddress.City);
        }

        if (!string.IsNullOrWhiteSpace(email.BillingAddress.Country))
        {
            var id = GetEmarsysCountryId(email.BillingAddress.Country);
            if (!string.IsNullOrWhiteSpace(id))
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Country, id);
            }
        }

        if (!string.IsNullOrWhiteSpace(email.Customer.CompanyName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.CompanyName, email.Customer.CompanyName);
        }

        //================ Customer Type ==================

        if (!string.IsNullOrWhiteSpace(email.Customer.Type))
        {
            if (email.Customer.Type == "B2B")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2B, "1");
            }
            else if (email.Customer.Type == "B2F")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2F, "1");
            }
            else
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2C, "1");
            }
        }
        else
        {
            fields.Add(emarsysFieldsIds.CurrentValue.B2C, "1");
        }

        //================ OPT-IN Management ==================

        //TODO : No Opt-in for partners orders

        fields.Add(emarsysFieldsIds.CurrentValue.OptIn, "1");
        fields.Add(emarsysFieldsIds.CurrentValue.MobileSmsOptIn, "1");

        if (emarsysContact != null)
        {
            if (settings.CurrentValue.Country == "FR")
            {

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailFr))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailFr, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInSmsFr))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsFr, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInOriginFr)
                    && (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailFr) || string.IsNullOrWhiteSpace(emarsysContact.OptInSmsFr)))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginFr, "1");
                }

                //if (emarsysContact.OptInAbandonedCartFr == null)
                //{
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartFr, "1");
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInOriginFr, "1");
                //}
            }

            if (settings.CurrentValue.Country == "ES")
            {
                if (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailEs))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailEs, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInSmsEs))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsEs, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInOriginEs)
                    && (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailEs) || string.IsNullOrWhiteSpace(emarsysContact.OptInSmsEs)))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginEs, "1");
                }

                //if (emarsysContact.OptInAbandonedCartEs == null)
                //{
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartEs, "1");
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInOriginEs, "1");
                //}
            }

            if (settings.CurrentValue.Country == "IT")
            {
                if ((string.IsNullOrWhiteSpace(emarsysContact.OptInEmailIt) || emarsysContact.OptInEmailIt == "2") && newsletterOptIn == true)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailIt, "1");
                }

                if ((string.IsNullOrWhiteSpace(emarsysContact.OptInSmsIt) || emarsysContact.OptInSmsIt == "2") && smsOptIn == true)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsIt, "1");
                }

                if (string.IsNullOrEmpty(emarsysContact.OptInOriginIt)
                    && (((string.IsNullOrWhiteSpace(emarsysContact.OptInEmailIt) || emarsysContact.OptInEmailIt == "2") && newsletterOptIn == true)
                        || ((string.IsNullOrWhiteSpace(emarsysContact.OptInSmsIt) || emarsysContact.OptInSmsIt == "2") && smsOptIn == true)))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginIt, "1");
                }

                //if (emarsysContact.OptInAbandonedCartIt == null && newsletterOptIn == true)
                //{
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartIt, "1");
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInOriginIt, "1");
                //}
            }

            if (settings.CurrentValue.Country == "PT")
            {
                if (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailPt))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailPt, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInSmsPt))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsPt, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInOriginPt)
                    && (string.IsNullOrWhiteSpace(emarsysContact.OptInEmailPt) || string.IsNullOrWhiteSpace(emarsysContact.OptInSmsPt)))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginPt, "1");
                }

                //if (emarsysContact.OptInAbandonedCartPt == null)
                //{
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartPt, "1");
                //    fields.Add(_emarsysFieldsIds.CurrentValue.OptInOriginPt, "1");
                //}
            }

            if (settings.CurrentValue.Country == "DK")
            {

                if (email.OptInSms)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsDk, "1");
                }

                if (email.OptInNewsletter)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailDk, "1");
                }

                if (string.IsNullOrWhiteSpace(emarsysContact.OptInOriginDk))
                {
                    fields[emarsysFieldsIds.CurrentValue.OptInOriginDk] = "1";
                }
            }

        }
        else
        {
            if (settings.CurrentValue.Country == "FR")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailFr, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsFr, "1");
                //fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartFr, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginFr, "1");
            }

            if (settings.CurrentValue.Country == "ES")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailEs, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsEs, "1");
                //fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartEs, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginEs, "1");
            }

            if (settings.CurrentValue.Country == "IT")
            {
                if (newsletterOptIn == true)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailIt, "1");
                    //fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartIt, "1");
                }

                if (smsOptIn == true)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsIt, "1");
                }

                if (newsletterOptIn == true || smsOptIn == true)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginIt, "1");
                }
            }

            if (settings.CurrentValue.Country == "PT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailPt, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsPt, "1");
                //fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartPt, "1");
                fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginPt, "1");
            }

            if (settings.CurrentValue.Country == "DK")
            {

                if (email.OptInSms)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInSmsDk, "1");
                }

                if (email.OptInNewsletter)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInEmailDk, "1");
                }

                if (email.OptInNewsletter || email.OptInSms)
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.OptInOriginDk, "1");
                }
            }

        }

        //================ End of OPT-IN Management ==================

        if (emarsysContact == null)
        {
            if (settings.CurrentValue.Country == "FR")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedFr, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "ES")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedEs, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "IT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedIt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "PT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedPt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "DK")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedDk, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }
        }

        if (settings.CurrentValue.Country == "FR")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactFr, "1");
        }

        if (settings.CurrentValue.Country == "ES")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactEs, "1");
        }

        if (settings.CurrentValue.Country == "IT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactIt, "1");
        }

        if (settings.CurrentValue.Country == "PT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactPt, "1");
        }

        if (settings.CurrentValue.Country == "DK")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactDk, "1");
        }

        return fields;
    }

    private Dictionary<string, string> GetFields(CartEmail email, Contact? emarsysContact)
    {
        Dictionary<string, string> fields = new()
        {
            { emarsysFieldsIds.CurrentValue.ContactId, GenerateUniqueContactId(email.Customer.Email) }
        };

        //if (emarsysContact != null)
        //{
        //    if (!String.IsNullOrEmpty(email.Customer.FirstName))
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.FirstName, email.Customer.FirstName);
        //    }

        //    if (!String.IsNullOrEmpty(email.Customer.LastName))
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.LastName, email.Customer.LastName);
        //    }

        //    //================ Check Civility ==================
        //    //See https://help.emarsys.com/hc/en-us/articles/115004634749-Overview-Single-choice-fields-and-their-values
        //    if (!String.IsNullOrEmpty(email.Customer.Salutation))
        //    {
        //        if (email.Customer.Salutation == SalutationEmailEnum.Mr.ToString())
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.Salutation, "1"); //Mr
        //        }
        //        else if (email.Customer.Salutation == SalutationEmailEnum.Ms.ToString())
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.Salutation, "2"); //Ms
        //        }
        //        else if (email.Customer.Salutation == SalutationEmailEnum.Mx.ToString())
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.Salutation, "6"); //Mx
        //        }
        //    }

        //    try
        //    {
        //        if (!String.IsNullOrEmpty(email.Customer.Phone))
        //        {
        //            var customerPhoneNumber = phoneNumberUtil.Parse(email.Customer.Phone, _settings.CurrentValue.Country);

        //            if (IsMobilePhoneNumber(customerPhoneNumber, _settings.CurrentValue.Country))
        //            {
        //                fields.Add(_emarsysFieldsIds.CurrentValue.Mobile, FormatPhoneNumber(customerPhoneNumber, _settings.CurrentValue.Country));
        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        _logger.LogError(e, "Phone number Format Exception for email {email}: {phone}", email.Customer.Email, email.Customer.Phone);
        //    }

        //}

        if (!string.IsNullOrWhiteSpace(email.Customer.Email))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.Email, email.Customer.Email.ToLower().Trim());
        }



        //================ OPT-IN Management ==================
        // No opt-in for abandoned cart except technical ones

        //TODO : No Opt-in for partners orders

        fields.Add(emarsysFieldsIds.CurrentValue.OptIn, "1");
        fields.Add(emarsysFieldsIds.CurrentValue.MobileSmsOptIn, "1");

        //if (emarsysContact != null)
        //{
        //    if (_emarsysFieldsIds.CurrentValue.Country == "FR")
        //    {
        //        if (emarsysContact.OptInAbandonedCartFr == null && email.Customer.IsLogged)
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartFr, "1");
        //        }
        //    }

        //    if (_emarsysFieldsIds.CurrentValue.Country == "ES")
        //    {
        //        if (emarsysContact.OptInAbandonedCartEs == null && email.Customer.IsLogged)
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartEs, "1");
        //        }
        //    }

        //    if (_emarsysFieldsIds.CurrentValue.Country == "IT")
        //    {
        //        if (emarsysContact.OptInAbandonedCartIt == null && email.Customer.IsLogged)
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartIt, "1");
        //        }
        //    }

        //    if (_emarsysFieldsIds.CurrentValue.Country == "PT")
        //    {
        //        if (emarsysContact.OptInAbandonedCartPt == null && email.Customer.IsLogged)
        //        {
        //            fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartPt, "1");
        //        }
        //    }
        //}
        //else
        //{
        //    if (_settings.CurrentValue.Country == "FR" && email.Customer.IsLogged)
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartFr, "1");
        //    }

        //    if (_settings.CurrentValue.Country == "ES" && email.Customer.IsLogged)
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartEs, "1");
        //    }

        //    if (_settings.CurrentValue.Country == "IT" && email.Customer.IsLogged)
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartIt, "1");
        //    }

        //    if (_settings.CurrentValue.Country == "PT" && email.Customer.IsLogged)
        //    {
        //        fields.Add(_emarsysFieldsIds.CurrentValue.OptInAbandonedCartPt, "1");
        //    }
        //}

        //================ End of OPT-IN Management ==================

        if (emarsysContact == null)
        {
            if (settings.CurrentValue.Country == "FR")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedFr, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "ES")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedEs, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "IT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedIt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "PT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedPt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "DK")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedDk, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }
        }

        if (settings.CurrentValue.Country == "FR")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactFr, "1");
        }

        if (settings.CurrentValue.Country == "ES")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactEs, "1");
        }

        if (settings.CurrentValue.Country == "IT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactIt, "1");
        }

        if (settings.CurrentValue.Country == "PT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactPt, "1");
        }

        if (settings.CurrentValue.Country == "DK")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactDk, "1");
        }

        return fields;
    }

    private Dictionary<string, string> GetFields(CustomerEmail customer, Contact? emarsysContact)
    {
        Dictionary<string, string> fields = [];
        var phoneNumberUtil = PhoneNumberUtil.GetInstance();

        fields.Add(emarsysFieldsIds.CurrentValue.ContactId, GenerateUniqueContactId(customer.Email));

        if (!string.IsNullOrWhiteSpace(customer.FirstName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.FirstName, customer.FirstName);
        }

        if (!string.IsNullOrWhiteSpace(customer.LastName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.LastName, customer.LastName);
        }

        //================ Check Civility ==================
        //See https://help.emarsys.com/hc/en-us/articles/115004634749-Overview-Single-choice-fields-and-their-values
        if (!string.IsNullOrWhiteSpace(customer.Salutation))
        {
            if (customer.Salutation == SalutationEmailEnum.Mr.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "1"); //Mr
            }
            else if (customer.Salutation == SalutationEmailEnum.Ms.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "2"); //Ms
            }
            else if (customer.Salutation == SalutationEmailEnum.Mx.ToString())
            {
                fields.Add(emarsysFieldsIds.CurrentValue.Salutation, "6"); //Mx
            }
        }

        if (!string.IsNullOrWhiteSpace(customer.Email))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.Email, customer.Email.ToLower().Trim());
        }

        if (!string.IsNullOrWhiteSpace(customer.CompanyName))
        {
            fields.Add(emarsysFieldsIds.CurrentValue.CompanyName, customer.CompanyName);
        }

        //================ Customer Type ==================

        if (!string.IsNullOrWhiteSpace(customer.Type))
        {
            if (customer.Type == "B2B")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2B, "1");
            }
            else if (customer.Type == "B2F")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2F, "1");
            }
            else
            {
                fields.Add(emarsysFieldsIds.CurrentValue.B2C, "1");
            }
        }
        else
        {
            fields.Add(emarsysFieldsIds.CurrentValue.B2C, "1");
        }

        try
        {
            if (!string.IsNullOrWhiteSpace(customer.Phone))
            {
                Console.WriteLine(customer.Phone);
                var customerPhoneNumber = phoneNumberUtil.Parse(customer.Phone, settings.CurrentValue.Country);

                if (IsMobilePhoneNumber(customerPhoneNumber, settings.CurrentValue.Country))
                {
                    fields.Add(emarsysFieldsIds.CurrentValue.Mobile, FormatPhoneNumber(customerPhoneNumber, settings.CurrentValue.Country));
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Phone number Format Exception for email {email}: {phone}", customer.Email, customer.Phone);
        }


        if (emarsysContact == null)
        {
            if (settings.CurrentValue.Country == "FR")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedFr, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "ES")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedEs, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "IT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedIt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "PT")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedPt, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }

            if (settings.CurrentValue.Country == "DK")
            {
                fields.Add(emarsysFieldsIds.CurrentValue.CreatedDk, DateTime.UtcNow.ToString("yyyy-MM-dd"));
            }
        }

        if (settings.CurrentValue.Country == "FR")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactFr, "1");
        }

        if (settings.CurrentValue.Country == "ES")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactEs, "1");
        }

        if (settings.CurrentValue.Country == "IT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactIt, "1");
        }

        if (settings.CurrentValue.Country == "PT")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactPt, "1");
        }

        if (settings.CurrentValue.Country == "DK")
        {
            fields.Add(emarsysFieldsIds.CurrentValue.ContactDk, "1");
        }

        //Mandatory fields
        fields.Add(emarsysFieldsIds.CurrentValue.OptIn, "1");
        fields.Add(emarsysFieldsIds.CurrentValue.MobileSmsOptIn, "1");

        return fields;
    }

}

public interface IEmarsysContactService
{
    Task<Result<Contact, Error>> GetContact(string email);
    Task<Result<List<string>, Error>> CreateOrUpdateContactForOrderConfirmation(OrderEmail email, bool newsletterOptIn = false, bool smsOptIn = false);
    Task<Result<List<string>, Error>> CreateOrUpdateContactForAbandonnedCart(CartEmail email);
    Task<Result<List<string>, Error>> CreateOrUpdateContact(CustomerEmail customer);
    Task<Result<List<Contact>, Error>> GetContactsBatch(List<string> emails);
    Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<OrderEmail>> emails);
    Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<CartEmail>> emails);
    Task<Result<Dictionary<string, string?>, Error>> UpsertContactBatch(List<UpsertContactInfo<CustomerEmail>> emails);
}

public class UpsertContactInfo<T>
{
    public required string Email { get; set; }
    public Contact? Contact { get; set; }
    public required T EmailData { get; set; }
    public bool NewsletterOptIn { get; set; } = false;
    public bool SmsOptIn { get; set; } = false;
}