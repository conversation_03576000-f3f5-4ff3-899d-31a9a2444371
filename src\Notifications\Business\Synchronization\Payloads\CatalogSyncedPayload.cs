﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Notifications.Business.Synchronization.Payloads;
public class CatalogSyncedPayload : IPayload
{
    public string ProductNumber { get; set; }
    public DateTime LastSynchronized { get; set; }
    public string EventID { get; set; }
    public DateTime EventDate { get; set; }
}
