﻿using commercetools.Sdk.Api.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class PriceExtensionMethode
    {
        public static decimal GetPrice(this IPrice price , int qty = 1)
        {
            // if we are in price tiers mode --> Roses a la tige
            if(price.Tiers?.Count > 0 && qty > 1)
            {
                var priceUnitForQty = price.Tiers.Where(t => t.MinimumQuantity == qty).Select(t => t.Value.AmountToDecimal()).FirstOrDefault();
                 if(priceUnitForQty is default(decimal))
                    return price.Value.AmountToDecimal();
                return Math.Round(priceUnitForQty * qty , 2);

            }
            return price.Value.AmountToDecimal();
        }

        public static bool IsExpired(this IPrice price) => price.ValidUntil != null && price.ValidUntil < DateTime.Now;
    }
}
