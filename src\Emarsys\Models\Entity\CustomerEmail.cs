﻿using Swashbuckle.AspNetCore.Annotations;

namespace ITF.SharedLibraries.Emarsys.Models.Entity;

[SwaggerSchema(Required = new[] { "Email" })]
public class CustomerEmail
{
    public string FirstName { get; set; }
    public string LastName { get; set; }

    [SwaggerSchema("Customer's Civility")]
    public string Salutation { get; set; }
    public string Email { get; set; }

    [SwaggerSchema("Customer's Phone number in international format: FR = +***********, IT = +************,...")]
    public string Phone { get; set; }
    public string Password { get; set; }

    [SwaggerSchema("B2C, B2B or B2F")]
    public string Type { get; set; }

    [SwaggerSchema("If customer is B2B")]
    public string CompanyName { get; set; }

    [SwaggerSchema("The Customer has an active Interflora Plus subscription")]
    public bool IsInterfloraPlus { get; set; }

    [SwaggerSchema("The Customer is logged to his account")]
    public bool IsLogged { get; set; }
}
