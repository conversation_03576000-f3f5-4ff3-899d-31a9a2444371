﻿using ITF.Lib.Common.DomainDrivenDesign;
using ITF.Lib.Common.DomainDrivenDesign.Interfaces;
using ITF.SharedLibraries.EventSourcing.Interfaces;
using Marten;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.EventSourcing.Repository
{
    public class MartenRepository : IAggregateStoreRepository
    {
        private readonly IServiceScopeFactory _scopeFactory;

        public MartenRepository(IServiceScopeFactory scopeFactory)
        {
            _scopeFactory = scopeFactory;
        }

        public async Task<bool> Exists<T, TId>(TId aggregateId, CancellationToken ct) where TId : IValueId
        {
            using var scope = _scopeFactory.CreateScope();
            using var documentSession = scope.ServiceProvider.GetRequiredService<IDocumentSession>();
            var stream = GetStreamName<T, TId>(aggregateId);
            var streamState = await documentSession.Events.FetchStreamStateAsync(stream, ct);
            return !(streamState == null);
        }

        public async Task<T> Load<T, TId>(TId aggregateId, CancellationToken ct)
            where T : AggregateRoot<TId>
            where TId : IValueId
        {
            var events = new List<object>();

            if (aggregateId == null)
                throw new ArgumentNullException(nameof(aggregateId));

            using var scope = _scopeFactory.CreateScope();
            using var documentSession = scope.ServiceProvider.GetRequiredService<IDocumentSession>();
            var streamName = GetStreamName<T, TId>(aggregateId);
            var result = await documentSession.Events.FetchStreamAsync(streamName, token: ct);

            if (!result.Any())
                throw new InvalidOperationException($"Aggregate with id {aggregateId} of type {typeof(T)} was not found!");

            var aggregate = (T)Activator.CreateInstance(typeof(T), true);
            foreach (var @event in result)
            {
                events.Add(@event.Data);
            }

            aggregate.Load(events);
            return aggregate;
        }

        public async Task Save<T, TId>(T aggregate, CancellationToken ct)
            where T : AggregateRoot<TId>
            where TId : IValueId
        {
            if (aggregate == null)
                throw new ArgumentNullException(nameof(aggregate));

            var streamName = GetStreamName<T, TId>(aggregate);
            var changes = aggregate.GetChanges();

            using var scope = _scopeFactory.CreateScope();
            using var documentSession = scope.ServiceProvider.GetRequiredService<IDocumentSession>();

            // Be aware of : no manual serialization => what if the serialization fail (version in library assembly name)
            // or need a special serialized (CT) => stringify in a field
            documentSession.Events.Append(streamName, aggregate.GetEventVersion() + 2, changes);
            await documentSession.SaveChangesAsync(ct);
            aggregate.ClearChanges();
        }

        private static string GetStreamName<T, TId>(TId aggregateId)
            where TId : IValueId
            => $"{typeof(T).Name}-{aggregateId.AsString()}";

        private static string GetStreamName<T, TId>(T aggregate)
            where T : AggregateRoot<TId>
            where TId : IValueId
            => $"{typeof(T).Name}-{aggregate.Id}";
    }
}
