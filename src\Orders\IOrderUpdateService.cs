﻿using commercetools.Sdk.Api.Models.Orders;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Orders
{
    public interface IOrderUpdateService
    {
        Task<IOrder> RefuseOrder(string orderId, long orderVersion, string orderNumber);
        Task<IOrder> UpdateCustomField(string orderId, long orderVersion, string orderNumber, string customFieldName, object customFieldValue, bool retry);
        Task<IOrder> UpdateReadByExecutingFloristCustomField(string orderId, long orderVersion, string orderNumber, string executingFloristId, string transmitterFloristId);
        Task<IOrder> UpdateReadByExecutingFloristCustomFieldForced(string orderId, long orderVersion, string orderNumber, bool value);
    }
}