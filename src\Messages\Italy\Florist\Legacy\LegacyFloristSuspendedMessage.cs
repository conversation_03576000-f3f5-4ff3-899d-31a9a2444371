﻿using ITF.Lib.Common.Notifications.Messages;
using System;
using static ITF.SharedModels.Notifications.Business.Synchronization.Messages.Messages.V1;

namespace ITF.SharedModels.Messages.Italy.Florist.Legacy
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class LegacyFloristSuspendedMessage : BaseMessage<LegacyFloristSuspendedPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                    => Payload?.FloristIdentifier;

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

            }
        }
    }

    public class LegacyFloristSuspendedPayload : LegacyPayload, IEquatable<LegacyFloristSuspendedPayload>
    {
        public string FloristIdentifier { get; set; }
        public bool Suspended { get; set; }
        public string Reason { get; set; }

        public bool Equals(LegacyFloristSuspendedPayload parameter)
        {
            return (FloristIdentifier == parameter.FloristIdentifier &&
                Suspended == parameter.Suspended &&
                Reason == parameter.Reason
                );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as LegacyFloristSuspendedPayload);
        }

        public override int GetHashCode() => new
        {
            FloristIdentifier,
            Suspended,
            Reason
        }.GetHashCode();
    }
}
