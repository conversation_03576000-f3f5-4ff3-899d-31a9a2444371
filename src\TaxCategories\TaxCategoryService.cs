﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Models.ProductTypes;
using commercetools.Sdk.Api.Models.TaxCategories;
using commercetools.Sdk.Api.Serialization;
using commercetools.Sdk.Api.Extensions;
using IT.SharedLibraries.CT.ProductTypes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.TaxCategories
{
    public class TaxCategoryService : ITaxCategoryService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ProductTypeService> _logger;
        private readonly SerializerService _serializerService;
        private readonly string _projectKey;

        public TaxCategoryService(IClient commerceToolsClient, IConfiguration configuration, ILogger<ProductTypeService> logger, SerializerService serializerService)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _projectKey = _configuration.GetSection("Client:ProjectKey").Value;
        }
        public async Task<IList<ITaxCategory>> GetAll()
        {
            IList<ITaxCategory> caxCategories = null;
            try
            {
                var result = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .TaxCategories()
                    .Get()
                    .ExecuteAsync();
                caxCategories = result.Results;
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while reading TaxCategories, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while reading TaxCategories because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return caxCategories;
        }
    }
}
