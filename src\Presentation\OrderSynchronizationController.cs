using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Models.Orders;
using IT.Microservices.OrderReactor.Domain;
using IT.Microservices.OrderReactor.Infrastructure;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.Orders;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedLibraries.RAO;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static ITF.SharedModels.DataModels.Order.RaoOrderModel;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace IT.Microservices.OrderReactor.Presentation
{
    /// <summary>
    /// Controller for manually triggering order synchronization processes
    /// </summary>
    public class OrderSynchronizationController(IOrderUseCase orderUseCase, IOrderService orderService, ILogger<OrderSynchronizationController> logger, IRAOSupplierHttpService? RAOSupplierHttpService = null,ISlackAlertService? slackAlertService = null) : BaseController
    {
        
        // Constants for repeated string literals
        private const string MessageCannotBeNull = "Message cannot be null";
        private const string StatusSuccess = "Success";
        private const string StatusError = "Error";
        private const string InternalServerError = "Internal server error occurred";

        #region SEU Legacy Messages

        /// <summary>
        /// Process Legacy Order Created Message
        /// </summary>
        [HttpPost("legacy-order-created")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderCreated([FromBody] LegacyOrderCreatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderCreatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderCreatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderCreatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderCreatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Assigned Message
        /// </summary>
        [HttpPost("legacy-order-assigned")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderAssigned([FromBody] LegacyOrderAssignedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderAssignedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderAssignedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderAssignedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderAssignedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Cancelled Message
        /// </summary>
        [HttpPost("legacy-order-cancelled")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderCancelled([FromBody] LegacyOrderCancelledMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderCancelledMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderCancelledMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderCancelledMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderCancelledMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivery Time Updated Message
        /// </summary>
        [HttpPost("legacy-order-delivery-time-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveryTimeUpdated([FromBody] LegacyOrderDeliveryTimeUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveryTimeUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveryTimeUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveryTimeUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveryTimeUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivery Status Updated Message
        /// </summary>
        [HttpPost("legacy-order-delivery-status-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveryStatusUpdated([FromBody] LegacyOrderDeliveryStatusUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveryStatusUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveryStatusUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveryStatusUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveryStatusUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivery Cost Updated Message
        /// </summary>
        [HttpPost("legacy-order-delivery-cost-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveryCostUpdated([FromBody] LegacyOrderDeliveryCostUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveryCostUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveryCostUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveryCostUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveryCostUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivery Date Updated Message
        /// </summary>
        [HttpPost("legacy-order-delivery-date-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveryDateUpdated([FromBody] LegacyOrderDeliveryDateUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveryDateUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveryDateUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveryDateUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveryDateUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Card Message Updated Message
        /// </summary>
        [HttpPost("legacy-order-card-message-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderCardMessageUpdated([FromBody] LegacyOrderCardMessageUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderCardMessageUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderCardMessageUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderCardMessageUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderCardMessageUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivery Address Updated Message
        /// </summary>
        [HttpPost("legacy-order-delivery-address-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveryAddressUpdated([FromBody] LegacyOrderDeliveryAddressUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveryAddressUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveryAddressUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveryAddressUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveryAddressUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Notes Updated Message
        /// </summary>
        [HttpPost("legacy-order-notes-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderNotesUpdated([FromBody] LegacyOrderNotesUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderNotesUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderNotesUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderNotesUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderNotesUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Recipient Coordinates Updated Message
        /// </summary>
        [HttpPost("legacy-order-recipient-coordinates-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderRecipientCoordinatesUpdated([FromBody] LegacyOrderRecipientCoordinatesUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderRecipientCoordinatesUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderRecipientCoordinatesUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderRecipientCoordinatesUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderRecipientCoordinatesUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivered On Behalf Message
        /// </summary>
        [HttpPost("legacy-order-delivered-on-behalf")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDeliveredOnBehalf([FromBody] LegacyOrderDeliveredOnBehalfMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveredOnBehalfMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveredOnBehalfMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveredOnBehalfMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveredOnBehalfMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Accepted Message
        /// </summary>
        [HttpPost("legacy-order-accepted")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderAccepted([FromBody] LegacyOrderAcceptedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderAcceptedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderAcceptedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderAcceptedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderAcceptedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Rejected Message
        /// </summary>
        [HttpPost("legacy-order-rejected")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderRejected([FromBody] LegacyOrderRejectedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderRejectedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderRejectedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderRejectedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderRejectedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Delivered Message
        /// </summary>
        [HttpPost("legacy-order-delivered")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderDelivered([FromBody] LegacyOrderDeliveredMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderDeliveredMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderDeliveredMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderDeliveredMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderDeliveredMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Item Updated Message
        /// </summary>
        [HttpPost("legacy-order-item-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderItemUpdated([FromBody] LegacyOrderItemUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderItemUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderItemUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderItemUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderItemUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Item Executor Amount Updated Message
        /// </summary>
        [HttpPost("legacy-order-item-executor-amount-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderItemExecutorAmountUpdated([FromBody] LegacyOrderItemExecutorAmountUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderItemExecutorAmountUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderItemExecutorAmountUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderItemExecutorAmountUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderItemExecutorAmountUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Assignation Removed Message
        /// </summary>
        [HttpPost("legacy-order-assignation-removed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderAssignationRemoved([FromBody] LegacyOrderAssignationRemovedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderAssignationRemovedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderAssignationRemovedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderAssignationRemovedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderAssignationRemovedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Accepted On Behalf Message
        /// </summary>
        [HttpPost("legacy-order-accepted-on-behalf")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderAcceptedOnBehalf([FromBody] LegacyOrderAcceptedOnBehalfMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderAcceptedOnBehalfMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderAcceptedOnBehalfMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderAcceptedOnBehalfMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderAcceptedOnBehalfMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Rejected On Behalf Message
        /// </summary>
        [HttpPost("legacy-order-rejected-on-behalf")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderRejectedOnBehalf([FromBody] LegacyOrderRejectedOnBehalfMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderRejectedOnBehalfMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderRejectedOnBehalfMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderRejectedOnBehalfMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderRejectedOnBehalfMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Sent Message
        /// </summary>
        [HttpPost("legacy-order-sent")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderSent([FromBody] LegacyOrderSentMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderSentMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderSentMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderSentMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderSentMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Recipient Name Updated Message
        /// </summary>
        [HttpPost("legacy-order-recipient-name-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderRecipientNameUpdated([FromBody] LegacyOrderRecipientNameUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderRecipientNameUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderRecipientNameUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderRecipientNameUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderRecipientNameUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Legacy Order Recipient Phone Number Updated Message
        /// </summary>
        [HttpPost("legacy-order-recipient-phone-number-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLegacyOrderRecipientPhoneNumberUpdated([FromBody] LegacyOrderRecipientPhoneNumberUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null LegacyOrderRecipientPhoneNumberUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing LegacyOrderRecipientPhoneNumberUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "LegacyOrderRecipientPhoneNumberUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing LegacyOrderRecipientPhoneNumberUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        #endregion

        #region France RAO Legacy Messages

        /// <summary>
        /// Process Order Placed Message
        /// </summary>
        [HttpPost("order-placed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderPlaced([FromBody] OrderPlacedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderPlacedMessage");
                    await (slackAlertService?.SendErrorAlertAsync("Received null OrderPlacedMessage in ProcessOrderPlaced") ?? Task.CompletedTask);
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderPlacedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderPlacedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderPlacedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Assignment Message
        /// </summary>
        [HttpPost("order-assignment")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderAssignment([FromBody] OrderAssignmentMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderAssignmentMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderAssignmentMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderAssignmentMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderAssignmentMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Updated Message
        /// </summary>
        [HttpPost("order-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderUpdated([FromBody] OrderUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Management Status Message
        /// </summary>
        [HttpPost("order-management-status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderManagementStatus([FromBody] OrderManagementStatusMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderManagementStatusMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderManagementStatusMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderManagementStatusMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderManagementStatusMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Delivery Courier Updated Message
        /// </summary>
        [HttpPost("order-delivery-courier-updated")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderDeliveryCourierUpdated([FromBody] OrderDeliveryCourierUpdatedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderDeliveryCourierUpdatedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderDeliveryCourierUpdatedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderDeliveryCourierUpdatedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderDeliveryCourierUpdatedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Delivery Courier Reseted Message
        /// </summary>
        [HttpPost("order-delivery-courier-reseted")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderDeliveryCourierReseted([FromBody] OrderDeliveryCourierResetedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderDeliveryCourierResetedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderDeliveryCourierResetedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderDeliveryCourierResetedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderDeliveryCourierResetedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Order Delivery Courier Initialized Message
        /// </summary>
        [HttpPost("order-delivery-courier-initialized")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessOrderDeliveryCourierInitialized([FromBody] OrderDeliveryCourierInitializedMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null OrderDeliveryCourierInitializedMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing OrderDeliveryCourierInitializedMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "OrderDeliveryCourierInitializedMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing OrderDeliveryCourierInitializedMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        /// <summary>
        /// Process Invoice Message
        /// </summary>
        [HttpPost("invoice")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessInvoice([FromBody] InvoiceMessage message)
        {
            try
            {
                if (message == null)
                {
                    logger.LogWarning("Received null InvoiceMessage");
                    return BadRequest(MessageCannotBeNull);
                }

                logger.LogInformation("Processing InvoiceMessage via API endpoint");
                await orderUseCase.SychronizeProcess(message);
                return Ok(new { Status = StatusSuccess, Message = "InvoiceMessage processed successfully" });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing InvoiceMessage via API");
                return StatusCode(500, new { Status = StatusError, Message = InternalServerError });
            }
        }

        [SwaggerOperation(
        Summary = "Perform full fields sync Rao To Ct for an orderList",
        Description = "Perform full fields sync Rao To Ct for an orderList",
        OperationId = "UpdateOrderStatus")]
        [SwaggerResponse(200, "All order has been updated")]
        [SwaggerResponse(207, "Some order hasn't been updated")]
        [SwaggerResponse(400, "A parameter is invalid")]
        [SwaggerResponse(500, "An unknown error occured, please contact us")]
        [HttpPost("SyncRaoToCTOrders")]
        public async Task<IActionResult> SyncRaoToCTOrdersAsync(string[] orderNumbers)
        {
            logger.LogInformation("Received {method}: {cmd}", nameof(SyncRaoToCTOrdersAsync), orderNumbers.Serialize());

            var orderNumberNotUpdatedList = new List<string>();
            foreach (var orderNumber in orderNumbers ?? [])
            {
                try
                {
                    await ProcessSyncRaoToCTOrderAsync(orderNumber);
                }
                catch (Exception ex)
                {
                    logger.LogError($"Error Exception on  {nameof(SyncRaoToCTOrdersAsync)} -  {ex.ToString()}");
                    orderNumberNotUpdatedList.Add(orderNumber);
                }
            }
            if (orderNumberNotUpdatedList.Count > 0)
                return StatusCode(StatusCodes.Status207MultiStatus, orderNumberNotUpdatedList.Serialize());
            return Ok();

        }
        private async Task ProcessSyncRaoToCTOrderAsync(string orderNumber)
        {
            var raoOrderResponse = await RAOSupplierHttpService.GetOrder(orderNumber);
            string responseData = await raoOrderResponse.Content.ReadAsStringAsync();
            if (!raoOrderResponse.IsSuccessStatusCode)
                throw new Exception($"getRaoOrder failure : {(int)raoOrderResponse.StatusCode} {raoOrderResponse.StatusCode} :\n{responseData}");
            var raoOrder = responseData.Deserialize<RaoOrderModel>();

            var raoFileReferenceResponse = await RAOSupplierHttpService.GetFileReference("bdc", orderNumber, raoOrder.ExecutingFloristId);
            string responseFileReferenceData = await raoFileReferenceResponse.Content.ReadAsStringAsync();
            if (!raoFileReferenceResponse.IsSuccessStatusCode)
                throw new Exception($"getRaoFileReference failure : {(int)raoFileReferenceResponse.StatusCode} {raoFileReferenceResponse.StatusCode} :\n{responseFileReferenceData}");
            var raoFileReferenceList = responseFileReferenceData.Deserialize<List<FileReferenceRaoModel>>();
            var raoFileReference = raoFileReferenceList!.FirstOrDefault();

            string internalOrderId = "";
            GetFloristCounterPerDayResult result = await orderUseCase.GetFloristCounterPerDay(orderNumber, raoOrder.ExecutingFloristId);
            if (result.Counter >= 0)
            {
                result.Counter++;
                internalOrderId = result.OrderDeliveryDate.ToString("yyyyMMdd") + "-" + result.Counter.ToString();
            }
            else
                throw new Exception($"Can't get Florist counter for orderId : {orderNumber} and floristId : {raoOrder.ExecutingFloristId}");

            IOrder order = await orderService.GetByOrderNumber(orderNumber);
            var fieldsToUpdate = await orderService.GetDifferencesForUpdate(raoOrder.ToBaseOrderPayload(), order);

            if (!string.IsNullOrWhiteSpace(internalOrderId))
                fieldsToUpdate.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.InternalOrderId, internalOrderId));

            if (!string.IsNullOrWhiteSpace(raoFileReference.Url))
                fieldsToUpdate.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.FloristInvoiceUrl, raoFileReference.Url));

            // Log summary of differences found
            logger.LogInformation("Found {DifferencesCount} differences to update for order {OrderId}. Differences: {DifferenceKeys}",
                fieldsToUpdate.Count,
                orderNumber,
                string.Join(", ", fieldsToUpdate.Select(f => f.Key.ToString())));

            // Log detailed information about each difference
            foreach (var field in fieldsToUpdate)
            {
                orderUseCase.LogFieldDifference(field, order);
            }
            var res = await orderService.UpdateOrderFromRAOToCT(order, fieldsToUpdate);

            if (res.IsFailure)
                throw new Exception($"Error in HandleRAOLegacyOrderUpdate process. Failed to update orderNumber {orderNumber} Reason: {res.Error}");

            else
            {
                logger.LogInformation("HandleRAOLegacyOrderUpdate completed successfully for order {OrderId}", orderNumber);
           
                GlobalFloristOrderPerDayModel globalFloristOrderPerDay = new GlobalFloristOrderPerDayModel
                {
                    Counter = result.Counter,
                    FloristIdentifier = raoOrder.ExecutingFloristId ?? string.Empty,
                    DeliveryDate = result.OrderDeliveryDate,

                };
                globalFloristOrderPerDay.SetLastUpdate();
                globalFloristOrderPerDay.SetId();
            }

          

            

        }



        #endregion
    }
}
