﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Keycloak
{
    public class KeycloakSettings
    {
       
        public string LoginPath { get; set; }
        public string Url { get; set; }
        public string Realm { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string ResponseType { get; set; }
        public bool SaveTokens { get; set; }
        public TokenValidationParameters TokenValidationParameters { get; set; }
     
    }

    
}
