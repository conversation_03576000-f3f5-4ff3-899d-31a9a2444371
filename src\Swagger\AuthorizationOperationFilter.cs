﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Microsoft.FeatureManagement.Mvc;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ITF.SharedLibraries.Swagger
{
    public class AuthorizationOperationFilter : IOperationFilter
	{
        private readonly ILogger<AuthorizationOperationFilter> _logger;
        private readonly IFeatureManager? _featureManager;

        public AuthorizationOperationFilter(ILogger<AuthorizationOperationFilter> logger, IFeatureManager? featureManager = null)
        {
            _logger = logger;
            _featureManager = featureManager;
        }
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            try
            {
                var authorizations = new List<string>();
                var policy = new List<string>();

                if (context.ApiDescription.CustomAttributes().OfType<AllowAnonymousAttribute>().Any())
                    authorizations.Add("[Anonymous]");

                var authorizeAttributes = context.ApiDescription.CustomAttributes().OfType<AuthorizeAttribute>().ToList();
                if (authorizeAttributes.Any())
                {
                    var auth = GetAuthorize(authorizeAttributes);
                    if (!string.IsNullOrWhiteSpace(auth))
                        authorizations.Add(auth);

                    var roles = GetRoles(authorizeAttributes);
                    if (!string.IsNullOrWhiteSpace(roles))
                        authorizations.Add($"Roles: {roles}");

                    var policies = GetPolicies(authorizeAttributes);
                    if (!string.IsNullOrWhiteSpace(policies))
                        authorizations.Add($"Policies: {policies}");

                    if (authorizations.Any())
                        operation.Description += $"<div> Authorizations: {string.Join(" / ", authorizations)}</div>";
                }
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Fail to apply authentication filtering on action {action}", context?.ApiDescription?.ActionDescriptor?.DisplayName);
            }

            if(_featureManager != null)
            {
                var featureGateAttributes = context.MethodInfo
                .GetCustomAttributes(typeof(FeatureGateAttribute), true)
                .Cast<FeatureGateAttribute>().ToList();

                var controllerType = context.MethodInfo.DeclaringType;
                if (controllerType is not null)
                {
                    var controllerFeatureGateAttributes = controllerType.GetCustomAttributes(typeof(FeatureGateAttribute), true)
                        .Cast<FeatureGateAttribute>();
                    featureGateAttributes.AddRange(controllerFeatureGateAttributes);
                }

                if (featureGateAttributes.Count != 0)
                {
                    var enabledApi = true;

                    foreach (var featureGateAttribute in featureGateAttributes)
                    {
                        if (!enabledApi)
                        {
                            break;
                        }

                        foreach (var feature in featureGateAttribute.Features)
                        {
                            if (!_featureManager.IsEnabledAsync(feature).GetAwaiter().GetResult())
                            {
                                enabledApi = false;
                                break;
                            }
                        }
                    }

                    if (!enabledApi)
                    {
                        operation.Tags.Add(new OpenApiTag() { Name = "DisabledApi" });
                    }
                }
            }
		}

        private static string GetAuthorize(List<AuthorizeAttribute> authorizeAttributes)
        {
            foreach (var attribute in authorizeAttributes)
            {
                if (string.IsNullOrWhiteSpace(attribute.Roles) && string.IsNullOrWhiteSpace(attribute.Policy))
                    return "[Auth]";
            }
            return null;
        }

        private static string GetRoles(List<AuthorizeAttribute> authorizeAttributes)
        {
            var roles = new List<string>();
            foreach (var attribute in authorizeAttributes)
            {
                if (!string.IsNullOrWhiteSpace(attribute.Roles))
                {
                    if (attribute.Roles.Contains(','))
                        roles.AddRange(attribute.Roles.Split(",").ToList());
                    else
                        roles.Add(attribute.Roles);
                }
            }
            return roles.Any() ? $"[{string.Join(", ", roles)}]" : null;
        }

        private static string GetPolicies(List<AuthorizeAttribute> authorizeAttributes)
        {
            var policies = new List<string>();
            foreach (var attribute in authorizeAttributes)
            {
                if (!string.IsNullOrWhiteSpace(attribute.Policy))
                {
                    if (attribute.Policy.Contains(','))
                        policies.AddRange(attribute.Policy.Split(",").ToList());
                    else
                        policies.Add(attribute.Policy);
                }
            }
            return policies.Any() ? $"[{string.Join(", ", policies)}]" : null;
        }
    }
}
