﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedLibraries.Postgres.Repository.Dapper
{
    public interface IGenericDapperRepository
    {
        Task<IEnumerable<T>> GetAllAsync<T>(string tableName);
        Task DeleteRowAsync<Tid>(Tid id, string tableName);
        Task<T> GetAsync<T,Tid>(Tid id, string tableName);
        Task<int> SaveRangeAsync<T>(IEnumerable<T> list, string tableName);
        Task UpdateAsync<T>(T t, string tableName);
        Task InsertAsync<T>(T t, string tableName);
    }
}
