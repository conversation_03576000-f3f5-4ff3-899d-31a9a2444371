﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ITF.SharedLibraries.EnvironmentVariable;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Linq;
using Prometheus;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Routing;

namespace ITF.SharedLibraries.HealthCheck.Extensions
{
    public static class HealthCheckExtensions
    {
        public static IServiceCollection AddHealthChecksMiddleware(this IServiceCollection services)
        {
            var hc = new HealthCheckProvider();
            services.AddSingleton(hc);

            services.AddHealthChecks()
                .AddCheck("self", () => HealthCheckResult.Healthy()).ForwardToPrometheus()
                .AddCheck<HealthChecker>("InterfloraHC");

            return services;
        }

        public static IHealthChecksBuilder AddHealthChecksMiddlewareBuilder(this IServiceCollection services)
        {
            var hc = new HealthCheckProvider();
            services.AddSingleton(hc);

            return services.AddHealthChecks()
                .AddCheck("self", () => HealthCheckResult.Healthy()).ForwardToPrometheus()
                .AddCheck<HealthChecker>("InterfloraHC");
        }

        public static IHealthChecksBuilder AddMongoDbHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "MongoDb")
        {
            var configuration = config.Get<MongoDB.Configuration>(varEnv);
            return healthChecksBuilder.AddMongoDb(
                configuration.ConnectionString,
                "MongoDbHC",
                HealthStatus.Unhealthy,
                new string[] { "db", "nosql", "mongodb" });            
        }

        public static IHealthChecksBuilder AddEventStoreHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "EventStore")
        {
            var configuration = config.Get<EventSourcing.EventStore.Configuration>(varEnv);
            return healthChecksBuilder.AddEventStore(
                configuration.ConnectionString,
                "EventStoreHC",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "db", "eventsourcing", "eventstore" });
        }

        public static IHealthChecksBuilder AddPostgresHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "Postgres")
        {
            var configuration = config.Get<Postgres.Configuration>(varEnv);
            return healthChecksBuilder.AddNpgSql(
                configuration.postgres,
                name: "PostgresHC",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "db", "sql", "postgres" });
        }

        public static IHealthChecksBuilder AddRabbitMqHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "RabbitMQ")
        {
            var configuration = config.Get<RabbitMQ.Configuration>(varEnv);
            return healthChecksBuilder.AddRabbitMQ(
                $"amqp://{configuration.UserName}:{configuration.Password}@{configuration.HostName}:{configuration.Port}{configuration.VHost}",
                name:"RabbitMQHC",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "broker", "amqp", "rabbitmq" });
        }

        public static IHealthChecksBuilder AddElasticSearchHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "ElasticSearch")
        {
            var configuration = config.Get<ElasticSearch.Configuration>(varEnv);
            return healthChecksBuilder.AddElasticsearch(
                configuration.Nodes.FirstOrDefault()?.NodeUrl,
                name: "ElasticSearchHC",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "searchengine", "nosql", "elasticsearch" });
        }

        public static IHealthChecksBuilder AddKafkaHC(this IHealthChecksBuilder healthChecksBuilder, IConfiguration config, string varEnv = "Kafka")
        {
            var configuration = config.Get<Kafka.Configuration>(varEnv);
            return healthChecksBuilder.AddKafka(
                new Confluent.Kafka.ProducerConfig { BootstrapServers = configuration.BootStrapServers },
                name: "KafkaHC",
                failureStatus: HealthStatus.Unhealthy,
                tags: new string[] { "streaming", "nosql", "kafka" });
        }

        public static IEndpointConventionBuilder UseHealthChecks(this IEndpointRouteBuilder endpointRouteBuilder, string url = "/health")
        {
            return endpointRouteBuilder.MapHealthChecks(url, new HealthCheckOptions
            {
                Predicate = r => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
        }
    }
}
