﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Notifications.Business.IntegrationEvents.Payloads.GFS;

namespace ITF.SharedModels.Notifications.Business.IntegrationEvents.Messages.GFS
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class BackchargeMessage : BaseMessage<BackchargePayload>, IMessageKey
            {
                public string GetMessageKey()
                    => Payload?.FloristgateNumber.ToString();
            }
        }
    }
}
